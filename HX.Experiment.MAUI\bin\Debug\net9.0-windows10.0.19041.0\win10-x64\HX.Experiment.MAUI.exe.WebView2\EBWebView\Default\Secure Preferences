{"extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.109\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.109\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "protection": {"macs": {"browser": {"show_home_button": "DF64759C1282A2EBE046786CB439A8E9F7FCCCC2E106BD6CD6CEC7BDCBD1222C"}, "default_search_provider_data": {"template_url_data": "56C41BC62781B3CACC68110638D08DA5E7641209B492CC134CECBCEB951FE219"}, "edge": {"services": {"account_id": "66E2C101DF8F65C401E338868D47621B9A7E7A925C36D04E77A751326FC58924", "last_username": "000EC24FAA42B8A61A37A0375EEA64C992E1081F33D644CD31F5F012D1A3990C"}}, "enterprise_signin": {"policy_recovery_token": "496883B1B5654B0C7A55A18C556095D6E23B0AB99769857E62D373BC953B725E"}, "extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": "C7702AFAFEF07051A056B781930B77BB571B2921C5A3478E879E43D4B71851D7", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "0480B6D989B27731B2156C3F771AAB7D42D23FBD3EFED671E2A89B7EB9D03698"}, "ui": {"developer_mode": "E219476CE3D9F4CC572C0805709B72737D4BE793D7CF3EC62AA58BB5D4E21218"}}, "google": {"services": {"last_signed_in_username": "526F512533476FB5DD29A501FC93528CD3E73760323FF750D262093D4ECAE0CF"}}, "homepage": "F2CD17872093D30A9CFD319A6B03EBE824CB65586FB44488D3EC1013B2553A43", "homepage_is_newtabpage": "F23F828C4941F2AEF7F05426040864B92FE1B91A9947906E3BF2CC84407FEF87", "media": {"cdm": {"origin_data": "4F0AD8DAB885F1CB01511A312F1915EA0FFCE863535F9F1199B622EE5B9ED926"}, "storage_id_salt": "9A7661958B7E0BF230F6C70D643E488DB796DA08E3EF8F5844459F5E2E165922"}, "pinned_tabs": "4CBEE65A5978E5DD713E15AE0B6FB27271FE636DEB059103E7E990B1773124E1", "prefs": {"preference_reset_time": "72CBB8474E1E964C16920E24EE09DA0AFC1AB757F92EF66C828873E9501430D4"}, "safebrowsing": {"incidents_sent": "AB8DDEC287CEC33EA9CD71ACA29FAFC572370C2E158026E3E136CC4C0006B5FF"}, "search_provider_overrides": "78ABEA46C24922D4C9ED27189BD9F78C01B0BAFF6C690717338950D938E379FB", "session": {"restore_on_startup": "21317004D861EDBBF80FACD8407CA90AA1DF459FEA1D4BA02A13E7CDE7C644E2", "startup_urls": "3E5206128ED9FBF52383EF932D235535820BA29737FFD34EF0A6518A247EE275"}}, "super_mac": "2280CF1D334757EA5FEEF39C0C5335C89F34179D48C69A0596678D2AA48B476C"}}