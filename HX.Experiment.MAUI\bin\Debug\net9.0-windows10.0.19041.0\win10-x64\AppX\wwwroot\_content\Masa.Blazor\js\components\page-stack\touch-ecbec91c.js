import{a as t}from"../../chunks/tslib.es6-68144fbe.js";import{u as e}from"../../chunks/touch-5a32c5ea.js";import{a as n}from"../../chunks/helper-6d386307.js";function o(o,s,r,c=-1){const a=n(o);if(!a)return;const m=n(c>-1?"[page-stack-id='"+c+"'] .m-page-stack-item":".m-page-stack");window.addEventListener("touchstart",M,{passive:!0}),window.addEventListener("touchmove",P,{passive:!1}),window.addEventListener("touchend",T,{passive:!0});const d=["left","right"].includes(r.position),{addMovement:u,endTouch:l,getVelocity:h}=e();let p,f=!1,v=!1,g=0,y=0;function E(t,e){return("left"===r.position?t:"right"===r.position?document.documentElement.clientWidth-t:i())-(e?document.documentElement.clientWidth:0)}function w(t,e=!0){const n="left"===r.position?(t-y)/document.documentElement.clientWidth:"right"===r.position?(document.documentElement.clientWidth-t-y)/document.documentElement.clientWidth:i();return e?Math.max(0,Math.min(1,n)):n}function k(t){const e=t.target.closest(".m-page-stack-item");return e&&e.parentElement===a}function M(t){if(!k(t))return;const e=t.changedTouches[0].clientX,n=t.changedTouches[0].clientY,o="left"===r.position?e<25:"right"===r.position?e>document.documentElement.clientWidth-25:i(),s=r.isActive&&("left"===r.position?e<document.documentElement.clientWidth:"right"===r.position?e>0:i());(o||s||r.isActive)&&(p=[e,n],y=E(d?e:n,r.isActive),g=w(d?e:n),f=y>-20&&y<80,l(t),u(t))}function P(e){return t(this,void 0,void 0,(function*(){if(!k(e))return;const t=e.changedTouches[0].clientX,n=e.changedTouches[0].clientY;if(f){if(!e.cancelable)return void(f=!1);const o=Math.abs(t-p[0]),i=Math.abs(n-p[1]);(d?o>i&&o>3:i>o&&i>3)?(v=!0,f=!1):(d?i:o)>3&&(f=!1)}if(!v)return;e.preventDefault(),u(e);const o=w(d?t:n,!1);g=Math.max(0,Math.min(1,o)),o>1?y=E(d?t:n,!0):o<0&&(y=E(d?t:n,!1)),W()}))}function T(t){if(!k(t))return;if(f=!1,!v)return;u(t),v=!1;const e=h(t.changedTouches[0].identifier),n=Math.abs(e.x),o=Math.abs(e.y),c=d?n>o&&n>400:o>n&&o>3;r.isActive=c?e.direction===({left:"right",right:"left",top:"down",bottom:"up"}[r.position]||i()):g>.5,W(),setTimeout((()=>s.invokeMethodAsync("TouchEnd",r.isActive)),200)}const W=()=>{if(v){const t="left"===r.position?`translateX(calc(-100% + ${g*document.documentElement.clientWidth}px))`:"right"===r.position?`translateX(calc(100% - ${g*document.documentElement.clientWidth}px))`:i();a.style.setProperty("transform",t),a.style.setProperty("transition","none"),m&&(m.style.setProperty("--m-page-stack-item-progress",`${g.toFixed(2)}`),m.style.setProperty("transition","none"))}else r.isActive?a.style.removeProperty("transform"):a.style.setProperty("transform","translateX(100%)"),a.style.removeProperty("transition"),m&&(m.style.setProperty("--m-page-stack-item-progress",r.isActive?"1":"0"),m.style.removeProperty("transition"),setTimeout((()=>{m.style.removeProperty("--m-page-stack-item-progress")}),300))};return{syncState:t=>{r=t},dispose:()=>{s.dispose(),window.removeEventListener("touchstart",M),window.removeEventListener("touchmove",P),window.removeEventListener("touchend",T)}}}function i(){throw new Error}export{o as useTouch};
//# sourceMappingURL=touch-ecbec91c.js.map
