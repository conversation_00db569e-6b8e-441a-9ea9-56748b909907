﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Machine>HX-SC-02</Machine>
    <WindowsUser>Liao</WindowsUser>
    <TargetPlatformIdentifier>Windows</TargetPlatformIdentifier>
    <TargetOsVersion>10.0</TargetOsVersion>
    <TargetOsDescription>Windows 10.0</TargetOsDescription>
    <SolutionConfiguration>Debug|AnyCPU</SolutionConfiguration>
    <PackageArchitecture>x64</PackageArchitecture>
    <PackageIdentityName>com.companyname.hx.experiment.maui</PackageIdentityName>
    <PackageIdentityPublisher>CN=User Name</PackageIdentityPublisher>
    <IntermediateOutputPath>D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\</IntermediateOutputPath>
    <RemoteDeploymentType>CopyToDevice</RemoteDeploymentType>
    <PackageRegistrationPath></PackageRegistrationPath>
    <RemoveNonLayoutFiles>false</RemoveNonLayoutFiles>
    <DeployOptionalPackages>false</DeployOptionalPackages>
    <WindowsSdkPath>C:\Program Files %28x86%29\Windows Kits\10\</WindowsSdkPath>
    <LayoutDir>D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\bin\Debug\net9.0-windows10.0.19041.0\win10-x64\AppX</LayoutDir>
  </PropertyGroup>
  <ItemGroup>
    <AppXManifest Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\bin\Debug\net9.0-windows10.0.19041.0\win10-x64\AppxManifest.xml">
      <PackagePath>AppxManifest.xml</PackagePath>
      <ReRegisterAppIfChanged>true</ReRegisterAppIfChanged>
    </AppXManifest>
  </ItemGroup>
  <ItemGroup>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\bin\Debug\net9.0-windows10.0.19041.0\win10-x64\resources.pri">
      <PackagePath>resources.pri</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\bin\Debug\net9.0-windows10.0.19041.0\win10-x64\HX.Experiment.MAUI.runtimeconfig.json">
      <PackagePath>HX.Experiment.MAUI.runtimeconfig.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\bin\Debug\net9.0-windows10.0.19041.0\win10-x64\HX.Experiment.MAUI.dll">
      <PackagePath>HX.Experiment.MAUI.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\apphost.exe">
      <PackagePath>HX.Experiment.MAUI.exe</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\Resources\Raw\AboutAssets.txt">
      <PackagePath>AboutAssets.txt</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\1xpyc92csh-2jzru2q90w.gz">
      <PackagePath>wwwroot\_content\AntDesign\js\ant-design-blazor.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\1zsqth3rcz-x67401ppqt.gz">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.compact.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\34blflduah-vhh2jufqs3.gz">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.aliyun.min.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\3pkgqoc23x-4e7su8ls4e.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\transition\index-339f8848.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\3v9sscxtw3-apt2gw0td9.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\window\touch-f9d2ba92.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\4ay3ztng19-q06nhyzej9.gz">
      <PackagePath>wwwroot\images\check-circle-outline.svg.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\55df3tpy1w-ta6451bhu3.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\drawflow-proxy.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\5ozbnmqx4c-less8k5dp3.gz">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\6q525j3tlr-less8k5dp3.gz">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.aliyun.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\7ftge31yl6-fipknvnpsp.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\css\masa-blazor.min.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\8286u7uofv-61n19gt1b8.gz">
      <PackagePath>wwwroot\favicon.ico.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\96ra3xu8vv-6tqei36j6p.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\baidumap.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\98s27ux9m1-n1s3lxfdbi.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\chunks\tslib.es6-68144fbe.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\9prptnwetl-islnrq0c31.gz">
      <PackagePath>wwwroot\_content\AntDesign\js\ant-design-blazor.js.map.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\b77ayb31m4-yvkv6cxmvi.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\chunks\index-cef005e4.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\cehasc43bm-5vcnc68aiq.gz">
      <PackagePath>wwwroot\HX.Experiment.MAUI.styles.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\gart0pz622-l7rw681l30.gz">
      <PackagePath>wwwroot\index.html.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\gntkt48ehx-hmh33oi7ee.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\chunks\helper-6d386307.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\hphvn5lkuo-6jrkf04sja.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\sortable.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\hve6ogtdoi-cy2ms3wvwg.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\mixins\activatable\index-82cb7376.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\j1jr3esqjc-hrfj977lv2.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\mixins\intersect\index-f360c115.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\j9ep1wbucs-b97pdmzs4h.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\scroll-to-target\index-1c14c8ac.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\jbtdqpyjb0-pv848xjna4.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\mixins\resize\index-07a0c3f6.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\jsex7tijp6-suojt78vcy.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\page-stack\touch-ecbec91c.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\m9sr6dry09-2mtw4tr229.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\vditor\vditor-helper.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\1xpyc92csh-2jzru2q90w.br">
      <PackagePath>wwwroot\_content\AntDesign\js\ant-design-blazor.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\1zsqth3rcz-x67401ppqt.br">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.compact.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\34blflduah-vhh2jufqs3.br">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.aliyun.min.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\3pkgqoc23x-4e7su8ls4e.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\transition\index-339f8848.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\3v9sscxtw3-apt2gw0td9.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\window\touch-f9d2ba92.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\4ay3ztng19-q06nhyzej9.br">
      <PackagePath>wwwroot\images\check-circle-outline.svg.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\55df3tpy1w-ta6451bhu3.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\drawflow-proxy.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\5ozbnmqx4c-less8k5dp3.br">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\6q525j3tlr-less8k5dp3.br">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.aliyun.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\7ftge31yl6-fipknvnpsp.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\css\masa-blazor.min.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\8286u7uofv-61n19gt1b8.br">
      <PackagePath>wwwroot\favicon.ico.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\96ra3xu8vv-6tqei36j6p.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\baidumap.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\98s27ux9m1-n1s3lxfdbi.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\chunks\tslib.es6-68144fbe.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\9prptnwetl-islnrq0c31.br">
      <PackagePath>wwwroot\_content\AntDesign\js\ant-design-blazor.js.map.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\b77ayb31m4-yvkv6cxmvi.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\chunks\index-cef005e4.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\cehasc43bm-5vcnc68aiq.br">
      <PackagePath>wwwroot\HX.Experiment.MAUI.styles.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\gart0pz622-l7rw681l30.br">
      <PackagePath>wwwroot\index.html.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\gntkt48ehx-hmh33oi7ee.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\chunks\helper-6d386307.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\hphvn5lkuo-6jrkf04sja.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\sortable.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\hve6ogtdoi-cy2ms3wvwg.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\mixins\activatable\index-82cb7376.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\j1jr3esqjc-hrfj977lv2.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\mixins\intersect\index-f360c115.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\j9ep1wbucs-b97pdmzs4h.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\scroll-to-target\index-1c14c8ac.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\jbtdqpyjb0-pv848xjna4.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\mixins\resize\index-07a0c3f6.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\jsex7tijp6-suojt78vcy.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\page-stack\touch-ecbec91c.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\m9sr6dry09-2mtw4tr229.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\vditor\vditor-helper.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\qwz8zeekkt-koqihchfaq.br">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.variable.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\rxxrt4efv6-xex8mncx4b.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\Presets\EllipsisText\EllipsisText.razor.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\s2vzjyofgz-pvc8ota2an.br">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.variable.min.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\s682nmenk5-hoaxe64hrl.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\masa-blazor.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\s9f7hgutfm-uyjj58hnqg.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\echarts.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\sqjvzvz5ns-0i9c2pqcbz.br">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.dark.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\srs9i3hsfa-u9u24q7mqc.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\monaco-editor.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\tzep23ns6m-ggo88y3uma.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\chunks\EventType-63cda6c3.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\u85skt7njs-c85iknmnh6.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\chunks\touch-5a32c5ea.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\vivduzjdd9-vhh2jufqs3.br">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.min.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\vl9avoc1ur-ftbbm70hxr.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\page-stack\index-3d21987e.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\vyboxlqsxg-82f7tjxk3h.br">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.compact.min.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\vz9ebaqy17-umtkqu2ib7.br">
      <PackagePath>wwwroot\css\app.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\xi6ba9wok3-y1br3qhvs0.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\input\index-b92d32d0.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\xp8j0y02uu-4x1zod59k2.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\mixins\outside-click\index-47d0ce8d.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\y33gtmb7m2-3eyeiubupr.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\navigation-drawer\touch-47e3a6be.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\ypalxvgq99-466423quza.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\quill\quill-helper.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\ypc3d9zz7v-ex5yhgml6j.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\overlay\scroll-strategy-dc362cab.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\zb5s6wfjbh-l551dbsw1z.br">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.dark.min.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\publish\zp1f6whp0t-oye2tnu4h3.br">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\manifest.json.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\qwz8zeekkt-koqihchfaq.gz">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.variable.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\rxxrt4efv6-xex8mncx4b.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\Presets\EllipsisText\EllipsisText.razor.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\s2vzjyofgz-pvc8ota2an.gz">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.variable.min.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\s682nmenk5-hoaxe64hrl.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\masa-blazor.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\s9f7hgutfm-uyjj58hnqg.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\echarts.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\sqjvzvz5ns-0i9c2pqcbz.gz">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.dark.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\srs9i3hsfa-u9u24q7mqc.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\monaco-editor.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\tzep23ns6m-ggo88y3uma.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\chunks\EventType-63cda6c3.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\u85skt7njs-c85iknmnh6.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\chunks\touch-5a32c5ea.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\vivduzjdd9-vhh2jufqs3.gz">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.min.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\vl9avoc1ur-ftbbm70hxr.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\page-stack\index-3d21987e.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\vyboxlqsxg-82f7tjxk3h.gz">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.compact.min.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\vz9ebaqy17-umtkqu2ib7.gz">
      <PackagePath>wwwroot\css\app.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\xi6ba9wok3-y1br3qhvs0.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\input\index-b92d32d0.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\xp8j0y02uu-4x1zod59k2.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\mixins\outside-click\index-47d0ce8d.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\y33gtmb7m2-3eyeiubupr.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\navigation-drawer\touch-47e3a6be.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\ypalxvgq99-466423quza.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\quill\quill-helper.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\ypc3d9zz7v-ex5yhgml6j.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\overlay\scroll-strategy-dc362cab.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\zb5s6wfjbh-l551dbsw1z.gz">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.dark.min.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\compressed\zp1f6whp0t-oye2tnu4h3.gz">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\manifest.json.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\check-bold.png">
      <PackagePath>wwwroot\images\check-bold.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\emotion.jpg">
      <PackagePath>wwwroot\images\emotion.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\head.png">
      <PackagePath>wwwroot\images\head.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\head\huanzhe.png">
      <PackagePath>wwwroot\images\head\huanzhe.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\head\kaohejiance.png">
      <PackagePath>wwwroot\images\head\kaohejiance.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\head\shebeixinxi.png">
      <PackagePath>wwwroot\images\head\shebeixinxi.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\head\shuju.png">
      <PackagePath>wwwroot\images\head\shuju.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\head\xitong.png">
      <PackagePath>wwwroot\images\head\xitong.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\head\zhanghao.png">
      <PackagePath>wwwroot\images\head\zhanghao.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\icon\add.png">
      <PackagePath>wwwroot\images\icon\add.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\icon\electricity-full.png">
      <PackagePath>wwwroot\images\icon\electricity-full.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\icon\free_fill.png">
      <PackagePath>wwwroot\images\icon\free_fill.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\icon\is_charge.png">
      <PackagePath>wwwroot\images\icon\is_charge.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\icon\jiazaizhong.png">
      <PackagePath>wwwroot\images\icon\jiazaizhong.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\icon\jinggao.png">
      <PackagePath>wwwroot\images\icon\jinggao.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\icon\lazy.png">
      <PackagePath>wwwroot\images\icon\lazy.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\icon\lixian%402x.png">
      <PackagePath>wwwroot\images\icon\lixian%402x.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\icon\loading.png">
      <PackagePath>wwwroot\images\icon\loading.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\icon\play.png">
      <PackagePath>wwwroot\images\icon\play.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\icon\shalou.png">
      <PackagePath>wwwroot\images\icon\shalou.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\icon\stack.png">
      <PackagePath>wwwroot\images\icon\stack.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\icon\today.png">
      <PackagePath>wwwroot\images\icon\today.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\icon\wifi%402x.png">
      <PackagePath>wwwroot\images\icon\wifi%402x.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\icon\yiwancheng.png">
      <PackagePath>wwwroot\images\icon\yiwancheng.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\large_button_back.png">
      <PackagePath>wwwroot\images\large_button_back.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\layout_header.png">
      <PackagePath>wwwroot\images\layout_header.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\login_bg.png">
      <PackagePath>wwwroot\images\login_bg.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\logo.png">
      <PackagePath>wwwroot\images\logo.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\logo_only.png">
      <PackagePath>wwwroot\images\logo_only.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\logo_white.png">
      <PackagePath>wwwroot\images\logo_white.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\main_bg.png">
      <PackagePath>wwwroot\images\main_bg.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\mima.png">
      <PackagePath>wwwroot\images\mima.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\zhanghao.png">
      <PackagePath>wwwroot\images\zhanghao.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Shared\obj\Debug\net9.0\compressed\61mm80u2zs-jbejp81bb0.gz">
      <PackagePath>wwwroot\_content\HX.Experiment.Shared\css\materialdesign\v7.1.96\css\materialdesignicons.min.css.map.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Shared\obj\Debug\net9.0\compressed\la6eg0xiph-rr2qxwjego.gz">
      <PackagePath>wwwroot\_content\HX.Experiment.Shared\HX.Experiment.Shared.rr2qxwjego.bundle.scp.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Shared\obj\Debug\net9.0\compressed\publish\61mm80u2zs-jbejp81bb0.br">
      <PackagePath>wwwroot\_content\HX.Experiment.Shared\css\materialdesign\v7.1.96\css\materialdesignicons.min.css.map.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Shared\obj\Debug\net9.0\compressed\publish\la6eg0xiph-rr2qxwjego.br">
      <PackagePath>wwwroot\_content\HX.Experiment.Shared\HX.Experiment.Shared.rr2qxwjego.bundle.scp.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Shared\obj\Debug\net9.0\compressed\publish\u8ws6zb8n4-e7uwgz2urb.br">
      <PackagePath>wwwroot\_content\HX.Experiment.Shared\css\materialdesign\v7.1.96\css\materialdesignicons.min.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Shared\obj\Debug\net9.0\compressed\u8ws6zb8n4-e7uwgz2urb.gz">
      <PackagePath>wwwroot\_content\HX.Experiment.Shared\css\materialdesign\v7.1.96\css\materialdesignicons.min.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Shared\wwwroot\css\materialdesign\v7.1.96\fonts\materialdesignicons-webfont.eot">
      <PackagePath>wwwroot\_content\HX.Experiment.Shared\css\materialdesign\v7.1.96\fonts\materialdesignicons-webfont.eot</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Shared\wwwroot\css\materialdesign\v7.1.96\fonts\materialdesignicons-webfont.ttf">
      <PackagePath>wwwroot\_content\HX.Experiment.Shared\css\materialdesign\v7.1.96\fonts\materialdesignicons-webfont.ttf</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Shared\wwwroot\css\materialdesign\v7.1.96\fonts\materialdesignicons-webfont.woff">
      <PackagePath>wwwroot\_content\HX.Experiment.Shared\css\materialdesign\v7.1.96\fonts\materialdesignicons-webfont.woff</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Shared\wwwroot\css\materialdesign\v7.1.96\fonts\materialdesignicons-webfont.woff2">
      <PackagePath>wwwroot\_content\HX.Experiment.Shared\css\materialdesign\v7.1.96\fonts\materialdesignicons-webfont.woff2</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\UFU.IoT.HRV\UFU.IoT.Shared\obj\Debug\net9.0\compressed\eqbp3gewar-rsp46tpbl7.gz">
      <PackagePath>wwwroot\_content\UFU.IoT.Shared\UFU.IoT.Shared.rsp46tpbl7.bundle.scp.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\UFU.IoT.HRV\UFU.IoT.Shared\obj\Debug\net9.0\compressed\publish\eqbp3gewar-rsp46tpbl7.br">
      <PackagePath>wwwroot\_content\UFU.IoT.Shared\UFU.IoT.Shared.rsp46tpbl7.bundle.scp.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\0mos7799j3-c3ya2mr0el.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\js\utils.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\35sh04k5ma-l4mf1jwdqk.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\font\iconfont.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\3fx309d257-h492093d3n.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\css\base-list.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\40mec64m37-hyipwchtko.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\UFU.CoreFX.Shared.hyipwchtko.bundle.scp.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\40yrg7bogm-3vfiobr2kw.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\font\iconfont.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\4v958o3y3k-zh2nsokffv.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\alifont\core\iconfont.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\5cooqgomdu-ze809w2t8w.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\demo.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\5mj0qi4qip-ze809w2t8w.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\alifont\core\demo.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\9ie6j0em2n-12dt1be4xp.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\iconfont.svg.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\9jednrsq6s-rliuuggbbg.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\alifont\core\demo_index.html.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\cb39xfuuyh-a6epnovc5j.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\css\base.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\dyrju7011d-zynzbl9kb6.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\demo_index.html.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\g35nvzgiu6-x0nn8ia2u9.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\font\demo_index.html.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\hotrbhbkko-favli1t8ef.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\images\up.svg.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\j36os3pagl-c1zyc2x1op.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\index.html.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\kisc1ekbgr-51drh6b8ti.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\js\cookieStore.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\lfzdjiwobj-tdvqggcqbj.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\images\sort.svg.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\m6ehu28fb9-k8yj7801q0.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\font\iconfont.json.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\mt2vjdnzay-ze809w2t8w.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\font\demo.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\n7re1c6bct-ogew5pzpvg.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\css\app.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\npl0c2xe4d-3t1al3jwu0.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\iconfont.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\pijuesgzmg-lm8e0mto1e.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\css\common.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\ps18sxdgai-oof07czp3s.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\alifont\core\iconfont.json.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\0mos7799j3-c3ya2mr0el.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\js\utils.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\35sh04k5ma-l4mf1jwdqk.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\font\iconfont.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\3fx309d257-h492093d3n.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\css\base-list.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\40mec64m37-hyipwchtko.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\UFU.CoreFX.Shared.hyipwchtko.bundle.scp.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\40yrg7bogm-3vfiobr2kw.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\font\iconfont.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\4v958o3y3k-zh2nsokffv.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\alifont\core\iconfont.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\5cooqgomdu-ze809w2t8w.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\demo.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\5mj0qi4qip-ze809w2t8w.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\alifont\core\demo.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\9ie6j0em2n-12dt1be4xp.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\iconfont.svg.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\9jednrsq6s-rliuuggbbg.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\alifont\core\demo_index.html.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\cb39xfuuyh-a6epnovc5j.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\css\base.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\dyrju7011d-zynzbl9kb6.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\demo_index.html.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\g35nvzgiu6-x0nn8ia2u9.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\font\demo_index.html.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\hotrbhbkko-favli1t8ef.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\images\up.svg.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\j36os3pagl-c1zyc2x1op.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\index.html.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\kisc1ekbgr-51drh6b8ti.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\js\cookieStore.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\lfzdjiwobj-tdvqggcqbj.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\images\sort.svg.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\m6ehu28fb9-k8yj7801q0.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\font\iconfont.json.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\mt2vjdnzay-ze809w2t8w.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\font\demo.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\n7re1c6bct-ogew5pzpvg.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\css\app.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\npl0c2xe4d-3t1al3jwu0.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\iconfont.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\pijuesgzmg-lm8e0mto1e.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\css\common.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\ps18sxdgai-oof07czp3s.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\alifont\core\iconfont.json.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\r7ng82yfo3-hs9fnbdhiu.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\images\down.svg.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\ra1o37owsv-nwb8ew5at4.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\alifont\core\iconfont.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\ufkbhbnxu8-lvithvnkg2.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\iconfont.json.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\ybcl9gntyv-kt2yn3i7de.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\js\createCanvas.js.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\publish\zuwuzgm3qx-rcd6yxga24.br">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\iconfont.css.br</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\r7ng82yfo3-hs9fnbdhiu.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\images\down.svg.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\ra1o37owsv-nwb8ew5at4.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\alifont\core\iconfont.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\ufkbhbnxu8-lvithvnkg2.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\iconfont.json.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\ybcl9gntyv-kt2yn3i7de.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\js\createCanvas.js.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\compressed\zuwuzgm3qx-rcd6yxga24.gz">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\iconfont.css.gz</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\font\iconfont.ttf">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\font\iconfont.ttf</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\font\iconfont.woff">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\font\iconfont.woff</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\font\iconfont.woff2">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\font\iconfont.woff2</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\images\default.png">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\images\default.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\alifont\core\iconfont.ttf">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\alifont\core\iconfont.ttf</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\alifont\core\iconfont.woff">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\alifont\core\iconfont.woff</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\alifont\core\iconfont.woff2">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\alifont\core\iconfont.woff2</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\verify\font\iconfont.eot">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\iconfont.eot</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\verify\font\iconfont.ttf">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\iconfont.ttf</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\verify\font\iconfont.woff">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\iconfont.woff</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\verify\font\iconfont.woff2">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\iconfont.woff2</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\verify\images\1.jpg">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\images\1.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\verify\images\2.jpg">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\images\2.jpg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\sp\splashSplashScreen.scale-100.png">
      <PackagePath>splashSplashScreen.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\sp\splashSplashScreen.scale-125.png">
      <PackagePath>splashSplashScreen.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\sp\splashSplashScreen.scale-150.png">
      <PackagePath>splashSplashScreen.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\sp\splashSplashScreen.scale-200.png">
      <PackagePath>splashSplashScreen.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\sp\splashSplashScreen.scale-400.png">
      <PackagePath>splashSplashScreen.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\f\OpenSans-Regular.ttf">
      <PackagePath>OpenSans-Regular.ttf</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-400.png">
      <PackagePath>dotnet_bot.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-200.png">
      <PackagePath>dotnet_bot.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-150.png">
      <PackagePath>dotnet_bot.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-125.png">
      <PackagePath>dotnet_bot.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\dotnet_bot.scale-100.png">
      <PackagePath>dotnet_bot.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-400.png">
      <PackagePath>appiconLargeTile.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-200.png">
      <PackagePath>appiconLargeTile.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-150.png">
      <PackagePath>appiconLargeTile.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-125.png">
      <PackagePath>appiconLargeTile.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLargeTile.scale-100.png">
      <PackagePath>appiconLargeTile.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-400.png">
      <PackagePath>appiconWideTile.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-200.png">
      <PackagePath>appiconWideTile.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-150.png">
      <PackagePath>appiconWideTile.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-125.png">
      <PackagePath>appiconWideTile.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconWideTile.scale-100.png">
      <PackagePath>appiconWideTile.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-400.png">
      <PackagePath>appiconMediumTile.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-200.png">
      <PackagePath>appiconMediumTile.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-150.png">
      <PackagePath>appiconMediumTile.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-125.png">
      <PackagePath>appiconMediumTile.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconMediumTile.scale-100.png">
      <PackagePath>appiconMediumTile.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-400.png">
      <PackagePath>appiconSmallTile.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-200.png">
      <PackagePath>appiconSmallTile.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-150.png">
      <PackagePath>appiconSmallTile.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-125.png">
      <PackagePath>appiconSmallTile.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconSmallTile.scale-100.png">
      <PackagePath>appiconSmallTile.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-400.png">
      <PackagePath>appiconStoreLogo.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-200.png">
      <PackagePath>appiconStoreLogo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-150.png">
      <PackagePath>appiconStoreLogo.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-125.png">
      <PackagePath>appiconStoreLogo.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconStoreLogo.scale-100.png">
      <PackagePath>appiconStoreLogo.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-256.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-256.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-48.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-48.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-32.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-32.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-24.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-24.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-lightunplated_targetsize-16.png">
      <PackagePath>appiconLogo.altform-lightunplated_targetsize-16.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-256.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-256.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-48.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-48.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-32.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-32.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-24.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-24.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.altform-unplated_targetsize-16.png">
      <PackagePath>appiconLogo.altform-unplated_targetsize-16.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-256.png">
      <PackagePath>appiconLogo.targetsize-256.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-48.png">
      <PackagePath>appiconLogo.targetsize-48.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-32.png">
      <PackagePath>appiconLogo.targetsize-32.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-24.png">
      <PackagePath>appiconLogo.targetsize-24.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.targetsize-16.png">
      <PackagePath>appiconLogo.targetsize-16.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-400.png">
      <PackagePath>appiconLogo.scale-400.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-200.png">
      <PackagePath>appiconLogo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-150.png">
      <PackagePath>appiconLogo.scale-150.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-125.png">
      <PackagePath>appiconLogo.scale-125.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appiconLogo.scale-100.png">
      <PackagePath>appiconLogo.scale-100.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\resizetizer\r\appicon.ico">
      <PackagePath>appicon.ico</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windows.sdk.net.ref\10.0.19041.57\lib\net8.0\Microsoft.Windows.SDK.NET.dll">
      <PackagePath>Microsoft.Windows.SDK.NET.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windows.sdk.net.ref\10.0.19041.57\lib\net8.0\WinRT.Runtime.dll">
      <PackagePath>WinRT.Runtime.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\lib\net7.0\AntDesign.dll">
      <PackagePath>AntDesign.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\bemit\2.2.0\lib\net8.0\BemIt.dll">
      <PackagePath>BemIt.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\blazor-dragdrop\2.4.0\lib\net6.0\Plk.Blazor.DragDrop.dll">
      <PackagePath>Plk.Blazor.DragDrop.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\deepcloner.core\0.1.0\lib\net8.0\DeepCloner.Core.dll">
      <PackagePath>DeepCloner.Core.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\fluentvalidation\11.4.0\lib\net7.0\FluentValidation.dll">
      <PackagePath>FluentValidation.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\fluentvalidation.dependencyinjectionextensions\11.4.0\lib\netstandard2.1\FluentValidation.DependencyInjectionExtensions.dll">
      <PackagePath>FluentValidation.DependencyInjectionExtensions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\majorsoft.blazor.extensions.browserstorage\1.5.0\lib\net5.0\Majorsoft.Blazor.Extensions.BrowserStorage.dll">
      <PackagePath>Majorsoft.Blazor.Extensions.BrowserStorage.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\lib\net9.0\Masa.Blazor.dll">
      <PackagePath>Masa.Blazor.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor.mobilecomponents\1.10.2\lib\net6.0\Masa.Blazor.MobileComponents.dll">
      <PackagePath>Masa.Blazor.MobileComponents.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.authorization\9.0.7\lib\net9.0\Microsoft.AspNetCore.Authorization.dll">
      <PackagePath>Microsoft.AspNetCore.Authorization.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.components\9.0.7\lib\net9.0\Microsoft.AspNetCore.Components.dll">
      <PackagePath>Microsoft.AspNetCore.Components.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.components.authorization\9.0.7\lib\net9.0\Microsoft.AspNetCore.Components.Authorization.dll">
      <PackagePath>Microsoft.AspNetCore.Components.Authorization.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.components.dataannotations.validation\3.2.0-rc1.20223.4\lib\netstandard2.1\Microsoft.AspNetCore.Components.DataAnnotations.Validation.dll">
      <PackagePath>Microsoft.AspNetCore.Components.DataAnnotations.Validation.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.components.forms\9.0.7\lib\net9.0\Microsoft.AspNetCore.Components.Forms.dll">
      <PackagePath>Microsoft.AspNetCore.Components.Forms.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.components.web\9.0.7\lib\net9.0\Microsoft.AspNetCore.Components.Web.dll">
      <PackagePath>Microsoft.AspNetCore.Components.Web.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.components.webassembly\9.0.7\lib\net9.0\Microsoft.AspNetCore.Components.WebAssembly.dll">
      <PackagePath>Microsoft.AspNetCore.Components.WebAssembly.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.components.webview\9.0.5\lib\net9.0\Microsoft.AspNetCore.Components.WebView.dll">
      <PackagePath>Microsoft.AspNetCore.Components.WebView.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.components.webview.maui\9.0.90\lib\net9.0-windows10.0.19041\Microsoft.AspNetCore.Components.WebView.Maui.dll">
      <PackagePath>Microsoft.AspNetCore.Components.WebView.Maui.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.aspnetcore.metadata\9.0.7\lib\net9.0\Microsoft.AspNetCore.Metadata.dll">
      <PackagePath>Microsoft.AspNetCore.Metadata.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration\9.0.7\lib\net9.0\Microsoft.Extensions.Configuration.dll">
      <PackagePath>Microsoft.Extensions.Configuration.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.abstractions\9.0.7\lib\net9.0\Microsoft.Extensions.Configuration.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.Configuration.Abstractions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.binder\9.0.7\lib\net9.0\Microsoft.Extensions.Configuration.Binder.dll">
      <PackagePath>Microsoft.Extensions.Configuration.Binder.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.fileextensions\9.0.7\lib\net9.0\Microsoft.Extensions.Configuration.FileExtensions.dll">
      <PackagePath>Microsoft.Extensions.Configuration.FileExtensions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.json\9.0.7\lib\net9.0\Microsoft.Extensions.Configuration.Json.dll">
      <PackagePath>Microsoft.Extensions.Configuration.Json.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection\9.0.7\lib\net9.0\Microsoft.Extensions.DependencyInjection.dll">
      <PackagePath>Microsoft.Extensions.DependencyInjection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection.abstractions\9.0.7\lib\net9.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.DependencyInjection.Abstractions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.fileproviders.abstractions\9.0.7\lib\net9.0\Microsoft.Extensions.FileProviders.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.FileProviders.Abstractions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.fileproviders.composite\9.0.5\lib\net9.0\Microsoft.Extensions.FileProviders.Composite.dll">
      <PackagePath>Microsoft.Extensions.FileProviders.Composite.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.fileproviders.embedded\9.0.5\lib\net9.0\Microsoft.Extensions.FileProviders.Embedded.dll">
      <PackagePath>Microsoft.Extensions.FileProviders.Embedded.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.fileproviders.physical\9.0.7\lib\net9.0\Microsoft.Extensions.FileProviders.Physical.dll">
      <PackagePath>Microsoft.Extensions.FileProviders.Physical.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.filesystemglobbing\9.0.7\lib\net9.0\Microsoft.Extensions.FileSystemGlobbing.dll">
      <PackagePath>Microsoft.Extensions.FileSystemGlobbing.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging\9.0.7\lib\net9.0\Microsoft.Extensions.Logging.dll">
      <PackagePath>Microsoft.Extensions.Logging.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.7\lib\net9.0\Microsoft.Extensions.Logging.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.Logging.Abstractions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.debug\9.0.7\lib\net9.0\Microsoft.Extensions.Logging.Debug.dll">
      <PackagePath>Microsoft.Extensions.Logging.Debug.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.7\lib\net9.0\Microsoft.Extensions.Options.dll">
      <PackagePath>Microsoft.Extensions.Options.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.primitives\9.0.7\lib\net9.0\Microsoft.Extensions.Primitives.dll">
      <PackagePath>Microsoft.Extensions.Primitives.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.graphics.win2d\1.3.2\lib\net6.0-windows10.0.19041.0\Microsoft.Graphics.Canvas.Interop.dll">
      <PackagePath>Microsoft.Graphics.Canvas.Interop.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.io.recyclablememorystream\3.0.1\lib\net6.0\Microsoft.IO.RecyclableMemoryStream.dll">
      <PackagePath>Microsoft.IO.RecyclableMemoryStream.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.jsinterop\9.0.7\lib\net9.0\Microsoft.JSInterop.dll">
      <PackagePath>Microsoft.JSInterop.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.jsinterop.webassembly\9.0.7\lib\net9.0\Microsoft.JSInterop.WebAssembly.dll">
      <PackagePath>Microsoft.JSInterop.WebAssembly.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\Microsoft.Maui.Controls.dll">
      <PackagePath>Microsoft.Maui.Controls.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.xaml\9.0.90\lib\net9.0-windows10.0.19041\Microsoft.Maui.Controls.Xaml.dll">
      <PackagePath>Microsoft.Maui.Controls.Xaml.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.core\9.0.90\lib\net9.0-windows10.0.19041\Microsoft.Maui.dll">
      <PackagePath>Microsoft.Maui.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.essentials\9.0.90\lib\net9.0-windows10.0.19041\Microsoft.Maui.Essentials.dll">
      <PackagePath>Microsoft.Maui.Essentials.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.graphics\9.0.90\lib\net9.0-windows10.0.19041\Microsoft.Maui.Graphics.dll">
      <PackagePath>Microsoft.Maui.Graphics.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.graphics.win2d.winui.desktop\9.0.90\lib\net9.0-windows10.0.19041\Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll">
      <PackagePath>Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.win32.systemevents\8.0.0\runtimes\win\lib\net8.0\Microsoft.Win32.SystemEvents.dll">
      <PackagePath>Microsoft.Win32.SystemEvents.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Graphics.Imaging.Projection.dll">
      <PackagePath>Microsoft.Graphics.Imaging.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.InteractiveExperiences.Projection.dll">
      <PackagePath>Microsoft.InteractiveExperiences.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Security.Authentication.OAuth.Projection.dll">
      <PackagePath>Microsoft.Security.Authentication.OAuth.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.WinUI.dll">
      <PackagePath>Microsoft.WinUI.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AI.ContentSafety.Projection.dll">
      <PackagePath>Microsoft.Windows.AI.ContentSafety.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AI.Imaging.Projection.dll">
      <PackagePath>Microsoft.Windows.AI.Imaging.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AI.Projection.dll">
      <PackagePath>Microsoft.Windows.AI.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AI.Text.Projection.dll">
      <PackagePath>Microsoft.Windows.AI.Text.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AppLifecycle.Projection.dll">
      <PackagePath>Microsoft.Windows.AppLifecycle.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AppNotifications.Builder.Projection.dll">
      <PackagePath>Microsoft.Windows.AppNotifications.Builder.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AppNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.AppNotifications.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.ApplicationModel.Background.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.Background.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.ApplicationModel.Resources.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.Resources.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.BadgeNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.BadgeNotifications.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Management.Deployment.Projection.dll">
      <PackagePath>Microsoft.Windows.Management.Deployment.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Media.Capture.Projection.dll">
      <PackagePath>Microsoft.Windows.Media.Capture.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.PushNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.PushNotifications.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Security.AccessControl.Projection.dll">
      <PackagePath>Microsoft.Windows.Security.AccessControl.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Storage.Projection.dll">
      <PackagePath>Microsoft.Windows.Storage.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.System.Power.Projection.dll">
      <PackagePath>Microsoft.Windows.System.Power.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.System.Projection.dll">
      <PackagePath>Microsoft.Windows.System.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Widgets.Projection.dll">
      <PackagePath>Microsoft.Windows.Widgets.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.WindowsAppRuntime.Bootstrap.Net.dll">
      <PackagePath>Microsoft.WindowsAppRuntime.Bootstrap.Net.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\oneof\3.0.223\lib\netstandard2.0\OneOf.dll">
      <PackagePath>OneOf.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\opencvsharp4\4.11.0.20250507\lib\net6.0\OpenCvSharp.dll">
      <PackagePath>OpenCvSharp.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\opencvsharp4.extensions\4.11.0.20250507\lib\net6.0\OpenCvSharp.Extensions.dll">
      <PackagePath>OpenCvSharp.Extensions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\system.drawing.common\8.0.11\lib\net8.0\System.Drawing.Common.dll">
      <PackagePath>System.Drawing.Common.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\util.reflection\1.0.3\lib\net6.0\Util.Reflection.dll">
      <PackagePath>Util.Reflection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\ar\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ar\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\ca\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ca\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\cs\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>cs\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\da\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>da\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\de\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>de\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\el\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>el\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\es\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>es\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\fi\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>fi\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\fr\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>fr\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\he\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>he\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\hi\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>hi\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\hr\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>hr\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\hu\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>hu\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\id\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>id\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\it\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>it\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\ja\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ja\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\ko\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ko\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\ms\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ms\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\nb\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>nb\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\nl\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>nl\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\pl\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>pl\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\pt-BR\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>pt-BR\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\pt\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>pt\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\ro\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ro\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\ru\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>ru\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\sk\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>sk\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\sv\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>sv\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\th\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>th\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\tr\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>tr\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\uk\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>uk\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\vi\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>vi\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\zh-HK\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>zh-HK\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\zh-Hans\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>zh-Hans\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\zh-Hant\Microsoft.Maui.Controls.resources.dll">
      <PackagePath>zh-Hant\Microsoft.Maui.Controls.resources.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.graphics.win2d\1.3.2\runtimes\win-x64\native\Microsoft.Graphics.Canvas.dll">
      <PackagePath>Microsoft.Graphics.Canvas.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.3179.45\runtimes\win-x64\native\WebView2Loader.dll">
      <PackagePath>WebView2Loader.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\runtimes\win-x64\native\Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\runtimes\win-x64\native\Microsoft.WindowsAppRuntime.Bootstrap.dll">
      <PackagePath>Microsoft.WindowsAppRuntime.Bootstrap.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\opencvsharp4.runtime.win\4.11.0.20250507\runtimes\win-x64\native\OpenCvSharpExtern.dll">
      <PackagePath>OpenCvSharpExtern.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\opencvsharp4.runtime.win\4.11.0.20250507\runtimes\win-x64\native\opencv_videoio_ffmpeg4110_64.dll">
      <PackagePath>opencv_videoio_ffmpeg4110_64.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.3179.45\runtimes\win-x64\native_uap\Microsoft.Web.WebView2.Core.dll">
      <PackagePath>Microsoft.Web.WebView2.Core.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Shared\bin\Debug\net9.0\HX.Experiment.Shared.dll">
      <PackagePath>HX.Experiment.Shared.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.3179.45\lib_manual\net8.0-windows10.0.17763.0\Microsoft.Web.WebView2.Core.Projection.dll">
      <PackagePath>Microsoft.Web.WebView2.Core.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\bin\Debug\net9.0\UFU.CoreFX.Shared.dll">
      <PackagePath>UFU.CoreFX.Shared.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\UFU.IoT.HRV\UFU.IoT.Shared\bin\Debug\net9.0\UFU.IoT.Shared.dll">
      <PackagePath>UFU.IoT.Shared.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\bin\Debug\net9.0-windows10.0.19041.0\win10-x64\HX.Experiment.MAUI.deps.json">
      <PackagePath>HX.Experiment.MAUI.deps.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\staticwebassets.development.json">
      <PackagePath>UFU.CoreFX.Shared.staticwebassets.runtime.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\staticwebassets.build.endpoints.json">
      <PackagePath>UFU.CoreFX.Shared.staticwebassets.endpoints.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\UFU.IoT.HRV\UFU.IoT.Shared\obj\Debug\net9.0\staticwebassets.development.json">
      <PackagePath>UFU.IoT.Shared.staticwebassets.runtime.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\UFU.IoT.HRV\UFU.IoT.Shared\obj\Debug\net9.0\staticwebassets.build.endpoints.json">
      <PackagePath>UFU.IoT.Shared.staticwebassets.endpoints.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Shared\obj\Debug\net9.0\staticwebassets.development.json">
      <PackagePath>HX.Experiment.Shared.staticwebassets.runtime.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Shared\obj\Debug\net9.0\staticwebassets.build.endpoints.json">
      <PackagePath>HX.Experiment.Shared.staticwebassets.endpoints.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\staticwebassets.build.endpoints.json">
      <PackagePath>HX.Experiment.MAUI.staticwebassets.endpoints.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.controls.core\9.0.90\lib\net9.0-windows10.0.19041\Microsoft.Maui.Controls.pri">
      <PackagePath>Microsoft.Maui.Controls.pri</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.maui.core\9.0.90\lib\net9.0-windows10.0.19041\Microsoft.Maui.pri">
      <PackagePath>Microsoft.Maui.pri</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\css\ant-design-blazor.aliyun.css">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.aliyun.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\css\ant-design-blazor.aliyun.min.css">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.aliyun.min.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\css\ant-design-blazor.compact.css">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.compact.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\css\ant-design-blazor.compact.min.css">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.compact.min.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\css\ant-design-blazor.css">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\css\ant-design-blazor.dark.css">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.dark.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\css\ant-design-blazor.dark.min.css">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.dark.min.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\css\ant-design-blazor.min.css">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.min.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\css\ant-design-blazor.variable.css">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.variable.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\css\ant-design-blazor.variable.min.css">
      <PackagePath>wwwroot\_content\AntDesign\css\ant-design-blazor.variable.min.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\js\ant-design-blazor.js">
      <PackagePath>wwwroot\_content\AntDesign\js\ant-design-blazor.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\js\ant-design-blazor.js.map">
      <PackagePath>wwwroot\_content\AntDesign\js\ant-design-blazor.js.map</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\affix\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\affix\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\affix\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\affix\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\affix\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\affix\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\alert\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\alert\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\alert\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\alert\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\alert\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\alert\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\alert\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\alert\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\anchor\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\anchor\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\anchor\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\anchor\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\anchor\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\anchor\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\anchor\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\anchor\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\ant-design-blazor.aliyun.less">
      <PackagePath>wwwroot\_content\AntDesign\less\ant-design-blazor.aliyun.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\ant-design-blazor.compact.less">
      <PackagePath>wwwroot\_content\AntDesign\less\ant-design-blazor.compact.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\ant-design-blazor.dark.less">
      <PackagePath>wwwroot\_content\AntDesign\less\ant-design-blazor.dark.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\ant-design-blazor.less">
      <PackagePath>wwwroot\_content\AntDesign\less\ant-design-blazor.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\ant-design-blazor.variable.less">
      <PackagePath>wwwroot\_content\AntDesign\less\ant-design-blazor.variable.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\auto-complete\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\auto-complete\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\auto-complete\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\auto-complete\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\auto-complete\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\auto-complete\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\avatar\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\avatar\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\avatar\style\group.less">
      <PackagePath>wwwroot\_content\AntDesign\less\avatar\style\group.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\avatar\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\avatar\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\avatar\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\avatar\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\back-top\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\back-top\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\back-top\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\back-top\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\back-top\style\responsive.less">
      <PackagePath>wwwroot\_content\AntDesign\less\back-top\style\responsive.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\badge\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\badge\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\badge\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\badge\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\badge\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\badge\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\badge\style\ribbon.less">
      <PackagePath>wwwroot\_content\AntDesign\less\badge\style\ribbon.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\badge\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\badge\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\breadcrumb\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\breadcrumb\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\breadcrumb\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\breadcrumb\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\breadcrumb\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\breadcrumb\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\breadcrumb\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\breadcrumb\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\button\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\button\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\button\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\button\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\button\style\mixin.less">
      <PackagePath>wwwroot\_content\AntDesign\less\button\style\mixin.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\button\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\button\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\button\style\space-compact.less">
      <PackagePath>wwwroot\_content\AntDesign\less\button\style\space-compact.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\calendar\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\calendar\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\calendar\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\calendar\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\calendar\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\calendar\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\card\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\card\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\card\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\card\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\card\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\card\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\card\style\size.less">
      <PackagePath>wwwroot\_content\AntDesign\less\card\style\size.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\carousel\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\carousel\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\carousel\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\carousel\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\carousel\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\carousel\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\carousel\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\carousel\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\cascader\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\cascader\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\cascader\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\cascader\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\cascader\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\cascader\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\cascader\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\cascader\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\checkbox\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\checkbox\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\checkbox\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\checkbox\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\checkbox\style\mixin.less">
      <PackagePath>wwwroot\_content\AntDesign\less\checkbox\style\mixin.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\checkbox\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\checkbox\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\checkbox\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\checkbox\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\collapse\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\collapse\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\collapse\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\collapse\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\collapse\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\collapse\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\collapse\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\collapse\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\comment\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\comment\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\comment\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\comment\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\comment\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\comment\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\comment\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\comment\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\components.less">
      <PackagePath>wwwroot\_content\AntDesign\less\components.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\config-provider\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\config-provider\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\config-provider\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\config-provider\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\date-picker\style\Calendar.less">
      <PackagePath>wwwroot\_content\AntDesign\less\date-picker\style\Calendar.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\date-picker\style\DecadePanel.less">
      <PackagePath>wwwroot\_content\AntDesign\less\date-picker\style\DecadePanel.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\date-picker\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\date-picker\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\date-picker\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\date-picker\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\date-picker\style\MonthPanel.less">
      <PackagePath>wwwroot\_content\AntDesign\less\date-picker\style\MonthPanel.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\date-picker\style\MonthPicker.less">
      <PackagePath>wwwroot\_content\AntDesign\less\date-picker\style\MonthPicker.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\date-picker\style\panel.less">
      <PackagePath>wwwroot\_content\AntDesign\less\date-picker\style\panel.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\date-picker\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\date-picker\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\date-picker\style\Picker.less">
      <PackagePath>wwwroot\_content\AntDesign\less\date-picker\style\Picker.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\date-picker\style\RangePicker.less">
      <PackagePath>wwwroot\_content\AntDesign\less\date-picker\style\RangePicker.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\date-picker\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\date-picker\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\date-picker\style\status.less">
      <PackagePath>wwwroot\_content\AntDesign\less\date-picker\style\status.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\date-picker\style\TimePicker.less">
      <PackagePath>wwwroot\_content\AntDesign\less\date-picker\style\TimePicker.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\date-picker\style\WeekPicker.less">
      <PackagePath>wwwroot\_content\AntDesign\less\date-picker\style\WeekPicker.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\date-picker\style\YearPanel.less">
      <PackagePath>wwwroot\_content\AntDesign\less\date-picker\style\YearPanel.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\descriptions\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\descriptions\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\descriptions\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\descriptions\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\descriptions\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\descriptions\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\descriptions\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\descriptions\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\divider\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\divider\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\divider\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\divider\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\divider\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\divider\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\drawer\style\customize.less">
      <PackagePath>wwwroot\_content\AntDesign\less\drawer\style\customize.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\drawer\style\drawer.less">
      <PackagePath>wwwroot\_content\AntDesign\less\drawer\style\drawer.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\drawer\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\drawer\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\drawer\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\drawer\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\drawer\style\motion.less">
      <PackagePath>wwwroot\_content\AntDesign\less\drawer\style\motion.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\drawer\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\drawer\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\dropdown\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\dropdown\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\dropdown\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\dropdown\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\dropdown\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\dropdown\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\dropdown\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\dropdown\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\dropdown\style\status.less">
      <PackagePath>wwwroot\_content\AntDesign\less\dropdown\style\status.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\empty\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\empty\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\empty\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\empty\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\empty\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\empty\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\empty\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\empty\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\form\style\components.less">
      <PackagePath>wwwroot\_content\AntDesign\less\form\style\components.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\form\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\form\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\form\style\horizontal.less">
      <PackagePath>wwwroot\_content\AntDesign\less\form\style\horizontal.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\form\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\form\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\form\style\inline.less">
      <PackagePath>wwwroot\_content\AntDesign\less\form\style\inline.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\form\style\mixin.less">
      <PackagePath>wwwroot\_content\AntDesign\less\form\style\mixin.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\form\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\form\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\form\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\form\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\form\style\status.less">
      <PackagePath>wwwroot\_content\AntDesign\less\form\style\status.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\form\style\vertical.less">
      <PackagePath>wwwroot\_content\AntDesign\less\form\style\vertical.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\grid\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\grid\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\grid\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\grid\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\grid\style\mixin.less">
      <PackagePath>wwwroot\_content\AntDesign\less\grid\style\mixin.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\grid\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\grid\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\icon\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\icon\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\icon\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\icon\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\icon\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\icon\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\image\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\image\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\image\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\image\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\image\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\image\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\input\style\affix.less">
      <PackagePath>wwwroot\_content\AntDesign\less\input\style\affix.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\input\style\allow-clear.less">
      <PackagePath>wwwroot\_content\AntDesign\less\input\style\allow-clear.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\input\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\input\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\input\style\IE11.less">
      <PackagePath>wwwroot\_content\AntDesign\less\input\style\IE11.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\input\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\input\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\input\style\mixin.less">
      <PackagePath>wwwroot\_content\AntDesign\less\input\style\mixin.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\input\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\input\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\input\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\input\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\input\style\search-input.less">
      <PackagePath>wwwroot\_content\AntDesign\less\input\style\search-input.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\input\style\status.less">
      <PackagePath>wwwroot\_content\AntDesign\less\input\style\status.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\input-number\style\affix.less">
      <PackagePath>wwwroot\_content\AntDesign\less\input-number\style\affix.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\input-number\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\input-number\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\input-number\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\input-number\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\input-number\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\input-number\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\input-number\style\status.less">
      <PackagePath>wwwroot\_content\AntDesign\less\input-number\style\status.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\layout\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\layout\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\layout\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\layout\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\layout\style\light.less">
      <PackagePath>wwwroot\_content\AntDesign\less\layout\style\light.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\layout\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\layout\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\layout\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\layout\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\list\style\bordered.less">
      <PackagePath>wwwroot\_content\AntDesign\less\list\style\bordered.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\list\style\customize.less">
      <PackagePath>wwwroot\_content\AntDesign\less\list\style\customize.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\list\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\list\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\list\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\list\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\list\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\list\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\list\style\responsive.less">
      <PackagePath>wwwroot\_content\AntDesign\less\list\style\responsive.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\list\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\list\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\locale-provider\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\locale-provider\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\locale-provider\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\locale-provider\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\mentions\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\mentions\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\mentions\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\mentions\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\mentions\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\mentions\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\mentions\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\mentions\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\mentions\style\status.less">
      <PackagePath>wwwroot\_content\AntDesign\less\mentions\style\status.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\menu\style\dark.less">
      <PackagePath>wwwroot\_content\AntDesign\less\menu\style\dark.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\menu\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\menu\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\menu\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\menu\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\menu\style\light.less">
      <PackagePath>wwwroot\_content\AntDesign\less\menu\style\light.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\menu\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\menu\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\menu\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\menu\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\menu\style\status.less">
      <PackagePath>wwwroot\_content\AntDesign\less\menu\style\status.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\message\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\message\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\message\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\message\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\message\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\message\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\modal\style\confirm.less">
      <PackagePath>wwwroot\_content\AntDesign\less\modal\style\confirm.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\modal\style\customize.less">
      <PackagePath>wwwroot\_content\AntDesign\less\modal\style\customize.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\modal\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\modal\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\modal\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\modal\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\modal\style\modal.less">
      <PackagePath>wwwroot\_content\AntDesign\less\modal\style\modal.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\modal\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\modal\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\modal\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\modal\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\notification\style\customize.less">
      <PackagePath>wwwroot\_content\AntDesign\less\notification\style\customize.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\notification\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\notification\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\notification\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\notification\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\notification\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\notification\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\notification\style\placement.less">
      <PackagePath>wwwroot\_content\AntDesign\less\notification\style\placement.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\notification\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\notification\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\page-header\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\page-header\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\page-header\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\page-header\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\page-header\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\page-header\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\page-header\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\page-header\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\pagination\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\pagination\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\pagination\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\pagination\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\pagination\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\pagination\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\pagination\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\pagination\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\popconfirm\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\popconfirm\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\popconfirm\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\popconfirm\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\popconfirm\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\popconfirm\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\popover\style\customize.less">
      <PackagePath>wwwroot\_content\AntDesign\less\popover\style\customize.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\popover\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\popover\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\popover\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\popover\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\popover\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\popover\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\popover\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\popover\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\progress\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\progress\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\progress\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\progress\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\progress\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\progress\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\progress\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\progress\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\radio\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\radio\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\radio\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\radio\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\radio\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\radio\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\radio\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\radio\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\rate\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\rate\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\rate\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\rate\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\rate\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\rate\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\result\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\result\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\result\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\result\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\result\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\result\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\result\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\result\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\segmented\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\segmented\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\segmented\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\segmented\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\segmented\style\mixins.less">
      <PackagePath>wwwroot\_content\AntDesign\less\segmented\style\mixins.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\segmented\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\segmented\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\select\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\select\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\select\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\select\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\select\style\multiple.less">
      <PackagePath>wwwroot\_content\AntDesign\less\select\style\multiple.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\select\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\select\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\select\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\select\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\select\style\single.less">
      <PackagePath>wwwroot\_content\AntDesign\less\select\style\single.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\select\style\status.less">
      <PackagePath>wwwroot\_content\AntDesign\less\select\style\status.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\skeleton\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\skeleton\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\skeleton\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\skeleton\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\skeleton\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\skeleton\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\slider\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\slider\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\slider\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\slider\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\slider\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\slider\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\space\style\compact.less">
      <PackagePath>wwwroot\_content\AntDesign\less\space\style\compact.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\space\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\space\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\space\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\space\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\space\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\space\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\space\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\space\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\spin\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\spin\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\spin\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\spin\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\spin\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\spin\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\spin\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\spin\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\statistic\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\statistic\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\statistic\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\statistic\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\statistic\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\statistic\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\steps\style\compatibility.less">
      <PackagePath>wwwroot\_content\AntDesign\less\steps\style\compatibility.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\steps\style\custom-icon.less">
      <PackagePath>wwwroot\_content\AntDesign\less\steps\style\custom-icon.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\steps\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\steps\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\steps\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\steps\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\steps\style\label-placement.less">
      <PackagePath>wwwroot\_content\AntDesign\less\steps\style\label-placement.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\steps\style\nav.less">
      <PackagePath>wwwroot\_content\AntDesign\less\steps\style\nav.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\steps\style\progress.less">
      <PackagePath>wwwroot\_content\AntDesign\less\steps\style\progress.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\steps\style\progress-dot.less">
      <PackagePath>wwwroot\_content\AntDesign\less\steps\style\progress-dot.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\steps\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\steps\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\steps\style\small.less">
      <PackagePath>wwwroot\_content\AntDesign\less\steps\style\small.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\steps\style\vertical.less">
      <PackagePath>wwwroot\_content\AntDesign\less\steps\style\vertical.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\aliyun.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\aliyun.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\color\bezierEasing.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\color\bezierEasing.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\color\colorPalette.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\color\colorPalette.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\color\colors.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\color\colors.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\color\tinyColor.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\color\tinyColor.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\compact.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\compact.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\core\base.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\core\base.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\core\global.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\core\global.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\core\iconfont.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\core\iconfont.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\core\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\core\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\core\motion.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\core\motion.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\core\motion\fade.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\core\motion\fade.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\core\motion\move.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\core\motion\move.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\core\motion\other.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\core\motion\other.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\core\motion\slide.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\core\motion\slide.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\core\motion\swing.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\core\motion\swing.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\core\motion\zoom.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\core\motion\zoom.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\dark.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\dark.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\default.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\default.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\mixins\box.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\mixins\box.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\mixins\clearfix.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\mixins\clearfix.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\mixins\compact-item.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\mixins\compact-item.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\mixins\compact-item-vertical.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\mixins\compact-item-vertical.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\mixins\compatibility.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\mixins\compatibility.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\mixins\customize.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\mixins\customize.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\mixins\iconfont.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\mixins\iconfont.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\mixins\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\mixins\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\mixins\modal-mask.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\mixins\modal-mask.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\mixins\motion.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\mixins\motion.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\mixins\operation-unit.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\mixins\operation-unit.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\mixins\reset.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\mixins\reset.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\mixins\rounded-arrow.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\mixins\rounded-arrow.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\mixins\size.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\mixins\size.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\mixins\typography.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\mixins\typography.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\themes\aliyun.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\themes\aliyun.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\themes\compact.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\themes\compact.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\themes\dark.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\themes\dark.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\themes\default.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\themes\default.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\themes\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\themes\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\themes\variable.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\themes\variable.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\v2-compatible-reset.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\v2-compatible-reset.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\style\variable.less">
      <PackagePath>wwwroot\_content\AntDesign\less\style\variable.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\switch\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\switch\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\switch\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\switch\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\switch\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\switch\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\switch\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\switch\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\table\style\bordered.less">
      <PackagePath>wwwroot\_content\AntDesign\less\table\style\bordered.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\table\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\table\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\table\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\table\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\table\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\table\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\table\style\radius.less">
      <PackagePath>wwwroot\_content\AntDesign\less\table\style\radius.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\table\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\table\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\table\style\size.less">
      <PackagePath>wwwroot\_content\AntDesign\less\table\style\size.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tabs\style\card.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tabs\style\card.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tabs\style\card-style.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tabs\style\card-style.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tabs\style\card-style.rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tabs\style\card-style.rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tabs\style\dropdown.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tabs\style\dropdown.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tabs\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tabs\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tabs\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tabs\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tabs\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tabs\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tabs\style\position.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tabs\style\position.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tabs\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tabs\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tabs\style\size.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tabs\style\size.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tag\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tag\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tag\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tag\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tag\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tag\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tag\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tag\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\timeline\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\timeline\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\timeline\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\timeline\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\timeline\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\timeline\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\time-picker\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\time-picker\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\time-picker\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\time-picker\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tooltip\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tooltip\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tooltip\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tooltip\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tooltip\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tooltip\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tooltip\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tooltip\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\transfer\style\customize.less">
      <PackagePath>wwwroot\_content\AntDesign\less\transfer\style\customize.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\transfer\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\transfer\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\transfer\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\transfer\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\transfer\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\transfer\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\transfer\style\status.less">
      <PackagePath>wwwroot\_content\AntDesign\less\transfer\style\status.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tree\style\directory.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tree\style\directory.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tree\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tree\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tree\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tree\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tree\style\mixin.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tree\style\mixin.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tree\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tree\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tree\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tree\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tree-select\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tree-select\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tree-select\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tree-select\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\tree-select\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\tree-select\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\typography\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\typography\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\typography\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\typography\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\typography\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\typography\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\upload\style\entry.less">
      <PackagePath>wwwroot\_content\AntDesign\less\upload\style\entry.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\upload\style\index.less">
      <PackagePath>wwwroot\_content\AntDesign\less\upload\style\index.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\upload\style\patch.less">
      <PackagePath>wwwroot\_content\AntDesign\less\upload\style\patch.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\antdesign\0.15.5\staticwebassets\less\upload\style\rtl.less">
      <PackagePath>wwwroot\_content\AntDesign\less\upload\style\rtl.less</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\css\masa-blazor.min.css">
      <PackagePath>wwwroot\_content\Masa.Blazor\css\masa-blazor.min.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\chunks\EventType-63cda6c3.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\chunks\EventType-63cda6c3.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\chunks\helper-6d386307.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\chunks\helper-6d386307.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\chunks\index-cef005e4.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\chunks\index-cef005e4.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\chunks\touch-5a32c5ea.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\chunks\touch-5a32c5ea.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\chunks\tslib.es6-68144fbe.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\chunks\tslib.es6-68144fbe.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\components\input\index-b92d32d0.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\input\index-b92d32d0.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\components\navigation-drawer\touch-47e3a6be.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\navigation-drawer\touch-47e3a6be.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\components\overlay\scroll-strategy-dc362cab.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\overlay\scroll-strategy-dc362cab.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\components\page-stack\index-3d21987e.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\page-stack\index-3d21987e.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\components\page-stack\touch-ecbec91c.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\page-stack\touch-ecbec91c.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\components\scroll-to-target\index-1c14c8ac.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\scroll-to-target\index-1c14c8ac.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\components\transition\index-339f8848.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\transition\index-339f8848.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\components\window\touch-f9d2ba92.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\components\window\touch-f9d2ba92.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\manifest.json">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\manifest.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\masa-blazor.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\masa-blazor.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\mixins\activatable\index-82cb7376.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\mixins\activatable\index-82cb7376.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\mixins\intersect\index-f360c115.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\mixins\intersect\index-f360c115.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\mixins\outside-click\index-47d0ce8d.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\mixins\outside-click\index-47d0ce8d.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\mixins\resize\index-07a0c3f6.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\mixins\resize\index-07a0c3f6.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\wrappers\baidumap.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\baidumap.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\wrappers\drawflow-proxy.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\drawflow-proxy.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\wrappers\echarts.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\echarts.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\wrappers\monaco-editor.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\monaco-editor.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\wrappers\quill\quill-helper.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\quill\quill-helper.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\wrappers\sortable.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\sortable.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\js\wrappers\vditor\vditor-helper.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\js\wrappers\vditor\vditor-helper.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\masa.blazor\1.10.2\staticwebassets\Presets\EllipsisText\EllipsisText.razor.js">
      <PackagePath>wwwroot\_content\Masa.Blazor\Presets\EllipsisText\EllipsisText.razor.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\obj\Debug\net9.0-windows10.0.19041.0\win10-x64\scopedcss\bundle\HX.Experiment.MAUI.styles.css">
      <PackagePath>wwwroot\HX.Experiment.MAUI.styles.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\css\app.css">
      <PackagePath>wwwroot\css\app.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\favicon.ico">
      <PackagePath>wwwroot\favicon.ico</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\images\check-circle-outline.svg">
      <PackagePath>wwwroot\images\check-circle-outline.svg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.MAUI\wwwroot\index.html">
      <PackagePath>wwwroot\index.html</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Shared\obj\Debug\net9.0\scopedcss\projectbundle\HX.Experiment.Shared.bundle.scp.css">
      <PackagePath>wwwroot\_content\HX.Experiment.Shared\HX.Experiment.Shared.rr2qxwjego.bundle.scp.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Shared\wwwroot\css\materialdesign\v7.1.96\css\materialdesignicons.min.css">
      <PackagePath>wwwroot\_content\HX.Experiment.Shared\css\materialdesign\v7.1.96\css\materialdesignicons.min.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Shared\wwwroot\css\materialdesign\v7.1.96\css\materialdesignicons.min.css.map">
      <PackagePath>wwwroot\_content\HX.Experiment.Shared\css\materialdesign\v7.1.96\css\materialdesignicons.min.css.map</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\HuiXin\HX.HRV\UFU.IoT.HRV\UFU.IoT.Shared\obj\Debug\net9.0\scopedcss\projectbundle\UFU.IoT.Shared.bundle.scp.css">
      <PackagePath>wwwroot\_content\UFU.IoT.Shared\UFU.IoT.Shared.rsp46tpbl7.bundle.scp.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\obj\Debug\net9.0\scopedcss\projectbundle\UFU.CoreFX.Shared.bundle.scp.css">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\UFU.CoreFX.Shared.hyipwchtko.bundle.scp.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\css\app.css">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\css\app.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\css\base.css">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\css\base.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\css\base-list.css">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\css\base-list.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\css\common.css">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\css\common.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\font\demo.css">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\font\demo.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\font\demo_index.html">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\font\demo_index.html</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\font\iconfont.css">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\font\iconfont.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\font\iconfont.js">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\font\iconfont.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\font\iconfont.json">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\font\iconfont.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\images\down.svg">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\images\down.svg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\images\sort.svg">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\images\sort.svg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\images\up.svg">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\images\up.svg</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\index.html">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\index.html</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\js\cookieStore.js">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\js\cookieStore.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\js\createCanvas.js">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\js\createCanvas.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\js\utils.js">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\js\utils.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\alifont\core\demo.css">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\alifont\core\demo.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\alifont\core\demo_index.html">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\alifont\core\demo_index.html</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\alifont\core\iconfont.css">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\alifont\core\iconfont.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\alifont\core\iconfont.js">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\alifont\core\iconfont.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\alifont\core\iconfont.json">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\alifont\core\iconfont.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\verify\font\demo.css">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\demo.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\verify\font\demo_index.html">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\demo_index.html</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\verify\font\iconfont.css">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\iconfont.css</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\verify\font\iconfont.js">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\iconfont.js</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\verify\font\iconfont.json">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\iconfont.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\Project\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\wwwroot\lib\verify\font\iconfont.svg">
      <PackagePath>wwwroot\_content\UFU.CoreFX.Shared\lib\verify\font\iconfont.svg</PackagePath>
    </AppxPackagedFile>
  </ItemGroup>
  <ItemGroup>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.7.msix">
      <Name>Microsoft.WindowsAppRuntime.1.7</Name>
      <Version>7000.522.1444.0</Version>
      <Architecture>x86</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.7, MinVersion = 7000.522.1444.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.7.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.7.msix">
      <Name>Microsoft.WindowsAppRuntime.1.7</Name>
      <Version>7000.522.1444.0</Version>
      <Architecture>win32</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.7, MinVersion = 7000.522.1444.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.7.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\buildTransitive\..\tools\MSIX\win10-x64\Microsoft.WindowsAppRuntime.1.7.msix">
      <Name>Microsoft.WindowsAppRuntime.1.7</Name>
      <Version>7000.522.1444.0</Version>
      <Architecture>x64</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.7, MinVersion = 7000.522.1444.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\buildTransitive\..\tools\MSIX\win10-x64\Microsoft.WindowsAppRuntime.1.7.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\buildTransitive\..\tools\MSIX\win10-arm64\Microsoft.WindowsAppRuntime.1.7.msix">
      <Name>Microsoft.WindowsAppRuntime.1.7</Name>
      <Version>7000.522.1444.0</Version>
      <Architecture>arm64</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.7, MinVersion = 7000.522.1444.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\buildTransitive\..\tools\MSIX\win10-arm64\Microsoft.WindowsAppRuntime.1.7.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
  </ItemGroup>
</Project>
