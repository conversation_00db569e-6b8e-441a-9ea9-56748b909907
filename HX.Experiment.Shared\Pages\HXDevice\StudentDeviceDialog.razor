@using HX.Experiment.Shared.Model
@using UFU.IoT.Models
@using UFU.CoreFX.Models
@using UFU.CoreFX.Shared.Services
@namespace HX.Experiment.Shared.Pages.HXDevice

<MCard>
    <MCardTitle Class="text-center">
        开始检测
    </MCardTitle>
    <MCardText Class="d-flex justify-center flex-column align-center">
        <MRow Style="width: 100%;">
            <MCol Cols="6">
                <MLabel class="font-weight-medium" Style="font-size:1.5rem; ">
                    姓名:
                    <span>@StudentModel.Name</span>
                </MLabel>
            </MCol>
            <MCol Cols="6">
                <MLabel class="font-weight-medium" Style="font-size:1.5rem; ">
                    身份证号:
                    <span>@StudentModel.CardId</span>
                </MLabel>
            </MCol>
            <MCol Cols="12" Style="width: 100%;">
                <MDivider></MDivider>
            </MCol>
        </MRow>

        <MCard Class="text-center d-flex flex-column justify-center "
               Style="width: 100px; height: 100px;background-color: #4caf50; border-radius: 10px;">
            <MLabel Class="text-h5" Style="color: white" Color="white">@(DeviceModel?.Name ?? "设备")</MLabel>
        </MCard>
        <MLabel Style="color: #0288d1; margin-top: 10px;font-size: 1.5rem">请找到显示以上内容的设备进行佩戴</MLabel>
    </MCardText>
    <MCardActions Class="text-center d-flex justify-center">
        <MButton OnClick="HandleSubmit"
                 Color="#04B55C"
                 Style="width: 200px;color: white; font-size:1.5rem; border-radius: 5px;">
            已佩戴,开始检测
        </MButton>
    </MCardActions>
</MCard>


