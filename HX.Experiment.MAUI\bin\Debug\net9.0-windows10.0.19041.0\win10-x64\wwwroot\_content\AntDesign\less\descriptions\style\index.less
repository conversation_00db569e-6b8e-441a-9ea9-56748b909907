@import '../../style/themes/index';
@import '../../style/mixins/index';

@descriptions-prefix-cls: ~'@{ant-prefix}-descriptions';

.@{descriptions-prefix-cls} {
  &-header {
    display: flex;
    align-items: center;
    margin-bottom: @descriptions-title-margin-bottom;
  }

  &-title {
    flex: auto;
    overflow: hidden;
    color: @heading-color;
    font-weight: bold;
    font-size: @font-size-lg;
    line-height: @line-height-base;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &-extra {
    margin-left: auto;
    color: @descriptions-extra-color;
    font-size: @font-size-base;
  }

  &-view {
    width: 100%;
    border-radius: @border-radius-base;

    table {
      width: 100%;
      table-layout: fixed;
    }
  }

  &-row {
    > th,
    > td {
      padding-bottom: @descriptions-item-padding-bottom;
    }

    &:last-child {
      border-bottom: none;
    }
  }

  &-item-label {
    color: @heading-color;
    font-weight: normal;
    font-size: @font-size-base;
    line-height: @line-height-base;
    text-align: start;

    &::after {
      & when (@descriptions-item-trailing-colon=true) {
        content: ':';
      }
      & when not (@descriptions-item-trailing-colon=true) {
        content: ' ';
      }

      position: relative;
      top: -0.5px;
      margin: 0 @descriptions-item-label-colon-margin-right 0
        @descriptions-item-label-colon-margin-left;
    }

    &.@{descriptions-prefix-cls}-item-no-colon::after {
      content: ' ';
    }
  }

  &-item-no-label {
    &::after {
      margin: 0;
      content: '';
    }
  }

  &-item-content {
    display: table-cell;
    flex: 1;
    color: @text-color;
    font-size: @font-size-base;
    line-height: @line-height-base;
    word-break: break-word;
    overflow-wrap: break-word;
  }

  &-item {
    padding-bottom: 0;
    vertical-align: top;

    &-container {
      display: flex;

      .@{descriptions-prefix-cls}-item-label,
      .@{descriptions-prefix-cls}-item-content {
        display: inline-flex;
        align-items: baseline;
      }
    }
  }

  &-middle {
    .@{descriptions-prefix-cls}-row {
      > th,
      > td {
        padding-bottom: @padding-sm;
      }
    }
  }

  &-small {
    .@{descriptions-prefix-cls}-row {
      > th,
      > td {
        padding-bottom: @padding-xs;
      }
    }
  }

  &-bordered {
    .@{descriptions-prefix-cls}-view {
      border: 1px solid @border-color-split;

      > table {
        table-layout: auto;
        border-collapse: collapse;
      }
    }

    .@{descriptions-prefix-cls}-item-label,
    .@{descriptions-prefix-cls}-item-content {
      padding: @descriptions-default-padding;
      border-right: 1px solid @border-color-split;

      &:last-child {
        border-right: none;
      }
    }

    .@{descriptions-prefix-cls}-item-label {
      background-color: @descriptions-bg;

      &::after {
        display: none;
      }
    }

    .@{descriptions-prefix-cls}-row {
      border-bottom: 1px solid @border-color-split;

      &:last-child {
        border-bottom: none;
      }
    }

    &.@{descriptions-prefix-cls}-middle {
      .@{descriptions-prefix-cls}-item-label,
      .@{descriptions-prefix-cls}-item-content {
        padding: @descriptions-middle-padding;
      }
    }

    &.@{descriptions-prefix-cls}-small {
      .@{descriptions-prefix-cls}-item-label,
      .@{descriptions-prefix-cls}-item-content {
        padding: @descriptions-small-padding;
      }
    }
  }
}

@import './rtl';
