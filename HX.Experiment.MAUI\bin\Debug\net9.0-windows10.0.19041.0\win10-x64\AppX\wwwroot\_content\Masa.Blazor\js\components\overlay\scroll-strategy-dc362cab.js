import{c as o}from"../../chunks/helper-6d386307.js";function e(o,e){const l=[];if(e&&o&&!e.contains(o))return l;for(;o&&(t(o)&&l.push(o),o!==e);)o=o.parentElement;return l}function t(o){if(!o||o.nodeType!==Node.ELEMENT_NODE)return!1;const e=window.getComputedStyle(o);return"scroll"===e.overflowY||"auto"===e.overflowY&&o.scrollHeight>o.clientHeight}function l(l,r,s,n,c){return"block"===l.strategy?function(l,r){const s=l.root.offsetParent,n=[...new Set([...e(l.contentEl,r.contained?s:void 0)])],c=(i=s||document.documentElement,t(i)&&i),a=()=>{c&&l.root.classList.add("m-overlay--scroll-blocked");const e=window.innerWidth-document.documentElement.offsetWidth;n.filter((o=>!o.classList.contains("m-overlay-scroll-blocked"))).forEach(((t,l)=>{t.style.setProperty("--m-body-scroll-x",o(-t.scrollLeft)),t.style.setProperty("--m-body-scroll-y",o(-t.scrollTop)),t!==document.documentElement&&t.style.setProperty("--m-scrollbar-offset",o(e)),t.classList.add("m-overlay-scroll-blocked")}))};var i;return a(),{bind:a,unbind:()=>{n.filter((o=>o.classList.contains("m-overlay-scroll-blocked"))).forEach(((o,e)=>{const t=parseFloat(o.style.getPropertyValue("--m-body-scroll-x")),l=parseFloat(o.style.getPropertyValue("--m-body-scroll-y")),r=o.style.scrollBehavior;o.style.scrollBehavior="auto",o.style.removeProperty("--m-body-scroll-x"),o.style.removeProperty("--m-body-scroll-y"),o.style.removeProperty("--m-scrollbar-offset"),o.classList.remove("m-overlay-scroll-blocked"),o.scrollLeft=-t,o.scrollTop=-l,o.style.scrollBehavior=r})),c&&l.root.classList.remove("m-overlay--scroll-blocked")}}}({root:r,contentEl:s,targetEl:n},l):function(o,t){var l;const r=null!==(l=o.targetEl)&&void 0!==l?l:o.contentEl,s=()=>{var e;null===(e=o.invoker)||void 0===e||e.invokeMethodAsync("ScrollStrategy_OnScroll",t.strategy)},n=[document,...e(r)];return n.forEach((o=>o.addEventListener("scroll",s,{passive:!0}))),{unbind:()=>{var e;null===(e=o.invoker)||void 0===e||e.dispose(),n.forEach((o=>o.removeEventListener("scroll",s)))}}}({root:r,contentEl:s,targetEl:n,invoker:c},l)}export{l as useScrollStrategies};
//# sourceMappingURL=scroll-strategy-dc362cab.js.map
