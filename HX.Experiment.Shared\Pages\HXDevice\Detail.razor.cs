using HX.Experiment.Shared.Services;
using Microsoft.AspNetCore.Components;
using UFU.IoT.Models;
using HX.Experiment.Shared.Model;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared.Services;
using Masa.Blazor;
using Microsoft.AspNetCore.Components.Web;

namespace HX.Experiment.Shared.Pages.HXDevice;

public partial class Detail : ComponentBase
{
    [Inject] IDeviceService DeviceService { get; set; }
    [Inject] public StateService StateService { get; set; }

    [Inject] private IPopupService PopupService { get; set; }

    private DeviceModel DeviceModel => DeviceService.CurrentDevice;
    private DiskInfo CurrentLinkedDevice => DeviceService.CurrentLinkedDevice;
    private DataModel<DeviceBindComputer>? BindInfo;
    private bool IsLoading = true;
    private string? ErrorMsg;

    // 新建检测相关属性
    private bool isShowNewTest = false;
    private StudentInfo DialogStudentData = new();

    protected override async Task OnInitializedAsync()
    {
        try
        {
            var res = await StateService.GetAsJsonAsync<DataModel<DeviceBindComputer>>("/api/v2/hx_experiment/DeviceBindComputer/Get",
            new Dictionary<string, string> { { nameof(DeviceBindComputer.ComputerId), DeviceService.ComputerId } });
            if (res.Success)
            {
                BindInfo = res.Data;
            }
            else
            {
                ErrorMsg = res.Message;
            }
            DeviceService.OnUDiskAfterInsertedEvent += OnChanged;
        }
        catch (Exception ex)
        {
            ErrorMsg = ex.Message;
        }
        finally
        {
            IsLoading = false;
        }
    }

    private void OnChanged(List<DiskInfo> list)
    {
        InvokeAsync(StateHasChanged);
    }

    private Task HandleBind(MouseEventArgs args)
    {
        return PopupService.ConfirmAsync(
              "绑定设备",
              "确认将设备绑定到当前电脑?",
              AlertTypes.Info,
              async args =>
              {
                  try
                  {
                      var url = "/api/v2/hx_experiment/DeviceBindComputer/Add";
                      var  data = new DeviceBindComputer()
                      {
                          ComputerId = DeviceService.ComputerId,
                      };
                      var res = await StateService.PostAsJsonAsync<DataModel<DeviceBindComputer>>(url, data);
                      if (res.Success)
                      {
                          await PopupService.EnqueueSnackbarAsync("绑定成功", AlertTypes.Success);
                      }
                      else
                      {
                          await PopupService.EnqueueSnackbarAsync(res.Message, AlertTypes.Error);
                      }
                  }
                  catch (Exception e)
                  {
                      args.Cancel();
                      await PopupService.EnqueueSnackbarAsync(e, true);
                  }
              });
    }

    /// <summary>
    /// 打开新建检测对话框
    /// </summary>
    private async Task OpenNewTestDialog()
    {
        if (DeviceModel == null || !DeviceModel.IsOnline)
        {
            await PopupService.EnqueueSnackbarAsync("设备未在线，无法开始检测！", AlertTypes.Warning);
            return;
        }

        // 重置学生数据
        DialogStudentData = new StudentInfo();
        isShowNewTest = true;
    }

    /// <summary>
    /// 刷新数据
    /// </summary>
    private async Task RefreshData()
    {
        try
        {
            var res = await StateService.GetAsJsonAsync<DataModel<DeviceBindComputer>>("/api/v2/hx_experiment/DeviceBindComputer/Get",
            new Dictionary<string, string> { { nameof(DeviceBindComputer.ComputerId), DeviceService.ComputerId } });
            if (res.Success)
            {
                BindInfo = res.Data;
            }
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await PopupService.EnqueueSnackbarAsync($"刷新数据失败: {ex.Message}", AlertTypes.Error);
        }
    }

}