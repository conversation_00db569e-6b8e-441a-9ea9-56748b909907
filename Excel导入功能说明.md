# Excel导入学生信息功能说明

## 功能概述

为HX.Experiment教学实验平台添加了Excel批量导入学生信息的功能，支持从Excel文件中批量导入学生数据到指定班级。

## 实现的组件

### 1. 接口定义 (HX.Experiment.Shared\Services\IImportStudentService.cs)

定义了Excel导入服务的接口，包含以下主要方法：

- `ImportStudentsFromExcelAsync()` - 从Excel文件导入学生信息
- `ValidateExcelFormatAsync()` - 验证Excel文件格式
- `GetExcelTemplateAsync()` - 获取Excel模板文件

以及相关的数据模型：
- `ImportResult` - 导入结果
- `ValidationResult` - 验证结果
- `FailedStudentInfo` - 失败的学生信息
- `StudentPreviewInfo` - 学生预览信息

### 2. 服务实现 (HX.Experiment.Web.Service\Services\ImportStudentService.cs)

实现了Excel导入的核心逻辑：

- 使用EPPlus库处理Excel文件
- 支持.xlsx和.xls格式
- 文件大小限制为10MB
- 验证Excel文件格式和数据完整性
- 批量保存学生信息到数据库
- 检查重复学号避免冲突
- 详细的错误处理和日志记录

### 3. API控制器 (HX.Experiment.Web.Service\Area\ImportStudentController.cs)

提供RESTful API接口：

- `POST /api/v2/hx_experiment/ImportStudent/validate` - 验证Excel文件
- `POST /api/v2/hx_experiment/ImportStudent/import` - 导入学生信息
- `GET /api/v2/hx_experiment/ImportStudent/template` - 下载Excel模板
- `GET /api/v2/hx_experiment/ImportStudent/classes` - 获取班级列表
- `POST /api/v2/hx_experiment/ImportStudent/cleanup` - 清理导入数据

### 4. 前端界面 (HX.Experiment.Shared\Pages\ClassManager.razor)

在班级管理页面中集成了Excel导入功能：

- 导入对话框界面
- 文件选择和验证
- 数据预览功能
- 导入进度显示
- 错误信息展示
- 失败记录详情查看

## Excel文件格式要求

### 标题行（第一行）
| 姓名 | 学号/身份证号 | 用户ID |
|------|---------------|--------|

### 数据要求
- **姓名**：必填项，学生姓名
- **学号/身份证号**：必填项，唯一标识
- **用户ID**：可选项，关联的用户账号

### 示例数据
| 姓名 | 学号/身份证号 | 用户ID |
|------|---------------|--------|
| 张三 | 2023001       | user001 |
| 李四 | 2023002       | user002 |

## 功能特性

### 1. 文件验证
- 支持.xlsx和.xls格式
- 文件大小限制10MB
- 标题行格式验证
- 数据完整性检查

### 2. 数据预览
- 显示前10条数据预览
- 验证通过后才能导入
- 实时显示验证结果

### 3. 导入处理
- 批量导入学生信息
- 自动检查重复学号
- 详细的成功/失败统计
- 失败记录详情展示

### 4. 错误处理
- 详细的错误信息提示
- 按行显示失败原因
- 支持部分成功导入

### 5. 用户体验
- 响应式界面设计
- 进度指示器
- 操作确认提示
- 模板文件下载

## 使用流程

1. **选择班级**：在班级管理页面选择要导入学生的班级
2. **点击导入**：点击"导入学生"按钮打开导入对话框
3. **下载模板**：（可选）下载Excel模板文件
4. **选择文件**：选择准备好的Excel文件
5. **验证格式**：系统自动验证文件格式并显示预览
6. **开始导入**：确认无误后点击"开始导入"
7. **查看结果**：查看导入结果和失败记录详情

## 技术依赖

- **EPPlus 7.5.1**：Excel文件处理
- **MASA.Blazor**：UI组件库
- **UFU.CoreFX**：核心框架
- **.NET 9.0**：运行时环境

## 权限配置

所有API接口都配置了相应的权限验证：
- 学生管理接口/Excel导入验证
- 学生管理接口/Excel导入
- 学生管理接口/下载Excel模板
- 学生管理接口/获取班级列表
- 学生管理接口/清理导入数据

## 注意事项

1. 确保Excel文件格式正确，特别是标题行
2. 学号/身份证号在同一班级中必须唯一
3. 文件大小不能超过10MB
4. 建议先下载模板文件，按照格式填写数据
5. 导入前请备份重要数据

## 扩展建议

1. 添加更多的数据验证规则（如手机号格式验证）
2. 支持更多Excel格式和字段
3. 添加导入历史记录功能
4. 支持导入数据的撤销操作
5. 添加导入进度的实时显示
