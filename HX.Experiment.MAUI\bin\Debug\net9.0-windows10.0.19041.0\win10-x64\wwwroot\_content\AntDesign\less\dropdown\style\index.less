@import '../../style/themes/index';
@import '../../style/mixins/index';
@import './status';

@dropdown-prefix-cls: ~'@{ant-prefix}-dropdown';

.@{dropdown-prefix-cls} {
  .reset-component();

  position: absolute;
  top: -9999px;
  left: -9999px;
  z-index: @zindex-dropdown;
  display: block;

  &::before {
    position: absolute;
    top: -@popover-distance + @popover-arrow-width;
    right: 0;
    bottom: -@popover-distance + @popover-arrow-width;
    left: -7px;
    z-index: -9999;
    opacity: 0.0001;
    content: ' ';
  }

  &-wrap {
    position: relative;

    .@{ant-prefix}-btn > .@{iconfont-css-prefix}-down {
      font-size: 10px;
    }

    .@{iconfont-css-prefix}-down::before {
      transition: transform @animation-duration-base;
    }
  }

  &-wrap-open {
    .@{iconfont-css-prefix}-down::before {
      transform: rotate(180deg);
    }
  }

  &-hidden,
  &-menu-hidden,
  &-menu-submenu-hidden {
    display: none;
  }

  // Offset the popover to account for the dropdown arrow
  &-show-arrow&-placement-topLeft,
  &-show-arrow&-placement-top,
  &-show-arrow&-placement-topRight {
    padding-bottom: @popover-distance;
  }

  &-show-arrow&-placement-bottomLeft,
  &-show-arrow&-placement-bottom,
  &-show-arrow&-placement-bottomRight {
    padding-top: @popover-distance;
  }

  // Arrows
  // .popover-arrow is outer, .popover-arrow:after is inner

  &-arrow {
    position: absolute;
    z-index: 1; // lift it up so the menu wouldn't cask shadow on it
    display: block;
    width: @popover-arrow-width;
    height: @popover-arrow-width;
    .roundedArrow(@popover-arrow-width, 5px, @popover-bg);
  }

  &-placement-top > &-arrow,
  &-placement-topLeft > &-arrow,
  &-placement-topRight > &-arrow {
    bottom: @popover-arrow-width * sqrt((1 / 2)) + 2px;
    box-shadow: 3px 3px 7px -3px fade(@black, 10%);
    transform: rotate(45deg);
  }

  &-placement-top > &-arrow {
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
  }

  &-placement-topLeft > &-arrow {
    left: 16px;
  }

  &-placement-topRight > &-arrow {
    right: 16px;
  }

  &-placement-bottom > &-arrow,
  &-placement-bottomLeft > &-arrow,
  &-placement-bottomRight > &-arrow {
    top: (@popover-arrow-width + 2px) * sqrt((1 / 2));
    box-shadow: 2px 2px 5px -2px fade(@black, 10%);
    transform: rotate(-135deg) translateY(-0.5px);
  }

  &-placement-bottom > &-arrow {
    left: 50%;
    transform: translateX(-50%) rotate(-135deg) translateY(-0.5px);
  }

  &-placement-bottomLeft > &-arrow {
    left: 16px;
  }

  &-placement-bottomRight > &-arrow {
    right: 16px;
  }

  &-menu {
    position: relative;
    margin: 0;
    padding: @dropdown-edge-child-vertical-padding 0;
    text-align: left;
    list-style-type: none;
    background-color: @dropdown-menu-bg;
    background-clip: padding-box;
    border-radius: @border-radius-base;
    outline: none;
    box-shadow: @box-shadow-base;

    &-item-group-title {
      padding: 5px @control-padding-horizontal;
      color: @text-color-secondary;
      transition: all @animation-duration-slow;
    }

    &-submenu-popup {
      position: absolute;
      z-index: @zindex-dropdown;
      background: transparent;
      box-shadow: none;
      transform-origin: 0 0;

      ul,
      li {
        list-style: none;
      }

      ul {
        margin-right: 0.3em;
        margin-left: 0.3em;
      }
    }

    // ======================= Item Content =======================
    &-item {
      position: relative;
      display: flex;
      align-items: center;
    }

    &-item-icon {
      min-width: 12px;
      margin-right: 8px;
      font-size: @font-size-sm;
    }

    &-title-content {
      flex: auto;

      > a {
        color: inherit;
        transition: all @animation-duration-slow;

        &:hover {
          color: inherit;
        }

        &::after {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          content: '';
        }
      }
    }

    // =========================== Item ===========================
    &-item,
    &-submenu-title {
      clear: both;
      margin: 0;
      padding: @dropdown-vertical-padding @control-padding-horizontal;
      color: @text-color;
      font-weight: normal;
      font-size: @dropdown-font-size;
      line-height: @dropdown-line-height;
      cursor: pointer;
      transition: all @animation-duration-slow;

      &:first-child {
        & when (@dropdown-edge-child-vertical-padding = 0) {
          border-radius: @border-radius-base @border-radius-base 0 0;
        }
      }

      &:last-child {
        & when (@dropdown-edge-child-vertical-padding = 0) {
          border-radius: 0 0 @border-radius-base @border-radius-base;
        }
      }

      &-selected {
        color: @dropdown-selected-color;
        background-color: @dropdown-selected-bg;
      }

      &:hover,
      &&-active {
        background-color: @item-hover-bg;
      }

      &-disabled {
        color: @disabled-color;
        cursor: not-allowed;

        &:hover {
          color: @disabled-color;
          background-color: @dropdown-menu-submenu-disabled-bg;
          cursor: not-allowed;
        }

        a {
          pointer-events: none;
        }
      }

      &-divider {
        height: 1px;
        margin: 4px 0;
        overflow: hidden;
        line-height: 0;
        background-color: @border-color-split;
      }

      .@{dropdown-prefix-cls}-menu-submenu-expand-icon {
        position: absolute;
        right: @padding-xs;

        .@{dropdown-prefix-cls}-menu-submenu-arrow-icon {
          margin-right: 0 !important;
          color: @text-color-secondary;
          font-size: 10px;
          font-style: normal;
        }
      }
    }

    &-item-group-list {
      margin: 0 8px;
      padding: 0;
      list-style: none;
    }

    &-submenu-title {
      padding-right: @control-padding-horizontal + @font-size-sm;
    }

    &-submenu-vertical {
      position: relative;
    }

    &-submenu-vertical > & {
      position: absolute;
      top: 0;
      left: 100%;
      min-width: 100%;
      margin-left: 4px;
      transform-origin: 0 0;
    }

    &-submenu&-submenu-disabled .@{dropdown-prefix-cls}-menu-submenu-title {
      &,
      .@{dropdown-prefix-cls}-menu-submenu-arrow-icon {
        color: @disabled-color;
        background-color: @dropdown-menu-submenu-disabled-bg;
        cursor: not-allowed;
      }
    }

    // https://github.com/ant-design/ant-design/issues/19264
    &-submenu-selected &-submenu-title {
      color: @primary-color;
    }
  }

  &.@{ant-prefix}-slide-down-enter.@{ant-prefix}-slide-down-enter-active&-placement-bottomLeft,
  &.@{ant-prefix}-slide-down-appear.@{ant-prefix}-slide-down-appear-active&-placement-bottomLeft,
  &.@{ant-prefix}-slide-down-enter.@{ant-prefix}-slide-down-enter-active&-placement-bottom,
  &.@{ant-prefix}-slide-down-appear.@{ant-prefix}-slide-down-appear-active&-placement-bottom,
  &.@{ant-prefix}-slide-down-enter.@{ant-prefix}-slide-down-enter-active&-placement-bottomRight,
  &.@{ant-prefix}-slide-down-appear.@{ant-prefix}-slide-down-appear-active&-placement-bottomRight {
    animation-name: antSlideUpIn;
  }

  &.@{ant-prefix}-slide-up-enter.@{ant-prefix}-slide-up-enter-active&-placement-topLeft,
  &.@{ant-prefix}-slide-up-appear.@{ant-prefix}-slide-up-appear-active&-placement-topLeft,
  &.@{ant-prefix}-slide-up-enter.@{ant-prefix}-slide-up-enter-active&-placement-top,
  &.@{ant-prefix}-slide-up-appear.@{ant-prefix}-slide-up-appear-active&-placement-top,
  &.@{ant-prefix}-slide-up-enter.@{ant-prefix}-slide-up-enter-active&-placement-topRight,
  &.@{ant-prefix}-slide-up-appear.@{ant-prefix}-slide-up-appear-active&-placement-topRight {
    animation-name: antSlideDownIn;
  }

  &.@{ant-prefix}-slide-down-leave.@{ant-prefix}-slide-down-leave-active&-placement-bottomLeft,
  &.@{ant-prefix}-slide-down-leave.@{ant-prefix}-slide-down-leave-active&-placement-bottom,
  &.@{ant-prefix}-slide-down-leave.@{ant-prefix}-slide-down-leave-active&-placement-bottomRight {
    animation-name: antSlideUpOut;
  }

  &.@{ant-prefix}-slide-up-leave.@{ant-prefix}-slide-up-leave-active&-placement-topLeft,
  &.@{ant-prefix}-slide-up-leave.@{ant-prefix}-slide-up-leave-active&-placement-top,
  &.@{ant-prefix}-slide-up-leave.@{ant-prefix}-slide-up-leave-active&-placement-topRight {
    animation-name: antSlideDownOut;
  }
}

.@{dropdown-prefix-cls}-trigger,
.@{dropdown-prefix-cls}-link,
.@{dropdown-prefix-cls}-button {
  > .@{iconfont-css-prefix}.@{iconfont-css-prefix}-down {
    font-size: 10px;
    vertical-align: baseline;
  }
}

.@{dropdown-prefix-cls}-button {
  white-space: nowrap;

  &.@{ant-prefix}-btn-group > .@{ant-prefix}-btn {
    &-loading,
    &-loading + .@{ant-prefix}-btn {
      cursor: default;
      pointer-events: none;
    }

    &-loading + .@{ant-prefix}-btn::before {
      display: block;
    }

    &:last-child:not(:first-child):not(.@{ant-prefix}-btn-icon-only) {
      padding-right: @padding-xs;
      padding-left: @padding-xs;
    }
  }
}

// https://github.com/ant-design/ant-design/issues/4903
.@{dropdown-prefix-cls}-menu-dark {
  &,
  .@{dropdown-prefix-cls}-menu {
    background: @menu-dark-bg;
  }
  .@{dropdown-prefix-cls}-menu-item,
  .@{dropdown-prefix-cls}-menu-submenu-title,
  .@{dropdown-prefix-cls}-menu-item > a,
  .@{dropdown-prefix-cls}-menu-item > .@{iconfont-css-prefix} + span > a {
    color: @text-color-secondary-dark;
    .@{dropdown-prefix-cls}-menu-submenu-arrow::after {
      color: @text-color-secondary-dark;
    }

    &:hover {
      color: @text-color-inverse;
      background: transparent;
    }
  }
  .@{dropdown-prefix-cls}-menu-item-selected {
    &,
    &:hover,
    > a {
      color: @text-color-inverse;
      background: @primary-color;
    }
  }
}

@import './rtl';
