@page "/class-manager"
@using System.Net
@using System.Text.Json
@using HX.Experiment.Shared.Model
@using Masa.Blazor
@using UFU.CoreFX.Models
@using UFU.CoreFX.Shared.Services
@using UFU.CoreFX.Utils
@using UFU.IoT.Models

@inject StateService state
@inject IPopupService PopupService

<MCard Class="pa-6" Style="background:#f5f5f5;margin:auto;">
	<MRow Align="@AlignTypes.Center" Justify="@JustifyTypes.SpaceBetween" Class="mb-4">
		<MCol>
			<h2 class="font-weight-bold">班级管理</h2>
		</MCol>
		<MCol Class="d-flex justify-end">
			<MButton Color="primary" Class="mr-2" OnClick="ShowCreateClassDialog">创建班级</MButton>
			<MButton Color="secondary" OnClick="ImportStudents">导入学生</MButton>
		</MCol>
	</MRow>

	<MRow>
		<MCol Xl="3" Sm="12">
			<MCard Class="elevation-2 pa-4">
				<MCardTitle>班级列表</MCardTitle>
				<MList Dense>
					@foreach (var classItem in classes)
					{
						<MListItem OnClick="async () => await SwicthClass(classItem)"
						           Class="@(selectedClass?.Id == classItem.Id ? "primary--text" : "")">
							<MListItemContent>
								<MListItemTitle>@classItem.Data?.Name</MListItemTitle>
							</MListItemContent>
							<MListItemAction>
								<MButton Icon IconName="mdi-pencil" Color="primary"
								         OnClick="() => HandleEditClassClick(classItem)" OnClickStopPropagation>
								</MButton>
								<MButton Class="ml-4" Icon IconName="mdi-delete" Color="error"
								         OnClick="() => DeleteFormDataById(classItem.Id, _classFormId)"
								         OnClickStopPropagation>
								</MButton>
							</MListItemAction>
						</MListItem>
					}
				</MList>
			</MCard>
		</MCol>

		<MCol xs="8" Xl="9" Sm="12">
			@if (Students != null)
			{
				<MCard Class="elevation-2 pa-4">
					<MCardTitle>
                        	<div class="d-flex  ">
                                <div>学生列表</div>
                                <div>
                                    <MButton LeftIconName="mdi-plus-box" Class="ml-4" Color="indigo" Onclick="@HandleAddStudentOnClick">
	                            添加学生
                                </MButton>
                                </div>
                            </div>	
                    </MCardTitle>
					<MDataTable Headers="_headers"   Items="Students" Page="@_page" ItemsPerPage="@_itemsPerPage"
					            OnOptionsUpdate="@HandleOnOptionsUpdate" ServerItemsLength="@_total"
					            Class="elevation-1">
					</MDataTable>
			
				</MCard>
			}
			else
			{
				<MCard Class="elevation-2 pa-6 text-center">
					<MIcon Size="64" Color="grey">mdi-school-outline</MIcon>
					<div class="mt-4 grey--text">请选择一个班级查看学生信息</div>
				</MCard>
			}
		</MCol>
	</MRow>
</MCard>

<!-- 创建班级对话框 -->
<MDialog @bind-Value="showEditClassDialog" MaxWidth="500">
	<MCard>
		<MForm Model="EditClass" AutoLabel>
			<MCardTitle>创建/更新班级</MCardTitle>
			<MCardText>
				<MTextField Required @bind-Value="EditClass.Data.Name" Label="班级名称" Outlined Dense/>
				<MTextField @bind-Value="EditClass.Data.Year" Label="入学年份" Outlined Dense/>
				<MTextField @bind-Value="EditClass.Data.Major" Label="专业名称" Outlined Dense/>
				<MTextarea @bind-Value="EditClass.Data.Description" Label="描述/备注" Outlined Dense Class="mt-4"/>
			</MCardText>
			<MCardActions>
				<MSpacer/>
				<MButton Color="grey" OnClick="(_) => showEditClassDialog = false">取消</MButton>
				<MButton Type="submit" Color="primary" OnClick="HandleEditClass">保存</MButton>
			</MCardActions>
		</MForm>
	</MCard>
</MDialog>



<MDialog @bind-Value="showEditStudentDialog" MaxWidth="500">
	<MCard>
		<MCardTitle>编辑学生</MCardTitle>
		<MCardText>
			<MTextField @bind-Value="editStudent.Data.Name" Label="学生姓名" Outlined Dense/>
			<MTextField @bind-Value="editStudent.Data.CardId" Label="学号" Outlined Dense/>
		</MCardText>
		<MCardActions>
			<MSpacer/>
			<MButton Color="grey" Onclick="() => showEditStudentDialog = false">取消</MButton>
			<MButton Color="primary" Onclick="@HandleEditStudent">保存</MButton>
		</MCardActions>
	</MCard>
</MDialog>

<!-- Excel导入对话框 -->
<MDialog @bind-Value="showImportDialog" MaxWidth="800">
	<MCard>
		<MCardTitle Class="d-flex align-center">
			<MIcon Class="mr-2">mdi-file-excel</MIcon>
			Excel批量导入学生
		</MCardTitle>
		<MCardText>
			<MRow>
				<MCol Cols="12">
					<MAlert Type="AlertTypes.Info" Class="mb-4">
						<div>
							<strong>导入说明：</strong>
							<ul class="mt-2">
								<li>支持.xlsx和.xls格式的Excel文件</li>
								<li>文件大小不能超过10MB</li>
								<li>Excel文件第一行必须是标题行：姓名、学号/身份证号、用户ID</li>
								<li>姓名和学号/身份证号为必填项</li>
							</ul>
						</div>
					</MAlert>
				</MCol>
			</MRow>

			<MRow>
				<MCol Cols="12" Class="d-flex align-center mb-4">
					<MButton Color="secondary" OnClick="DownloadTemplate" Class="mr-4">
						<MIcon Left>mdi-download</MIcon>
						下载模板
					</MButton>
					<span class="text-caption">建议先下载模板文件，按照格式填写学生信息</span>
				</MCol>
			</MRow>

			<MRow>
				<MCol Cols="12">
					<InputFile
						accept=".xlsx,.xls"
						OnChange="OnFileSelected"
						class="form-control"
						style="margin-bottom: 16px;">
					</InputFile>
				</MCol>
			</MRow>

			@if (isValidating)
			{
				<MRow>
					<MCol Cols="12" Class="text-center">
						<MProgressCircular Indeterminate Color="primary" Class="mr-2"></MProgressCircular>
						正在验证文件格式...
					</MCol>
				</MRow>
			}

			@if (validationResult != null)
			{
				<MRow>
					<MCol Cols="12">
						@if (validationResult.IsValid)
						{
							<MAlert Type="AlertTypes.Success" Class="mb-4">
								文件格式验证通过！预览前10条数据：
							</MAlert>

							@if (validationResult.PreviewStudents.Any())
							{
								<MDataTable
									Headers="_previewHeaders"
									Items="validationResult.PreviewStudents"
									ItemsPerPage="10"
									HideDefaultFooter
									Class="elevation-1">
								</MDataTable>
							}
						}
						else
						{
							<MAlert Type="AlertTypes.Error" Class="mb-4">
								<div>
									<strong>文件格式验证失败：</strong>
									<ul class="mt-2">
										@foreach (var error in validationResult.ErrorMessages)
										{
											<li>@error</li>
										}
									</ul>
								</div>
							</MAlert>
						}
					</MCol>
				</MRow>
			}

			@if (importResult != null)
			{
				<MRow>
					<MCol Cols="12">
						@if (importResult.Success)
						{
							<MAlert Type="AlertTypes.Success" Class="mb-4">
								<div>
									<strong>导入完成！</strong>
									<p>成功导入：@importResult.SuccessCount 条记录</p>
									@if (importResult.FailureCount > 0)
									{
										<p>失败：@importResult.FailureCount 条记录</p>
									}
								</div>
							</MAlert>
						}
						else
						{
							<MAlert Type="AlertTypes.Error" Class="mb-4">
								<div>
									<strong>导入失败：</strong>
									<ul class="mt-2">
										@foreach (var error in importResult.ErrorMessages)
										{
											<li>@error</li>
										}
									</ul>
								</div>
							</MAlert>
						}

						@if (importResult.FailedStudents.Any())
						{
							<MExpansionPanels>
								<MExpansionPanel>
									<MExpansionPanelHeader>
										查看失败记录详情 (@importResult.FailedStudents.Count 条)
									</MExpansionPanelHeader>
									<MExpansionPanelContent>
										<MDataTable
											Headers="_failedHeaders"
											Items="importResult.FailedStudents"
											ItemsPerPage="10"
											Class="elevation-1">
										</MDataTable>
									</MExpansionPanelContent>
								</MExpansionPanel>
							</MExpansionPanels>
						}
					</MCol>
				</MRow>
			}
		</MCardText>
		<MCardActions>
			<MSpacer/>
			<MButton Color="grey" OnClick="() => showImportDialog = false">取消</MButton>
			<MButton
				Color="primary"
				OnClick="ImportFile"
				Disabled="@(selectedFile == null || validationResult?.IsValid != true || isImporting)"
				Loading="isImporting">
				@if (isImporting)
				{
					<span>导入中...</span>
				}
				else
				{
					<span>开始导入</span>
				}
			</MButton>
		</MCardActions>
	</MCard>
</MDialog>



@code {

	private List<DataTableHeader<DataModel<StudentInfo>>> _headers => new()
	{
		new()
		{
			Text = "序号",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = studentDataModel => studentDataModel.Id
		},
		new()
		{
			Text = "学生姓名",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = studentDataModel => studentDataModel?.Data.Name
		},
		new()
		{
			Text = "学号",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = studentDataModel => studentDataModel.Data?.CardId
		},
		new()
		{
			Text = "操作",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = studentDataModel =>
			{
				RenderFragment renderFragment = @<div>
					                                <MButton Icon IconName="mdi-pencil" Color="primary"
					                                         OnClick="() => HandleEditStudentOnClick(studentDataModel)">
					                                </MButton>
					                                <MButton Icon IconName="mdi-delete" Color="error"
					                                         OnClick="() => DeleteFormDataById(studentDataModel.Id, _studentFormId)">
					                                </MButton>
				                                </div>
					;
				return renderFragment;
			}
		},
	};

	private List<DataTableHeader<StudentPreviewInfo>> _previewHeaders => new()
	{
		new()
		{
			Text = "行号",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = item => item.RowNumber.ToString()
		},
		new()
		{
			Text = "姓名",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = item => item.Name
		},
		new()
		{
			Text = "学号/身份证号",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = item => item.CardId
		},
		new()
		{
			Text = "用户ID",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = item => item.UserId
		}
	};

	private List<DataTableHeader<FailedStudentInfo>> _failedHeaders => new()
	{
		new()
		{
			Text = "行号",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = item => item.RowNumber.ToString()
		},
		new()
		{
			Text = "姓名",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = item => item.Name
		},
		new()
		{
			Text = "学号/身份证号",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = item => item.CardId
		},
		new()
		{
			Text = "错误原因",
			Align = DataTableHeaderAlign.Start,
			Sortable = false,
			CellRender = item => item.ErrorReason
		}
	};

}
