using System.Text.Json.Nodes;
using UFU.CoreFX.Models;
using UFU.CoreFX.Permission;
using UFU.CoreFX.Utils;
using UFU.IoT.Models;

namespace HX.Experiment.Web.Service.Services
{
    /// <summary>
    /// 物联网设备WebSocket
    /// </summary>
    [AllowAnonymous]
    [Permission("设备WebSocket", IsWebSocket = true, Url = "/iot/device/v2")]
    public class DeviceWebSocketConnect : IWebSocket
    {
        private readonly IServiceProvider _serviceProvider;
        private ISocketHandleService _socketHandleService;
        public DeviceWebSocketConnect()
        {
            var connectionScope = HxApp.Services.CreateScope();
            _serviceProvider = connectionScope.ServiceProvider;
            // _serviceProvider = HxApp.Services;
        }

        /// <summary>
        /// 收到文本消息
        /// </summary>
        /// <param name="data"></param>
        public override void OnReciveText(string data)
        {
            try
            {
                var receivedJsonNode = JsonNode.Parse(data);
                var typeStr = receivedJsonNode?["Device"]?.ToJsonString();
                
                if (string.IsNullOrEmpty(typeStr))
                {
                    LogTool.Logger.Error( "设备类型为空");
                    return;
                }
                var type = JsonTool.Deserialize<UFU.IoT.Shared.Models.BinaryDevice>(typeStr);
                _socketHandleService ??= _serviceProvider.GetKeyedService<ISocketHandleService>(type);
                var res = _socketHandleService.ParseTextData(receivedJsonNode, data);
            }
            catch (Exception e)
            {
                LogTool.GetLogger(nameof(DeviceWebSocketConnect),nameof(OnReciveText)).Error("OnReciveText", e);
            }
        }

        string ParseDataToJson(byte[] datas)
        {
            var dataSpan = datas.AsSpan();
            var head = dataSpan[0..2].ToArray().ToHex();
            var size = datas[2..6].ToHex();
            var totalSize = BitConverter.ToUInt32(dataSpan[2..6]);
            var zip = dataSpan[6];
            var jsonSize = BitConverter.ToUInt16(dataSpan[7..9]);
            var json = System.Text.Encoding.UTF8.GetString(datas, 9, jsonSize);
            var bin = dataSpan[(9 + jsonSize)..^2].ToArray().ToHex("");
            var binData = dataSpan[(9 + jsonSize)..^2];
            var crc = BitConverter.ToUInt16(dataSpan[^2..dataSpan.Length]);
            var jsonObj = JsonNode.Parse(json);
            var type = jsonObj?["Data"]?["Type"]?.GetValue<int>();
            if (!binData.IsEmpty && binData.Length > 0)
            {
                var parseService = _serviceProvider.GetRequiredKeyedService<ISocketHandleService>(type);
                var res = parseService.ParseByteData(binData,jsonObj);
                return res.ToJsonString();
            }
            return jsonObj.ToJsonString();
        }

        /// <summary>
        /// 收到二进制数据
        /// </summary>
        /// <param name="data"></param>
        public override void OnReciveBinary(byte[] data)
        {
            Task.Run(() =>
            {
                try
                {
                    var json = ParseDataToJson(data);
                }
                catch (Exception e)
                {
                    LogTool.Logger.Error("OnReciveBinary", e);
                }
            });
        }

        /// <summary>
        /// 关闭连接
        /// 
        /// </summary>
        public override void Close()
        {
            //触发离线事件
            LogTool.Logger.Information($"BinaryDeviceWebSocket:{ConnectId}");
            //关闭连接
            base.Close();
        }

        /// <summary>
        /// 发送文本消息
        /// </summary>
        /// <param name="data"></param>
        public bool SendText(string data)
        {
            _=SendTextAsync(data);
            return true;
        }

        /// <summary>
        /// 发送二进制消息
        /// </summary>
        /// <param name="data"></param>
        public bool SendBinary(byte[] data)
        {
            _=SendBinaryAsync(data);
            return true;
        }

        public override void OnConnect()
        {
            LogTool.GetLogger(nameof(DeviceWebSocketConnect), nameof(OnConnect)).Information("OnConnect");
            base.OnConnect();
        }


        

        
        
    }
}