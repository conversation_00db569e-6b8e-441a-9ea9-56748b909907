@import '../../style/themes/index';
@import '../../style/mixins/index';
@import './mixins.less';

@segmented-prefix-cls: ~'@{ant-prefix}-segmented';

@segmented-container-padding: 2px;

.@{segmented-prefix-cls} {
  .reset-component();
  display: inline-block;
  padding: @segmented-container-padding;
  color: @segmented-label-color;
  background-color: @segmented-bg;
  border-radius: @border-radius-base;
  transition: all 0.3s @ease-in-out;

  &-group {
    position: relative;
    display: flex;
    align-items: stretch;
    justify-items: flex-start;
    width: 100%;
  }

  // block styles
  &&-block {
    display: flex;
  }

  &&-block &-item {
    flex: 1;
    min-width: 0;
  }

  // hover/focus styles
  &:not(&-disabled) {
    &:hover,
    &:focus {
      background-color: @segmented-hover-bg;
    }
  }

  // item styles
  &-item {
    position: relative;
    text-align: center;
    cursor: pointer;
    transition: color 0.3s @ease-in-out;

    &-selected {
      .segmented-item-selected();
      color: @segmented-label-hover-color;
    }

    &:hover,
    &:focus {
      color: @segmented-label-hover-color;
    }

    &-label {
      min-height: @input-height-base - @segmented-container-padding * 2;
      padding: 0 @input-padding-horizontal-base;
      line-height: @input-height-base - @segmented-container-padding * 2;
      .segmented-text-ellipsis();
    }

    // syntactic sugar to add `icon` for Segmented Item
    &-icon + * {
      margin-left: (@margin-sm / 2);
    }

    &-input {
      position: absolute;
      top: 0;
      left: 0;
      width: 0;
      height: 0;
      opacity: 0;
      pointer-events: none;
    }
  }

  // size styles
  &&-lg &-item-label {
    min-height: @input-height-lg - @segmented-container-padding * 2;
    padding: 0 @input-padding-horizontal-lg;
    font-size: @font-size-lg;
    line-height: @input-height-lg - @segmented-container-padding * 2;
  }

  &&-sm &-item-label {
    min-height: @input-height-sm - @segmented-container-padding * 2;
    padding: 0 @input-padding-horizontal-sm;
    line-height: @input-height-sm - @segmented-container-padding * 2;
  }

  // disabled styles
  &-item-disabled {
    .segmented-disabled-item();
  }

  // thumb styles
  &-thumb {
    .segmented-item-selected();

    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    padding: 4px 0;
  }

  // transition effect when `appear-active`
  &-thumb-motion-appear-active {
    transition: transform 0.3s @ease-in-out, width 0.3s @ease-in-out;
    will-change: transform, width;
  }
}

@import './rtl';
