import{d as t}from"../../chunks/helper-6d386307.js";import{p as e}from"../../chunks/EventType-63cda6c3.js";class s{constructor(t,e,s){this.openDelay=t,this.closeDelay=e,this.dotNetHelper=s}clearDelay(){clearTimeout(this.openTimeout),clearTimeout(this.closeTimeout)}runDelay(t,e){this.clearDelay();const s=parseInt(this[`${t}Delay`],10);this[`${t}Timeout`]=setTimeout(e||(()=>{const e={open:!0,close:!1}[t];this.setActive(e)}),s)}setActive(t){this.isActive!=t&&(this.isActive=t,this.dotNetHelper.invokeMethodAsync("SetActive",this.isActive))}resetDelay(t,e){this.openDelay=t,this.closeDelay=e}}class i extends s{constructor(e,s,i,o,n,r,c,a,h){super(c,a,h),this.activatorListeners={},this.popupListeners={},this.isActive=e,this.activator=t(s),this.disabled=i,this.openOnClick=o,this.openOnHover=n,this.openOnFocus=r,this.dotNetHelper=h}resetActivator(t){const e=document.querySelector(t);e&&(this.activator=e),this.resetActivatorEvents(this.disabled,this.openOnHover,this.openOnFocus)}addActivatorEvents(){if(!this.activator||this.disabled)return;this.activatorListeners=this.genActivatorListeners();const t=Object.keys(this.activatorListeners);for(const e of t)this.activator.addEventListener(e,this.activatorListeners[e])}genActivatorListeners(){if(this.disabled)return{};const t={};return this.openOnHover?(t.mouseenter=t=>{this.runDelay("open")},t.mouseleave=t=>{this.runDelay("close")}):this.openOnClick&&(t.click=t=>{var s,i;this.activator&&this.activator.focus();const o=t.composedPath().find((t=>t===this.activator));(null===(i=null===(s=null==o?void 0:o._blazorEvents_1)||void 0===s?void 0:s.handlers)||void 0===i?void 0:i.click)||t.stopPropagation(),this.dotNetHelper.invokeMethodAsync("OnClick",e(t)),this.setActive(!this.isActive)}),this.openOnFocus&&(t.focus=t=>{var e,s;const i=t.composedPath().find((t=>t===this.activator));(null===(s=null===(e=null==i?void 0:i._blazorEvents_1)||void 0===e?void 0:e.handlers)||void 0===s?void 0:s.focus)||t.stopPropagation(),this.runDelay("open")},t.blur=t=>{this.runDelay("close")}),t}removeActivatorEvents(){if(!this.activator)return;const t=Object.keys(this.activatorListeners);for(const e of t)this.activator.removeEventListener(e,this.activatorListeners[e]);this.activatorListeners={}}resetActivatorEvents(t,e,s){this.disabled=t,this.openOnHover=e,this.openOnFocus=s,this.removeActivatorEvents(),this.addActivatorEvents()}runDelaying(t){this.runDelay(t?"open":"close")}registerPopup(t,e){const s=document.querySelector(t);s?(this.popupElement=s,this.closeOnContentClick=e,this.addPopupEvents()):console.error("popup not exists")}addPopupEvents(){if(!this.popupElement||this.disabled)return;this.popupListeners=this.genPopupListeners();const t=Object.keys(this.popupListeners);for(const e of t)this.popupElement.addEventListener(e,this.popupListeners[e])}removePopupEvents(){if(!this.popupElement)return;const t=Object.keys(this.popupListeners);for(const e of t)this.popupElement.removeEventListener(e,this.popupListeners[e]);this.popupListeners={}}genPopupListeners(){if(this.disabled)return;const t={};return!this.disabled&&this.openOnHover&&(t.mouseenter=t=>{this.runDelay("open")},t.mouseleave=t=>{this.runDelay("close")}),this.closeOnContentClick&&(t.click=t=>{this.setActive(!1)}),t}resetPopupEvents(t){this.closeOnContentClick=t,this.removePopupEvents(),this.addPopupEvents()}}function o(t,e,s,o,n,r,c,a,h){var p=new i(t,e,s,o,n,r,c,a,h);return p.addActivatorEvents(),p}export{o as init};
//# sourceMappingURL=index-82cb7376.js.map
