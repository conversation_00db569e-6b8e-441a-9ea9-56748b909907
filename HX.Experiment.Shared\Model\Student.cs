﻿using UFU.CoreFX.Models;

namespace HX.Experiment.Shared.Model;

[DataEntity("2507280500000001")]
public class StudentInfo
{
    /// <summary>
    /// 学生对应的登录的用户ID
    /// </summary>
    public string UserId { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 身份证ID或者学号ID
    /// </summary>
    public string CardId { get; set; }

    /// <summary>
    /// 班级ID
    /// </summary>
    public string ClassId { get; set; }
}