﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<!--<Nullable>enable</Nullable>-->
		<ImplicitUsings>enable</ImplicitUsings>
		<GenerateDocumentationFile>True</GenerateDocumentationFile>
		<AssemblyName>HX.Experiment.Web.Service</AssemblyName>
		<RootNamespace>HX.Experiment.Web.Service</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.*" />
		<PackageReference Include="UFU.CoreFX" Version="5.0.3" />
	</ItemGroup>

	<ItemGroup>
	  <Content Update="wwwroot\css\hx.hrv.css">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </Content>
	  <Content Include="..\.dockerignore">
	    <Link>.dockerignore</Link>
	    <DependentUpon>Dockerfile</DependentUpon>
	  </Content>
	</ItemGroup>

	<ItemGroup>
	  <None Update="Dockerfile">
	    <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\..\..\..\NetCore\UFU.CoreFX.Blazor\UFU.CoreFX.Shared\UFU.CoreFX.Shared.csproj" />
	  <ProjectReference Include="..\HX.Experiment.Web.Client\HX.Experiment.Web.Client.csproj" />
	</ItemGroup>
</Project>
