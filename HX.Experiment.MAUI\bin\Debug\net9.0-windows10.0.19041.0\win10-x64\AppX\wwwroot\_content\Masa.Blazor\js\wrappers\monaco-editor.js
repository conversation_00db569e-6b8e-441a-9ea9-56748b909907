function n(n,e,o){const t=monaco.editor.create(n,e);return t.onDidChangeModelContent((()=>{o.invokeMethodAsync("OnChange",t.getValue())})),t}function e(n,e){monaco.editor.defineTheme(n,e)}function o(){monaco.editor.remeasureFonts()}function t(n){monaco.editor.addKeybindingRules(n)}function i(n){monaco.editor.addKeybindingRule(n)}function c(n){monaco.editor.setTheme(n)}function d(n,e){monaco.editor.colorizeElement(document.getElementById(n),e)}function u(){return monaco.editor.getModels()}function a(n,e){n.updateOptions(e)}function r(n,e,o,t){n.addCommand(e,(function(){o.invokeMethodAsync(t)}))}function m(n){return n.getModel()}function f(n){return n.getValue()}function l(n,e){n.setValue(e)}function g(n,e){monaco.editor.setModelLanguage(n.getModel(),e)}export{r as addCommand,i as addKeybindingRule,t as addKeybindingRules,d as colorizeElement,e as defineTheme,m as getModel,u as getModels,f as getValue,n as init,o as remeasureFonts,g as setModelLanguage,c as setTheme,l as setValue,a as updateOptions};
//# sourceMappingURL=monaco-editor.js.map
