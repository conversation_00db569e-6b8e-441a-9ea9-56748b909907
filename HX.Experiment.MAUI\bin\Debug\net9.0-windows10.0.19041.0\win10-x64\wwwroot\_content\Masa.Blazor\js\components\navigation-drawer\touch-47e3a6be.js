import{u as t}from"../../chunks/touch-5a32c5ea.js";import{a as o}from"../../chunks/helper-6d386307.js";import"../../chunks/tslib.es6-68144fbe.js";function n(n,i,s){var c,r;const l=o(n);if(!l)return console.warn("The root element of MNavigationDrawer component is not found, touching to open/close will not work."),{};window.addEventListener("touchstart",M,{passive:!0}),window.addEventListener("touchmove",T,{passive:!1}),window.addEventListener("touchend",x,{passive:!0});const d=["left","right"].includes(s.position),{addMovement:u,endTouch:h,getVelocity:a}=t();let m,p=!1,f=!1,v=0,w=0,g=null!==(c=l.style.transform)&&void 0!==c?c:null,y=null!==(r=l.style.transition)&&void 0!==r?r:null;function E(t,o){return("left"===s.position?t:"right"===s.position?document.documentElement.clientWidth-t:"top"===s.position?t:"bottom"===s.position?document.documentElement.clientHeight-t:e())-(o?s.width:0)}function b(t,o=!0){const n="left"===s.position?(t-w)/s.width:"right"===s.position?(document.documentElement.clientWidth-t-w)/s.width:"top"===s.position?(t-w)/s.width:"bottom"===s.position?(document.documentElement.clientHeight-t-w)/s.width:e();return o?Math.max(0,Math.min(1,n)):n}function M(t){var o,n;if(s.touchless)return;g=null!==(o=l.style.transform)&&void 0!==o?o:null,y=null!==(n=l.style.transition)&&void 0!==n?n:null;const i=t.changedTouches[0].clientX,c=t.changedTouches[0].clientY,r="left"===s.position?i<25:"right"===s.position?i>document.documentElement.clientWidth-25:"top"===s.position?c<25:"bottom"===s.position?c>document.documentElement.clientHeight-25:e(),a=s.isActive&&("left"===s.position?i<s.width:"right"===s.position?i>document.documentElement.clientWidth-s.width:"top"===s.position?c<s.width:"bottom"===s.position?c>document.documentElement.clientHeight-s.width:e());(r||a||s.isActive&&s.isTemporary)&&(m=[i,c],w=E(d?i:c,s.isActive),v=b(d?i:c),p=w>-20&&w<80,h(t),u(t))}function T(t){if(s.touchless)return;const o=t.changedTouches[0].clientX,n=t.changedTouches[0].clientY;if(p){if(!t.cancelable)return void(p=!1);const e=Math.abs(o-m[0]),i=Math.abs(n-m[1]);(d?e>i&&e>3:i>e&&i>3)?(f=!0,p=!1):(d?i:e)>3&&(p=!1)}if(A(),!f)return;t.preventDefault(),u(t);const e=b(d?o:n,!1);v=Math.max(0,Math.min(1,e)),e>1?w=E(d?o:n,!0):e<0&&(w=E(d?o:n,!1)),i.invokeMethodAsync("TouchMove",f,v)}function x(t){if(p=!1,s.touchless||!f)return;u(t),f=!1,A();const o=a(t.changedTouches[0].identifier),n=Math.abs(o.x),c=Math.abs(o.y),r=d?n>c&&n>400:c>n&&c>3;s.isActive=r?o.direction===({left:"right",right:"left",top:"down",bottom:"up"}[s.position]||e()):v>.5,i.invokeMethodAsync("TouchEnd",s.isActive)}const A=()=>{const t=f?{transform:"left"===s.position?`translateX(calc(-100% + ${v*s.width}px))`:"right"===s.position?`translateX(calc(100% - ${v*s.width}px))`:"top"===s.position?`translateY(calc(-100% + ${v*s.width}px))`:"bottom"===s.position?`translateY(calc(100% - ${v*s.width}px))`:e(),transition:"none"}:void 0;f?(l.style.setProperty("transform",(null==t?void 0:t.transform)||"none"),l.style.setProperty("transition",(null==t?void 0:t.transition)||null)):(l.style.setProperty("transform",g),l.style.setProperty("transition",y))};return{syncState:t=>{s=t},dispose:()=>{i.dispose(),window.removeEventListener("touchstart",M),window.removeEventListener("touchmove",T),window.removeEventListener("touchend",x)}}}function e(){throw new Error}export{n as useTouch};
//# sourceMappingURL=touch-47e3a6be.js.map
