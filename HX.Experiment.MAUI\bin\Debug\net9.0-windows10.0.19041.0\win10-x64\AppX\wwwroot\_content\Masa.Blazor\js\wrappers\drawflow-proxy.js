class e{constructor(e,t,o){this.mousedown=new MouseEvent("mousedown",{view:window,bubbles:!0,cancelable:!1}),this.mouseup=new MouseEvent("mouseup",{view:window,bubbles:!0,cancelable:!1});var i=document.querySelector(e);this.editor=new Drawflow(i),this.editor.start(),this.editor.editor_mode=o,this.editor.on("nodeCreated",(function(e){t.invokeMethodAsync("OnNodeCreated",e.toString())})),this.editor.on("nodeRemoved",(function(e){t.invokeMethodAsync("OnNodeRemoved",e.toString())})),this.editor.on("nodeSelected",(function(e){t.invokeMethodAsync("OnNodeSelected",e.toString())})),this.editor.on("nodeUnselected",(function(e){t.invokeMethodAsync("OnNodeUnselected",e.toString())})),this.editor.on("nodeDataChanged",(function(e){t.invokeMethodAsync("OnNodeDataChanged",e.toString())})),this.editor.on("import",(function(e){t.invokeMethodAsync("OnImport")}))}setMode(e){this.editor.editor_mode=e}addNode(e,t,o,i,d,n,r,s,a={},h){if("fixed"==this.editor.editor_mode)return null;const c=i*(this.editor.precanvas.clientWidth/(this.editor.precanvas.clientWidth*this.editor.zoom))-this.editor.precanvas.getBoundingClientRect().x*(this.editor.precanvas.clientWidth/(this.editor.precanvas.clientWidth*this.editor.zoom))-n,l=d*(this.editor.precanvas.clientHeight/(this.editor.precanvas.clientHeight*this.editor.zoom))-this.editor.precanvas.getBoundingClientRect().y*(this.editor.precanvas.clientHeight/(this.editor.precanvas.clientHeight*this.editor.zoom))-r;return this.editor.addNode(e,t,o,c,l,s,a,h,!1).toString()}removeNodeId(e){this.editor.removeNodeId(e)}getNodeFromId(e){const t=this.editor.getNodeFromId(e);return t.id=t.id.toString(),t}updateNodeDataFromId(e,t){this.editor.updateNodeDataFromId(e,t),this.editor.dispatch("nodeDataChanged",e)}updateNodeHtml(e,t){this.editor.drawflow.drawflow.Home.data[e].html=t}addNodeInput(e){this.editor.addNodeInput(e)}addNodeOutput(e){this.editor.addNodeOutput(e)}removeNodeInput(e,t){this.editor.removeNodeInput(e,t)}removeNodeOutput(e,t){this.editor.removeNodeOutput(e,t)}updateConnectionNodes(e){this.editor.updateConnectionNodes(e)}removeConnectionNodeId(e){this.editor.removeConnectionNodeId(e)}clear(){this.editor.clear()}export(e=!1){const t=this.editor.export();return JSON.stringify(t,null,e?2:null)}import(e){const t=JSON.parse(e);this.editor.import(t)}focusNode(e){document.querySelector(`#node-${e} .drawflow_content_node`).dispatchEvent(this.mousedown),document.querySelector(`#node-${e} .drawflow_content_node`).dispatchEvent(this.mouseup)}centerNode(e,t){const o=document.getElementById(`node-${e}`),i=this.editor.drawflow.drawflow.Home.data[e].pos_x,d=this.editor.drawflow.drawflow.Home.data[e].pos_y,n=o.clientWidth,r=o.clientHeight,s=this.editor.precanvas.clientWidth/2-i-n/2,a=this.editor.precanvas.clientHeight/2-d-r/2,h=this.editor.zoom;if(this.setTranslate(s,a,h),t){const e=50,t=500;o.style.transition=`all ${t/1e3}s ease 0s`,window.setTimeout((()=>{o.style.transform="scale(1.1)"}),e),window.setTimeout((()=>{o.style.transform="scale(1.0)"}),e+t),window.setTimeout((()=>{o.style.transition="",o.style.transform=""}),e+2*t)}this.focusNode(e)}setTranslate(e,t,o){this.editor.canvas_x=e,this.editor.canvas_y=t;let i=o;this.editor.zoom=1,this.editor.precanvas.style.transform="translate("+this.editor.canvas_x+"px, "+this.editor.canvas_y+"px) scale("+this.editor.zoom+")",this.editor.zoom=i,this.editor.zoom_last_value=1,this.editor.zoom_refresh()}}function t(t,o,i="edit"){return new e(t,o,i)}export{t as init};
//# sourceMappingURL=drawflow-proxy.js.map
