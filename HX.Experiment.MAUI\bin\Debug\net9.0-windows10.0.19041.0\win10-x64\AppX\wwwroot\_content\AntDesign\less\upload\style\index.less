@import '../../style/themes/index';
@import '../../style/mixins/index';

@upload-prefix-cls: ~'@{ant-prefix}-upload';
@upload-item: ~'@{ant-prefix}-upload-list-item';
@upload-picture-card-size: 104px;
@upload-picture-card-border-style: @border-style-base;

.@{upload-prefix-cls} {
  .reset-component();

  outline: 0;

  p {
    margin: 0;
  }

  &-btn {
    display: block;
    width: 100%;
    outline: none;
  }

  input[type='file'] {
    cursor: pointer;
  }

  &&-select {
    display: inline-block;
  }

  &&-disabled {
    color: @disabled-color;
    cursor: not-allowed;
  }

  &&-select-picture-card {
    width: @upload-picture-card-size;
    height: @upload-picture-card-size;
    margin-right: 8px;
    margin-bottom: 8px;
    text-align: center;
    vertical-align: top;
    background-color: @background-color-light;
    border: @border-width-base dashed @border-color-base;
    border-radius: @border-radius-base;
    cursor: pointer;
    transition: border-color 0.3s;

    > .@{upload-prefix-cls} {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      text-align: center;
    }

    &:hover {
      border-color: @primary-color;
      .@{upload-prefix-cls}-disabled& {
        border-color: @border-color-base;
      }
    }
  }

  &&-drag {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    background: @background-color-light;
    border: @border-width-base dashed @border-color-base;
    border-radius: @border-radius-base;
    cursor: pointer;
    transition: border-color 0.3s;

    .@{upload-prefix-cls} {
      padding: @padding-md 0;
    }

    &.@{upload-prefix-cls}-drag-hover:not(.@{upload-prefix-cls}-disabled) {
      border-color: @primary-7;
    }

    &.@{upload-prefix-cls}-disabled {
      cursor: not-allowed;
    }

    .@{upload-prefix-cls}-btn {
      display: table;
      height: 100%;
    }

    .@{upload-prefix-cls}-drag-container {
      display: table-cell;
      vertical-align: middle;
    }

    &:not(.@{upload-prefix-cls}-disabled):hover {
      border-color: @primary-5;
    }

    p.@{upload-prefix-cls}-drag-icon {
      .@{iconfont-css-prefix} {
        color: @primary-5;
        font-size: 48px;
      }

      margin-bottom: 20px;
    }
    p.@{upload-prefix-cls}-text {
      margin: 0 0 4px;
      color: @heading-color;
      font-size: @font-size-lg;
    }
    p.@{upload-prefix-cls}-hint {
      color: @text-color-secondary;
      font-size: @font-size-base;
    }

    .@{iconfont-css-prefix}-plus {
      color: @disabled-color;
      font-size: 30px;
      transition: all 0.3s;

      &:hover {
        color: @text-color-secondary;
      }
    }
    &:hover .@{iconfont-css-prefix}-plus {
      color: @text-color-secondary;
    }
  }

  &-picture-card-wrapper {
    .clearfix();

    display: inline-block;
    width: 100%;
  }
}

.@{upload-prefix-cls}-list {
  .reset-component();
  .clearfix();
  line-height: @line-height-base;

  // ============================ Item ============================
  &-item {
    position: relative;
    height: @line-height-base * @font-size-base;
    margin-top: @margin-xs;
    font-size: @font-size-base;

    &-name {
      display: inline-block;
      width: 100%;
      padding-left: @font-size-base + 8px;
      overflow: hidden;
      line-height: @line-height-base;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &-card-actions {
      position: absolute;
      right: 0;

      &-btn {
        opacity: 0;
      }

      &-btn.@{ant-prefix}-btn-sm {
        height: @line-height-base * @font-size-base;
        line-height: 1;
        vertical-align: top;
      }

      &.picture {
        top: 22px;
        line-height: 0;
      }

      &-btn:focus,
      &.picture &-btn {
        opacity: 1;
      }

      .@{iconfont-css-prefix} {
        color: @upload-actions-color;
        transition: all 0.3s;
      }

      &:hover .@{iconfont-css-prefix} {
        color: @text-color;
      }
    }

    &-info {
      height: 100%;
      transition: background-color 0.3s;

      > span {
        display: block;
        width: 100%;
        height: 100%;
      }

      .@{iconfont-css-prefix}-loading,
      .@{upload-prefix-cls}-text-icon {
        .@{iconfont-css-prefix} {
          position: absolute;
          top: (@font-size-base / 2) - 2px;
          color: @text-color-secondary;
          font-size: @font-size-base;
        }
      }
    }

    &:hover &-info {
      background-color: @item-hover-bg;
    }

    &:hover &-card-actions-btn {
      opacity: 1;
    }

    &-error,
    &-error .@{upload-prefix-cls}-text-icon > .@{iconfont-css-prefix},
    &-error &-name {
      color: @error-color;
    }

    &-error &-card-actions {
      .@{iconfont-css-prefix} {
        color: @error-color;
      }

      &-btn {
        opacity: 1;
      }
    }

    &-progress {
      position: absolute;
      bottom: -12px;
      width: 100%;
      padding-left: @font-size-base + 12px;
      font-size: @font-size-base;
      line-height: 0;
    }
  }

  // =================== Picture & Picture Card ===================
  &-picture,
  &-picture-card {
    .@{upload-item} {
      position: relative;
      height: 66px;
      padding: @padding-xs;
      border: @border-width-base @upload-picture-card-border-style @border-color-base;
      border-radius: @border-radius-base;

      &:hover {
        background: transparent;
      }

      &-error {
        border-color: @error-color;
      }
    }

    .@{upload-item}-info {
      padding: 0;
    }

    .@{upload-item}:hover .@{upload-item}-info {
      background: transparent;
    }

    .@{upload-item}-uploading {
      border-style: dashed;
    }

    .@{upload-item}-thumbnail {
      width: 48px;
      height: 48px;
      line-height: 60px;
      text-align: center;
      opacity: 0.8;

      .@{iconfont-css-prefix} {
        font-size: 26px;
      }
    }

    // Adjust the color of the error icon : https://github.com/ant-design/ant-design/pull/24160
    .@{upload-item}-error .@{upload-item}-thumbnail {
      .@{iconfont-css-prefix} {
        svg path {
          &[fill='#e6f7ff'] {
            fill: @error-color-deprecated-bg;
          }

          &[fill='#1890ff'] {
            fill: @error-color;
          }
        }
      }
    }

    .@{upload-item}-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      font-size: 26px;
      transform: translate(-50%, -50%);

      .@{iconfont-css-prefix} {
        font-size: 26px;
      }
    }

    .@{upload-item}-image {
      max-width: 100%;
    }

    .@{upload-item}-thumbnail img {
      display: block;
      width: 48px;
      height: 48px;
      overflow: hidden;
    }

    .@{upload-item}-name {
      display: inline-block;
      box-sizing: border-box;
      max-width: 100%;
      margin: 0 0 0 8px;
      padding-right: 8px;
      padding-left: 48px;
      overflow: hidden;
      line-height: 44px;
      white-space: nowrap;
      text-overflow: ellipsis;
      transition: all 0.3s;
    }

    .@{upload-item}-uploading .@{upload-item}-name {
      margin-bottom: 12px;
    }

    .@{upload-item}-progress {
      bottom: 14px;
      width: ~'calc(100% - 24px)';
      margin-top: 0;
      padding-left: 56px;
    }
  }

  // ======================== Picture Card ========================
  &-picture-card {
    &-container {
      display: inline-block;
      width: @upload-picture-card-size;
      height: @upload-picture-card-size;
      margin: 0 @margin-xs @margin-xs 0;
      vertical-align: top;
    }

    .@{upload-item} {
      height: 100%;
      margin: 0;
    }

    .@{upload-item}-info {
      position: relative;
      height: 100%;
      overflow: hidden;

      &::before {
        position: absolute;
        z-index: 1;
        width: 100%;
        height: 100%;
        background-color: fade(@black, 50%);
        opacity: 0;
        transition: all 0.3s;
        content: ' ';
      }
    }

    .@{upload-item}:hover .@{upload-item}-info::before {
      opacity: 1;
    }

    .@{upload-item}-actions {
      position: absolute;
      top: 50%;
      left: 50%;
      z-index: 10;
      white-space: nowrap;
      transform: translate(-50%, -50%);
      opacity: 0;
      transition: all 0.3s;

      .@{iconfont-css-prefix}-eye,
      .@{iconfont-css-prefix}-download,
      .@{iconfont-css-prefix}-delete {
        z-index: 10;
        width: 16px;
        margin: 0 4px;
        color: @text-color-dark;
        font-size: 16px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          color: @text-color-inverse;
        }
      }
    }

    .@{upload-item}-info:hover + .@{upload-item}-actions,
    .@{upload-item}-actions:hover {
      opacity: 1;
    }

    .@{upload-item}-thumbnail,
    .@{upload-item}-thumbnail img {
      position: static;
      display: block;
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .@{upload-item}-name {
      display: none;
      margin: 8px 0 0;
      padding: 0;
      line-height: @line-height-base;
      text-align: center;
    }

    .@{upload-item}-file + .@{upload-item}-name {
      position: absolute;
      bottom: 10px;
      display: block;
    }

    .@{upload-item}-uploading {
      &.@{upload-item} {
        background-color: @background-color-light;
      }

      .@{upload-item}-info {
        height: auto;

        &::before,
        .@{iconfont-css-prefix}-eye,
        .@{iconfont-css-prefix}-delete {
          display: none;
        }
      }
    }

    .@{upload-item}-progress {
      bottom: 32px;
      width: calc(100% - 14px);
      padding-left: 0;
    }
  }

  // ======================= Picture & Text =======================
  &-text,
  &-picture {
    &-container {
      transition: opacity @animation-duration-slow, height @animation-duration-slow;

      &::before {
        display: table;
        width: 0;
        height: 0;
        content: '';
      }

      // Don't know why span here, just stretch it
      .@{upload-prefix-cls}-span {
        display: block;
        flex: auto;
      }
    }

    // text & picture no need this additional element.
    // But it used for picture-card, let's keep it.
    .@{upload-prefix-cls}-span {
      display: flex;
      align-items: center;

      > * {
        flex: none;
      }
    }

    .@{upload-item}-name {
      flex: auto;
      margin: 0;
      padding: 0 @padding-xs;
    }

    .@{upload-item}-card-actions {
      position: static;
    }
  }

  // ============================ Text ============================
  &-text {
    .@{upload-prefix-cls}-text-icon {
      .@{iconfont-css-prefix} {
        position: static;
      }
    }
  }

  // =========================== Motion ===========================
  .@{upload-prefix-cls}-animate-inline-appear,
  .@{upload-prefix-cls}-animate-inline-enter,
  .@{upload-prefix-cls}-animate-inline-leave {
    animation-duration: @animation-duration-slow;
    animation-timing-function: @ease-in-out-circ;
    animation-fill-mode: forwards;
  }

  .@{upload-prefix-cls}-animate-inline-appear,
  .@{upload-prefix-cls}-animate-inline-enter {
    animation-name: uploadAnimateInlineIn;
  }

  .@{upload-prefix-cls}-animate-inline-leave {
    animation-name: uploadAnimateInlineOut;
  }
}

@keyframes uploadAnimateInlineIn {
  from {
    width: 0;
    height: 0;
    margin: 0;
    padding: 0;
    opacity: 0;
  }
}

@keyframes uploadAnimateInlineOut {
  to {
    width: 0;
    height: 0;
    margin: 0;
    padding: 0;
    opacity: 0;
  }
}

@import './rtl';
