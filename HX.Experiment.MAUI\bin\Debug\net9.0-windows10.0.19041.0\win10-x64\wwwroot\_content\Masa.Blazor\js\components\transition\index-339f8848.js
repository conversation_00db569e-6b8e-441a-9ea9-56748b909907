import{b as n}from"../../chunks/helper-6d386307.js";class e{constructor(e,t){this._onTransitionEnd=e=>{const t=this._getTransitionLeaveEnter(e);t&&this.handle&&this.handle.invokeMethodAsync("OnTransitionEnd",n(e.target),"leave"==t?0:1)},this._onTransitionCancel=e=>{const t=this._getTransitionLeaveEnter(e);t&&this.handle&&this.handle.invokeMethodAsync("OnTransitionCancel",n(e.target),"leave"==t?0:1)},this.handle=t,this.el=e,this.el.addEventListener("transitionend",this._onTransitionEnd),this.el.addEventListener("transitioncancel",this._onTransitionCancel)}_getTransitionLeaveEnter(n){const e=n.target.className.split(" ");return e.some((n=>n.includes("transition-leave")))?"leave":e.some((n=>n.includes("transition-enter")))?"enter":void 0}reset(n){this.el.removeEventListener("transitionend",this._onTransitionEnd),this.el.removeEventListener("transitioncancel",this._onTransitionCancel),this.el=n,this.el.addEventListener("transitionend",this._onTransitionEnd),this.el.addEventListener("transitioncancel",this._onTransitionCancel)}dispose(){this.el.removeEventListener("transitionend",this._onTransitionEnd),this.el.removeEventListener("transitioncancel",this._onTransitionCancel),delete t[this.handle._id],this.handle.dispose(),this.handle=null}}let t={number:e};function i(n,i){let s;if(s="string"==typeof n?document.querySelector(n):n,!s||!i)return null;const r=i._id;if(t[r])return t[r];const o=new e(s,i);return t[r]=o,o}export{i as init};
//# sourceMappingURL=index-339f8848.js.map
