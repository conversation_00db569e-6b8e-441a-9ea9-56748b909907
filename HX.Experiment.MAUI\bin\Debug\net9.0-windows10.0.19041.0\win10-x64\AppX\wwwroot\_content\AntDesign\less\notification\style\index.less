@import '../../style/themes/index';
@import '../../style/mixins/index';

.popover-customize-bg(@notification-prefix-cls, @popover-background);

@notification-prefix-cls: ~'@{ant-prefix}-notification';
@notification-width: 384px;
@notification-padding: @notification-padding-vertical @notification-padding-horizontal;
@notification-margin-bottom: 16px;
@notification-margin-edge: 24px;

.@{notification-prefix-cls} {
  .reset-component();

  position: fixed;
  z-index: @zindex-notification;
  margin-right: @notification-margin-edge;

  &-close-icon {
    font-size: @font-size-base;
    cursor: pointer;
  }

  &-hook-holder {
    position: relative;
  }

  &-notice {
    position: relative;
    width: @notification-width;
    max-width: ~'calc(100vw - @{notification-margin-edge} * 2)';
    margin-bottom: @notification-margin-bottom;
    margin-left: auto;
    padding: @notification-padding;
    overflow: hidden;
    line-height: @line-height-base;
    word-wrap: break-word;
    background: @notification-bg;
    border-radius: @border-radius-base;
    box-shadow: @shadow-2;

    .@{notification-prefix-cls}-top &,
    .@{notification-prefix-cls}-bottom & {
      margin-right: auto;
      margin-left: auto;
    }

    .@{notification-prefix-cls}-topLeft &,
    .@{notification-prefix-cls}-bottomLeft & {
      margin-right: auto;
      margin-left: 0;
    }

    &-message {
      margin-bottom: 8px;
      color: @heading-color;
      font-size: @font-size-lg;
      line-height: 24px;

      // https://github.com/ant-design/ant-design/issues/5846#issuecomment-296244140
      &-single-line-auto-margin {
        display: block;
        width: ~'calc(@{notification-width} - @{notification-padding-horizontal} * 2 - 24px - 48px - 100%)';
        max-width: 4px;
        background-color: transparent;
        pointer-events: none;

        &::before {
          display: block;
          content: '';
        }
      }
    }

    &-description {
      font-size: @font-size-base;
    }

    &-closable &-message {
      padding-right: 24px;
    }

    &-with-icon &-message {
      margin-bottom: 4px;
      margin-left: 48px;
      font-size: @font-size-lg;
    }

    &-with-icon &-description {
      margin-left: 48px;
      font-size: @font-size-base;
    }

    // Icon & color style in different selector level
    // https://github.com/ant-design/ant-design/issues/16503
    // https://github.com/ant-design/ant-design/issues/15512
    &-icon {
      position: absolute;
      margin-left: 4px;
      font-size: 24px;
      line-height: 24px;
    }

    .@{iconfont-css-prefix}&-icon {
      &-success {
        color: @success-color;
      }

      &-info {
        color: @info-color;
      }

      &-warning {
        color: @warning-color;
      }

      &-error {
        color: @error-color;
      }
    }

    &-close {
      position: absolute;
      top: 16px;
      right: 22px;
      color: @text-color-secondary;
      outline: none;

      &:hover {
        & when (@theme = dark) {
          color: fade(@white, 85%);
        }
        & when not (@theme = dark) {
          color: shade(@text-color-secondary, 40%);
        }
      }
    }

    &-btn {
      float: right;
      margin-top: 16px;
    }
  }

  .notification-fade-effect {
    animation-duration: 0.24s;
    animation-timing-function: @ease-in-out;
    animation-fill-mode: both;
  }

  &-fade-enter,
  &-fade-appear {
    .notification-fade-effect();

    opacity: 0;
    animation-play-state: paused;
  }

  &-fade-leave {
    .notification-fade-effect();

    animation-duration: 0.2s;
    animation-play-state: paused;
  }

  &-fade-enter&-fade-enter-active,
  &-fade-appear&-fade-appear-active {
    animation-name: NotificationFadeIn;
    animation-play-state: running;
  }

  &-fade-leave&-fade-leave-active {
    animation-name: NotificationFadeOut;
    animation-play-state: running;
  }
}

@keyframes NotificationFadeIn {
  0% {
    left: @notification-width;
    opacity: 0;
  }

  100% {
    left: 0;
    opacity: 1;
  }
}

@keyframes NotificationFadeOut {
  0% {
    max-height: 150px;
    margin-bottom: @notification-margin-bottom;
    opacity: 1;
  }

  100% {
    max-height: 0;
    margin-bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
    opacity: 0;
  }
}

@import './rtl';
@import './placement';
