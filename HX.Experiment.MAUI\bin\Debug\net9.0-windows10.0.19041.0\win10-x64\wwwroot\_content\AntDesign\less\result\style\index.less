@import '../../style/themes/index';
@import '../../style/mixins/index';

@result-prefix-cls: ~'@{ant-prefix}-result';

.@{result-prefix-cls} {
  padding: 48px 32px;
  // status color
  &-success &-icon > .@{iconfont-css-prefix} {
    color: @success-color;
  }

  &-error &-icon > .@{iconfont-css-prefix} {
    color: @error-color;
  }

  &-info &-icon > .@{iconfont-css-prefix} {
    color: @info-color;
  }

  &-warning &-icon > .@{iconfont-css-prefix} {
    color: @warning-color;
  }

  // Exception Status image
  &-image {
    width: 250px;
    height: 295px;
    margin: auto;
  }

  &-icon {
    margin-bottom: 24px;
    text-align: center;

    > .@{iconfont-css-prefix} {
      font-size: @result-icon-font-size;
    }
  }

  &-title {
    color: @heading-color;
    font-size: @result-title-font-size;
    line-height: 1.8;
    text-align: center;
  }

  &-subtitle {
    color: @text-color-secondary;
    font-size: @result-subtitle-font-size;
    line-height: 1.6;
    text-align: center;
  }

  &-extra {
    margin: @result-extra-margin;
    text-align: center;

    > * {
      margin-right: 8px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  &-content {
    margin-top: 24px;
    padding: 24px 40px;
    background-color: @background-color-light;
  }
}

@import './rtl';
