import{c as t}from"../../chunks/tslib.es6-68144fbe.js";function o(t,o){const n=t.changedTouches[0];o.touchendX=n.clientX,o.touchendY=n.clientY,(t=>{const{touchstartX:o,touchendX:n,touchstartY:e,touchendY:c,dotnetHelper:s}=t;t.offsetX=n-o,t.offsetY=c-e,Math.abs(t.offsetY)<.5*Math.abs(t.offsetX)&&(n<o-16&&s.invokeMethodAsync("OnTouchend","left"),n>o+16&&s.invokeMethodAsync("OnTouchend","right")),Math.abs(t.offsetX)<.5*Math.abs(t.offsetY)&&(c<e-16&&s.invokeMethodAsync("OnTouchend","up"),c>e+16&&s.invokeMethodAsync("OnTouchend","down"))})(o)}let n=0;function e(e,c,s){var u,a;const h=(null==c?void 0:c.parent)?e.parentElement:e,r=null!==(u=null==c?void 0:c.options)&&void 0!==u?u:{};if(!h)return null;const i=function(t,n){const e={touchstartX:0,touchstartY:0,touchendX:0,touchendY:0,touchmoveX:0,touchmoveY:0,offsetX:0,offsetY:0,dotnetHelper:n};return{touchstart:o=>{var n;(null===(n=t.touchstart)||void 0===n?void 0:n.stopPropagation)&&o.stopPropagation(),function(t,o){const n=t.changedTouches[0];o.touchstartX=n.clientX,o.touchstartY=n.clientY}(o,e)},touchend:n=>{var c;(null===(c=t.touchend)||void 0===c?void 0:c.stopPropagation)&&n.stopPropagation(),o(n,e)},touchmove:o=>{var n;(null===(n=t.touchmove)||void 0===n?void 0:n.stopPropagation)&&o.stopPropagation(),function(t,o){const n=t.changedTouches[0];o.touchmoveX=n.clientX,o.touchmoveY=n.clientY}(o,e)}}}(r,s);return h._touchHandlers=null!==(a=h._touchHandlers)&&void 0!==a?a:Object.create(null),h._touchHandlers[n]=i,Object.keys(i).forEach((o=>{var n;const e=null!==(n=r[o])&&void 0!==n?n:{passive:!0},c=t(e,["stopPropagation"]);h.addEventListener(o,i[o],c)})),n++}function c(t,o){if(!t)return;const n=t._touchHandlers;if(!n||!n[o])return;const e=n[o];Object.keys(e).forEach((o=>{t.removeEventListener(o,e[o])})),delete n[o]}export{c as cleanupTouch,e as useTouch};
//# sourceMappingURL=touch-f9d2ba92.js.map
