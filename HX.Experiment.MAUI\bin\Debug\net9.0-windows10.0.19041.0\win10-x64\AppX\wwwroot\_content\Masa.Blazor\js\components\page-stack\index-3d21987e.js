import{a as e}from"../../chunks/tslib.es6-68144fbe.js";import{f as t}from"../../chunks/index-cef005e4.js";import{c as s}from"../../chunks/helper-6d386307.js";const o={},l={},c={},r={};let n=0;function a(s){const a=n,d=t=>function(t,s){return e(this,void 0,void 0,(function*(){const e=o[t];if(null===e)return;const l=s.target.closest("a");if(!l)return;const c=l.getAttribute("href");if(!c)return;let r=l.getAttribute("data-page-stack-strategy");r=null===r?null:r.toLowerCase(),""!==r&&"true"!==r&&"push"!==r||(document.querySelector("[page-stack-id]")||i(),yield e.invokeMethodAsync("Push",c))}))}(a,t),p=()=>s.invokeMethodAsync("Popstate",window.location.pathname),u=t((()=>{s.invokeMethodAsync("Scroll",window.pageYOffset)}),100);return l[a]=d,c[a]=p,r[a]=u,o[a]=s,document.addEventListener("click",d),window.addEventListener("popstate",p),window.addEventListener("scroll",u,{passive:!0}),n++}function i(){const e=document.documentElement;e.classList.contains("m-page-stack-scroll-blocked")||0===e.scrollLeft&&0===e.scrollTop||(e.style.setProperty("--m-page-stack-scroll-x",s(-e.scrollLeft)),e.style.setProperty("--m-page-stack-scroll-y",s(-e.scrollTop)),e.classList.add("m-page-stack-scroll-blocked"))}function d(){const e=document.documentElement;if(!e.classList.contains("m-page-stack-scroll-blocked"))return;const t=parseFloat(e.style.getPropertyValue("--m-page-stack-scroll-x")),s=parseFloat(e.style.getPropertyValue("--m-page-stack-scroll-y")),o=e.style.scrollBehavior;e.style.scrollBehavior="auto",e.style.removeProperty("--m-page-stack-scroll-x"),e.style.removeProperty("--m-page-stack-scroll-y"),e.classList.remove("m-page-stack-scroll-blocked"),e.scrollLeft=-t,e.scrollTop=-s,e.style.scrollBehavior=o}function p(e){const t=l[e];t&&(document.removeEventListener("click",t),delete l[e]);const s=c[e];s&&(window.removeEventListener("popstate",s),delete c[e]);const n=r[e];n&&(window.removeEventListener("scroll",n,{passive:!0}),delete r[e]),o[e]&&o[e].dispose(),delete o[e]}export{a as attachListener,i as blockScroll,p as detachListener,d as unblockScroll};
//# sourceMappingURL=index-3d21987e.js.map
