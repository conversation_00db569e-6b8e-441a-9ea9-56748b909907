# HX.Experiment 设备详情页新建检测功能

## 功能概述

在 `HX.Experiment\HX.Experiment.Shared\Pages\HXDevice\Detail.razor` 中实现了类似于 `HX.HRV.Shared\Pages\Client\DeviceStatus\DeviceStatus.razor` 的新建检测功能。

## 实现的文件

### 1. Detail.razor
- 添加了"新建检测"按钮
- 当设备在线时显示新建检测按钮
- 集成了新建检测对话框

### 2. Detail.razor.cs
- 添加了 `OpenNewTestDialog()` 方法来打开新建检测对话框
- 添加了 `RefreshData()` 方法来刷新页面数据
- 添加了相关的属性来管理对话框状态

### 3. StudentTestDialog.razor & StudentTestDialog.razor.cs
- 学生信息输入对话框
- 支持身份证号自动完成搜索
- 包含学生姓名、身份证号、班级选择
- 检测时长选择（5、10、15、20、30分钟）
- 表单验证

### 4. StudentDeviceDialog.razor & StudentDeviceDialog.razor.cs
- 设备确认对话框
- 显示学生信息和设备信息
- 确认开始检测功能
- 发送设备命令（查找设备、开始检测）

## 主要功能流程

1. **点击新建检测按钮**
   - 检查设备是否在线
   - 打开学生信息输入对话框

2. **填写学生信息**
   - 输入或搜索学生身份证号
   - 自动填充学生姓名（如果已存在）
   - 选择班级
   - 选择检测时长

3. **确认开始检测**
   - 显示学生和设备信息
   - 确认后创建检测记录
   - 发送设备命令开始检测

## 数据模型更新

### StudentInfo (Student.cs)
- 添加了 `Id` 字段

### ClassInfo (ClassInfo.cs)
- 添加了 `Id` 字段

## API 接口依赖

实现中假设存在以下API接口：

- `GET /api/v2/hx_experiment/Student/List` - 获取学生列表
- `POST /api/v2/hx_experiment/Student/Add` - 添加学生
- `POST /api/v2/hx_experiment/Student/Edit` - 编辑学生
- `GET /api/v2/hx_experiment/Class/List` - 获取班级列表
- `POST /api/v2/hx_experiment/TestRecord/Add` - 创建检测记录
- `POST /api/v2/hx_experiment/Device/SendCommand` - 发送设备命令

## 样式特性

- 使用了与HX.HRV项目相似的UI样式
- 蓝色渐变按钮设计
- 响应式布局
- 表单验证提示

## 注意事项

1. 设备通信部分可能需要根据实际的设备通信协议进行调整
2. API接口路径可能需要根据实际后端实现进行调整
3. 检测记录的数据结构可能需要根据实际需求进行调整
4. 错误处理和用户反馈已经集成，但可能需要根据具体业务需求进行优化

## 与HX.HRV的差异

1. **数据模型**: 使用StudentInfo代替PatientModel
2. **业务逻辑**: 适配实验场景而非医疗场景
3. **API接口**: 使用hx_experiment命名空间
4. **设备管理**: 简化了设备状态管理逻辑
