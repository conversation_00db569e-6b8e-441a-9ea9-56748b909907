@page "/HXDevice/Detail"
@using HX.Experiment.Shared.Model
@using UFU.CoreFX.Models
@using UFU.CoreFX.Shared.Services
@using HX.Experiment.Shared.Pages.HXDevice

<style>
    .new-test-button {
        padding: 1rem;
        font-size: 1.2rem;
        width: 200px;
        text-align: center;
        display: flex;
        border-radius: 10px;
        background: linear-gradient(0deg, #008ef7, #1e9fff);
        color: white;
        flex-direction: column;
        justify-content: center;
        box-shadow: 0px 8px 16px 0px rgb(48 138 239 / 83%);
    }

    .dialog-button.confirm {
        width: 300px;
        background: linear-gradient(0deg, #008ef7, #1e9fff);
        box-shadow: 0px 8px 16px 0px rgb(48 138 239 / 83%);
        border-radius: 28px;
        color: white;
        font-size: 2rem;
    }

    .dialog-button.cancel {
        width: 300px;
        border-radius: 28px;
        box-shadow: 0px 8px 16px 0px rgb(177 213 253 / 83%);
        font-size: 2rem;
    }

    .customer-input .m-input__prepend-outer .m-label {
        width: 140px;
        font-size: 1.25rem;
    }

    .student-dialog .m-text-field__slot {
        font-size: 1.625rem;
    }
</style>

<MContainer>
    <MCard>
        <MCardTitle>设备详情</MCardTitle>
        <MCardText>
          @if (!string.IsNullOrEmpty(ErrorMsg))
            {
                <MAlert Type="AlertTypes.Warning">@ErrorMsg</MAlert>
            }
            else if (DeviceModel!=null&&DeviceModel.IsOnline)
            {
                <MRow>
                    <MCol Cols="12">
                        <div><strong>绑定设备ID:</strong> @DeviceModel.DeviceSN</div>
                        <div><strong>绑定电脑ID:</strong> @DeviceModel.Name</div>
                        <MLabel>@DeviceModel.ChipSN</MLabel>
                    </MCol>
                    <MCol Cols="12" Class="mt-4">
                        <MButton Class="new-test-button" OnClick="OpenNewTestDialog">
                            新建检测
                        </MButton>
                    </MCol>
                </MRow>
            }
            else if (BindInfo != null)
            {
                <MRow>
                    <MCol xs="6">
                        <div><strong>绑定设备ID:</strong> @BindInfo.Data.DeviceId</div>
                        <div><strong>绑定电脑ID:</strong> @BindInfo.Data.ComputerId</div>
                        @if (BindInfo.Data.DeviceModel != null)
                        {
                            <div><strong>设备名称:</strong> @BindInfo.Data.DeviceModel.Name</div>
                        }
                    </MCol>
                </MRow>
            }
            else if (CurrentLinkedDevice != null)
            {
                <MRow>
                    <MCol Cols="12">
                         <h4>已插入设备</h4>
                        <div><strong>设备MAC:</strong> @CurrentLinkedDevice.ShowName</div>
                    </MCol>
                    <MCol Cols="12">
                        <MButton OnClick="HandleBind">去绑定</MButton>
                    </MCol>
                </MRow>
            }
            else
            {
                <MAlert Type="AlertTypes.Warning">请先插入设备</MAlert>
            }
        </MCardText>
    </MCard>
</MContainer>

<!-- 新建检测对话框 -->
<PModal Width="942" Persistent="false" @bind-Value="isShowNewTest">
    <TitleContent>
        <div style="flex-grow: 1;text-align: right;font-size: 1.5rem">新建检测</div>
    </TitleContent>
    <ChildContent>
        <StudentTestDialog
            CloseDialog="async () => { isShowNewTest = false; await RefreshData(); }">
        </StudentTestDialog>
    </ChildContent>
</PModal>

@code {

}