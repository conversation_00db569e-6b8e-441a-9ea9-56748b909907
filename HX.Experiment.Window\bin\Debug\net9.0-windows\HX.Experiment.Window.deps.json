{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"HX.Experiment.Window/1.0.0": {"dependencies": {"DeviceId": "6.9.0", "EPPlus": "7.5.1", "HX.Experiment.Shared": "1.0.0", "Microsoft.AspNetCore.Components.WebView.WindowsForms": "9.0.90", "Microsoft.Extensions.Http": "9.0.8", "OpenCvSharp4.Extensions": "4.11.0.20250507", "OpenCvSharp4.Windows": "4.11.0.20250507", "System.Management": "9.0.7", "Microsoft.Web.WebView2.Core": "1.0.3179.45", "Microsoft.Web.WebView2.WinForms": "1.0.3179.45", "Microsoft.Web.WebView2.Wpf": "1.0.3179.45"}, "runtime": {"HX.Experiment.Window.dll": {}}}, "AntDesign/0.15.5": {"dependencies": {"Microsoft.AspNetCore.Components.DataAnnotations.Validation": "3.2.0-rc1.20223.4", "Microsoft.AspNetCore.Components.Web": "9.0.8", "OneOf": "3.0.223"}, "runtime": {"lib/net7.0/AntDesign.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "BemIt/2.2.0": {"runtime": {"lib/net8.0/BemIt.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.0"}}}, "blazor-dragdrop/2.4.0": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.8", "Microsoft.AspNetCore.Components.Web": "9.0.8", "Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/net6.0/Plk.Blazor.DragDrop.dll": {"assemblyVersion": "2.2.2.0", "fileVersion": "2.2.2.0"}}}, "DeepCloner.Core/0.1.0": {"runtime": {"lib/net8.0/DeepCloner.Core.dll": {"assemblyVersion": "0.1.0.0", "fileVersion": "0.1.0.0"}}}, "DeviceId/6.9.0": {"runtime": {"lib/net9.0/DeviceId.dll": {"assemblyVersion": "6.9.0.0", "fileVersion": "6.9.0.0"}}}, "EPPlus/7.5.1": {"dependencies": {"EPPlus.System.Drawing": "7.5.0", "Microsoft.Extensions.Configuration.Json": "9.0.7", "Microsoft.IO.RecyclableMemoryStream": "3.0.1", "System.ComponentModel.Annotations": "5.0.0", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/net8.0/EPPlus.dll": {"assemblyVersion": "7.5.1.0", "fileVersion": "7.5.1.0"}}}, "EPPlus.Interfaces/7.5.0": {"runtime": {"lib/net8.0/EPPlus.Interfaces.dll": {"assemblyVersion": "7.5.0.0", "fileVersion": "7.5.0.0"}}}, "EPPlus.System.Drawing/7.5.0": {"dependencies": {"EPPlus.Interfaces": "7.5.0", "System.Drawing.Common": "8.0.11"}, "runtime": {"lib/net8.0/EPPlus.System.Drawing.dll": {"assemblyVersion": "7.5.0.0", "fileVersion": "7.5.0.0"}}}, "FluentValidation/11.4.0": {"runtime": {"lib/net7.0/FluentValidation.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.4.0.0"}}}, "FluentValidation.DependencyInjectionExtensions/11.4.0": {"dependencies": {"FluentValidation": "11.4.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8"}, "runtime": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.4.0.0"}}}, "Majorsoft.Blazor.Extensions.BrowserStorage/1.5.0": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "9.0.8"}, "runtime": {"lib/net5.0/Majorsoft.Blazor.Extensions.BrowserStorage.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.5.0.0"}}}, "Masa.Blazor/1.10.3": {"dependencies": {"BemIt": "2.2.0", "DeepCloner.Core": "0.1.0", "FluentValidation": "11.4.0", "FluentValidation.DependencyInjectionExtensions": "11.4.0", "Microsoft.AspNetCore.Components": "9.0.8", "Microsoft.AspNetCore.Components.Authorization": "9.0.7", "Microsoft.AspNetCore.Components.Web": "9.0.8", "OneOf": "3.0.223", "OneOf.SourceGenerator": "3.0.223", "Util.Reflection": "1.0.3"}, "runtime": {"lib/net9.0/Masa.Blazor.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Masa.Blazor.MobileComponents/1.10.2": {"dependencies": {"Masa.Blazor": "1.10.3", "Microsoft.AspNetCore.Components.Web": "9.0.8"}, "runtime": {"lib/net6.0/Masa.Blazor.MobileComponents.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Microsoft.AspNetCore.Authorization/9.0.8": {"dependencies": {"Microsoft.AspNetCore.Metadata": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36808"}}}, "Microsoft.AspNetCore.Components/9.0.8": {"dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.8", "Microsoft.AspNetCore.Components.Analyzers": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36808"}}}, "Microsoft.AspNetCore.Components.Analyzers/9.0.8": {}, "Microsoft.AspNetCore.Components.Authorization/9.0.7": {"dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.8", "Microsoft.AspNetCore.Components": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Authorization.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31702"}}}, "Microsoft.AspNetCore.Components.DataAnnotations.Validation/3.2.0-rc1.20223.4": {"dependencies": {"Microsoft.AspNetCore.Components.Forms": "9.0.8"}, "runtime": {"lib/netstandard2.1/Microsoft.AspNetCore.Components.DataAnnotations.Validation.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.200.20.22304"}}}, "Microsoft.AspNetCore.Components.Forms/9.0.8": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36808"}}}, "Microsoft.AspNetCore.Components.Web/9.0.8": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.8", "Microsoft.AspNetCore.Components.Forms": "9.0.8", "Microsoft.Extensions.DependencyInjection": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8", "Microsoft.JSInterop": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36808"}}}, "Microsoft.AspNetCore.Components.WebAssembly/9.0.7": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "9.0.8", "Microsoft.Extensions.Configuration.Binder": "9.0.8", "Microsoft.Extensions.Configuration.Json": "9.0.7", "Microsoft.Extensions.Logging": "9.0.8", "Microsoft.JSInterop.WebAssembly": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"assemblyVersion": "9.0.7.0", "fileVersion": "9.0.725.31702"}}}, "Microsoft.AspNetCore.Components.WebView/9.0.5": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "9.0.8", "Microsoft.Extensions.Configuration.Binder": "9.0.8", "Microsoft.Extensions.Configuration.Json": "9.0.7", "Microsoft.Extensions.FileProviders.Composite": "9.0.5", "Microsoft.Extensions.FileProviders.Embedded": "9.0.5", "Microsoft.Extensions.Logging": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.WebView.dll": {"assemblyVersion": "9.0.5.0", "fileVersion": "9.0.525.22904"}}}, "Microsoft.AspNetCore.Components.WebView.WindowsForms/9.0.90": {"dependencies": {"Microsoft.AspNetCore.Components.WebView": "9.0.5", "Microsoft.Web.WebView2": "1.0.3179.45"}, "runtime": {"lib/net9.0-windows7.0/Microsoft.AspNetCore.Components.WebView.WindowsForms.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "9.0.9025.36106"}}}, "Microsoft.AspNetCore.Metadata/9.0.8": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36808"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Extensions.Configuration/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.7", "Microsoft.Extensions.FileProviders.Physical": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration.Json/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.7", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.DependencyInjection/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Diagnostics/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.8", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.FileProviders.Composite/9.0.5": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Composite.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.FileProviders.Embedded/9.0.5": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.525.22904"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.7": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.7", "Microsoft.Extensions.FileSystemGlobbing": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.7": {"runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Http/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Diagnostics": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Options/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.Binder": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Primitives/9.0.8": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36511"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.1.0"}}}, "Microsoft.JSInterop/9.0.8": {"runtime": {"lib/net9.0/Microsoft.JSInterop.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.825.36808"}}}, "Microsoft.JSInterop.WebAssembly/9.0.7": {"dependencies": {"Microsoft.JSInterop": "9.0.8"}, "runtime": {"lib/net9.0/Microsoft.JSInterop.WebAssembly.dll": {"assemblyVersion": "9.0.7.0", "fileVersion": "9.0.725.31702"}}}, "Microsoft.Web.WebView2/1.0.3179.45": {"runtimeTargets": {"runtimes/win-arm64/native/WebView2Loader.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.0.3179.45"}, "runtimes/win-x64/native/WebView2Loader.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.0.3179.45"}, "runtimes/win-x86/native/WebView2Loader.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.0.3179.45"}}}, "Microsoft.Win32.SystemEvents/8.0.0": {}, "OneOf/3.0.223": {"runtime": {"lib/netstandard2.0/OneOf.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "OneOf.SourceGenerator/3.0.223": {}, "OpenCvSharp4/4.11.0.20250507": {"dependencies": {"System.Memory": "4.6.3"}, "runtime": {"lib/net6.0/OpenCvSharp.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "OpenCvSharp4.Extensions/4.11.0.20250507": {"dependencies": {"OpenCvSharp4": "4.11.0.20250507", "System.Drawing.Common": "8.0.11"}, "runtime": {"lib/net6.0/OpenCvSharp.Extensions.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "OpenCvSharp4.runtime.win/4.11.0.20250507": {"runtimeTargets": {"runtimes/win-x64/native/OpenCvSharpExtern.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/opencv_videoio_ffmpeg4110_64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "2024.12.0.0"}, "runtimes/win-x86/native/OpenCvSharpExtern.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/opencv_videoio_ffmpeg4110.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "2024.12.0.0"}}}, "OpenCvSharp4.Windows/4.11.0.20250507": {"dependencies": {"OpenCvSharp4": "4.11.0.20250507", "OpenCvSharp4.runtime.win": "4.11.0.20250507"}}, "System.CodeDom/9.0.7": {}, "System.ComponentModel.Annotations/5.0.0": {}, "System.Drawing.Common/8.0.11": {"dependencies": {"Microsoft.Win32.SystemEvents": "8.0.0"}}, "System.Management/9.0.7": {"dependencies": {"System.CodeDom": "9.0.7"}, "runtime": {"lib/net9.0/System.Management.dll": {"assemblyVersion": "9.0.0.7", "fileVersion": "9.0.725.31616"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.7", "fileVersion": "9.0.725.31616"}}}, "System.Memory/4.6.3": {}, "System.Security.Cryptography.Pkcs/8.0.1": {}, "System.Text.Encoding.CodePages/8.0.0": {}, "System.Text.Json/9.0.7": {}, "Util.Reflection/1.0.3": {"runtime": {"lib/net6.0/Util.Reflection.dll": {"assemblyVersion": "1.0.3.0", "fileVersion": "1.0.3.0"}}}, "HX.Experiment.Shared/1.0.0": {"dependencies": {"Masa.Blazor": "1.10.3", "Masa.Blazor.MobileComponents": "1.10.2", "Microsoft.AspNetCore.Components.Web": "9.0.8", "UFU.CoreFX.Shared": "5.0.6", "UFU.IoT.Shared": "1.0.0"}, "runtime": {"HX.Experiment.Shared.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "UFU.CoreFX.Shared/5.0.6": {"dependencies": {"AntDesign": "0.15.5", "Majorsoft.Blazor.Extensions.BrowserStorage": "1.5.0", "Microsoft.AspNetCore.Components.Authorization": "9.0.7", "Microsoft.AspNetCore.Components.Web": "9.0.8", "Microsoft.AspNetCore.Components.WebAssembly": "9.0.7", "System.Text.Json": "9.0.7", "blazor-dragdrop": "2.4.0"}, "runtime": {"UFU.CoreFX.Shared.dll": {"assemblyVersion": "5.0.6.0", "fileVersion": "5.0.6.0"}}}, "UFU.IoT.Shared/1.0.0": {"dependencies": {"AntDesign": "0.15.5", "Microsoft.AspNetCore.Components.Web": "9.0.8", "System.Text.Json": "9.0.7", "UFU.CoreFX.Shared": "5.0.6"}, "runtime": {"UFU.IoT.Shared.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Microsoft.Web.WebView2.Core/1.0.3179.45": {"runtime": {"Microsoft.Web.WebView2.Core.dll": {"assemblyVersion": "1.0.3179.45", "fileVersion": "1.0.3179.45"}}}, "Microsoft.Web.WebView2.WinForms/1.0.3179.45": {"runtime": {"Microsoft.Web.WebView2.WinForms.dll": {"assemblyVersion": "1.0.3179.45", "fileVersion": "1.0.3179.45"}}}, "Microsoft.Web.WebView2.Wpf/1.0.3179.45": {"runtime": {"Microsoft.Web.WebView2.Wpf.dll": {"assemblyVersion": "1.0.3179.45", "fileVersion": "1.0.3179.45"}}}}}, "libraries": {"HX.Experiment.Window/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AntDesign/0.15.5": {"type": "package", "serviceable": true, "sha512": "sha512-ldf0sMFcKqBrnEUoiWDo9cIcK+x6yUPcgjYIjq9tD2pc64VNonWVKg1mbXmlDezG2D1CtjCwS96px9mXH/L3Ig==", "path": "antdesign/0.15.5", "hashPath": "antdesign.0.15.5.nupkg.sha512"}, "BemIt/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-oSrFyB/9fvxKVEqwoMjHmxDSnEFiTjyxIFjdOEyXmc/4YVXoEiYhxrNhNA4B/4lAfAMQBVcOWXdllZ+bSkWNqg==", "path": "bemit/2.2.0", "hashPath": "bemit.2.2.0.nupkg.sha512"}, "blazor-dragdrop/2.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-gLw4kjHNV/q1x/KeNLDMvr1SHOU9XsbYuJ0k9Bta3I9DqJqqwBgiuCJPYT+YgoMVzMHPwQMlpSA7eO0Jsk+b7g==", "path": "blazor-dragdrop/2.4.0", "hashPath": "blazor-dragdrop.2.4.0.nupkg.sha512"}, "DeepCloner.Core/0.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-45ovZyLKECJUeyMqxp8QDc7D85lUhDfaSgu0d7W9o8GU1PdePKy9sAVh+4tlXqt3PyXnTRRU42Ta+iRssbLarw==", "path": "deepcloner.core/0.1.0", "hashPath": "deepcloner.core.0.1.0.nupkg.sha512"}, "DeviceId/6.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-2nfdZxcEfXVKJSg6n0Cv9JdH1UmlEdIIplfRy5AGqhzzmOOy5lK/kPcP0dHky1ZbwxqGIlf4fMq2qYCtMtX+0A==", "path": "deviceid/6.9.0", "hashPath": "deviceid.6.9.0.nupkg.sha512"}, "EPPlus/7.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-3ddsZARkpB5VxlfkRQJumCkSDZrU0yBOl6e/ZHR9KgPNa6atMFApgbu+hp9vOq/r5CUToMaudBWEyTSsdF+vLg==", "path": "epplus/7.5.1", "hashPath": "epplus.7.5.1.nupkg.sha512"}, "EPPlus.Interfaces/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-mGLKgdIKkXRYIu+HIGmZUngVAAlPzIQgI/KqG10m6P5P2112l6p/5dDa35UHu4GV4Qevw0Pq9PxAymrrrl4tzA==", "path": "epplus.interfaces/7.5.0", "hashPath": "epplus.interfaces.7.5.0.nupkg.sha512"}, "EPPlus.System.Drawing/7.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-cgwstM12foFisisURUyxwJOWHMD/rZxPSyBXFsCOFayaKq0oKlOs1mCTueKNNIlpPDG1no9vcaQiJgZXFM4KPA==", "path": "epplus.system.drawing/7.5.0", "hashPath": "epplus.system.drawing.7.5.0.nupkg.sha512"}, "FluentValidation/11.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-WZJyrNf4suiaI6avVG32W9kmBLC3+V5rDTcLeRJjeU4tDmTyDIOz1459DzCKppXs34YpZg9rCUzDY3M4XxG7Ug==", "path": "fluentvalidation/11.4.0", "hashPath": "fluentvalidation.11.4.0.nupkg.sha512"}, "FluentValidation.DependencyInjectionExtensions/11.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZXtoDqfRnggHwIbFWnK1sFs3KlTadH5ezNDiXz411OSkUNowBRboaZBcSvx4gEot7CNZdoReUKWYJwow2+mbg==", "path": "fluentvalidation.dependencyinjectionextensions/11.4.0", "hashPath": "fluentvalidation.dependencyinjectionextensions.11.4.0.nupkg.sha512"}, "Majorsoft.Blazor.Extensions.BrowserStorage/1.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-KNlVUlAmc/GX2T5YJG1rnZ0YDEnF656s9Tl+tInw6+7j8FAFHAnDGrMjV7/eNQJh0rSgCgRshCJbAPdOQzFRMg==", "path": "majorsoft.blazor.extensions.browserstorage/1.5.0", "hashPath": "majorsoft.blazor.extensions.browserstorage.1.5.0.nupkg.sha512"}, "Masa.Blazor/1.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-Xu4fIm0MM21YgeDaS1xxr43zwBU07oW3g/MuyTX97/H3xzcp+PLBbYT4o2Nf/70liRbXEAjoJ9QIyzUwk0+QkA==", "path": "masa.blazor/1.10.3", "hashPath": "masa.blazor.1.10.3.nupkg.sha512"}, "Masa.Blazor.MobileComponents/1.10.2": {"type": "package", "serviceable": true, "sha512": "sha512-+lYwxXUNlnvYlqwpuOgVkhgltmp7YvTx6wX81u9AnMYuPMEFuYiIHHpvZd0gqDdovK9nJdZvNaCb4JfkrhpAeA==", "path": "masa.blazor.mobilecomponents/1.10.2", "hashPath": "masa.blazor.mobilecomponents.1.10.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-1M8ncVJtsRBEfbDc9QCa2zuwMjh/YWLu/WnRXWHG5q+KE5FDvPEsV3cJag3qy6NWL2b0H+pdushFsN9d3geemA==", "path": "microsoft.aspnetcore.authorization/9.0.8", "hashPath": "microsoft.aspnetcore.authorization.9.0.8.nupkg.sha512"}, "Microsoft.AspNetCore.Components/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-+s+r/l2iBoU1mnG8ft9HS/xzqjd0cGbqnsVC9PgjNa5VGm/pVbPEMUJDea58iOaVETi7VGQCDoHLG83ObmlK2A==", "path": "microsoft.aspnetcore.components/9.0.8", "hashPath": "microsoft.aspnetcore.components.9.0.8.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Analyzers/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-uRx+7E0/nKIbQ0yG2/TYr6RYqeVanWwAVdwzzffGvTyI/jZBE3YT9aJ9c0DGrhgvkDZPzH1Z4ryp6LdMmBJFAQ==", "path": "microsoft.aspnetcore.components.analyzers/9.0.8", "hashPath": "microsoft.aspnetcore.components.analyzers.9.0.8.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Authorization/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-mICbg7Cic394tN3xoLsKi4aWiARw1eCS1bNMyho81WdIKdHayxCTw6Fvj6eLMPc+zgVsoIdYWTh5w2TRwVl8pQ==", "path": "microsoft.aspnetcore.components.authorization/9.0.7", "hashPath": "microsoft.aspnetcore.components.authorization.9.0.7.nupkg.sha512"}, "Microsoft.AspNetCore.Components.DataAnnotations.Validation/3.2.0-rc1.20223.4": {"type": "package", "serviceable": true, "sha512": "sha512-VyZAAbuXuAVrh4Vii5Aq1N841iVhSjE4mTf/lVh+AgrKinaltE0IeEWPKv889j4ineX7i9BUiAqlGP7UpZwlnQ==", "path": "microsoft.aspnetcore.components.dataannotations.validation/3.2.0-rc1.20223.4", "hashPath": "microsoft.aspnetcore.components.dataannotations.validation.3.2.0-rc1.20223.4.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Forms/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-OgbzTUq4/y0jbUspgBFBQ3TJZepx05uFYLgXyB04uwTIM2JtHFcFcQvoO2lWRwXl8I2B5PtMTcTFVFt+/b5onQ==", "path": "microsoft.aspnetcore.components.forms/9.0.8", "hashPath": "microsoft.aspnetcore.components.forms.9.0.8.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Web/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-YKdFoDJ36VNRDoZfoc6bf/RWEg4mR/f6Fa9CToDEiqwZId70Qc9JUamM4rgfDaKLIGY7QEeh3oS3j1cQb5AUUA==", "path": "microsoft.aspnetcore.components.web/9.0.8", "hashPath": "microsoft.aspnetcore.components.web.9.0.8.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebAssembly/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-XQpNRCk90fdz6srrcnED2zLQrjFjZYduqCpKrdzPhBicD37kW1p7cDGfX6Smfgk937iZmZwh72xAqEDYYjU8tA==", "path": "microsoft.aspnetcore.components.webassembly/9.0.7", "hashPath": "microsoft.aspnetcore.components.webassembly.9.0.7.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebView/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-qaS5pNT0oO/3o30AHwJiNDhHUoN6GDBu8z5m83cIxMo8nVmk0/IFjCrafYA1v/OfMRCmDaG/PS1wxTDcWm4VuA==", "path": "microsoft.aspnetcore.components.webview/9.0.5", "hashPath": "microsoft.aspnetcore.components.webview.9.0.5.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebView.WindowsForms/9.0.90": {"type": "package", "serviceable": true, "sha512": "sha512-27F9Btx7yzZQ7WhOWV6gbXtkaEg6ADagFYWdy08CTIxbwPDq4F9WV1LYoONl2KsoRvO0ZAzZT7TOdlL39ongog==", "path": "microsoft.aspnetcore.components.webview.windowsforms/9.0.90", "hashPath": "microsoft.aspnetcore.components.webview.windowsforms.9.0.90.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-sju8zleT9mRQWeEe4hu4Vh4uep6WppxULZEkirEgU/pRl6uIQQsd526EBa8u52j+LuA3J8gS8twRmTpsr3xGtA==", "path": "microsoft.aspnetcore.metadata/9.0.8", "hashPath": "microsoft.aspnetcore.metadata.9.0.8.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-6m+8Xgmf8UWL0p/oGqBM+0KbHE5/ePXbV1hKXgC59zEv0aa0DW5oiiyxDbK5kH5j4gIvyD5uWL0+HadKBJngvQ==", "path": "microsoft.extensions.configuration/9.0.8", "hashPath": "microsoft.extensions.configuration.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-yNou2KM35RvzOh4vUFtl2l33rWPvOCoba+nzEDJ+BgD8aOL/jew4WPCibQvntRfOJ2pJU8ARygSMD+pdjvDHuA==", "path": "microsoft.extensions.configuration.abstractions/9.0.8", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-0vK9DnYrYChdiH3yRZWkkp4x4LbrfkWEdBc5HOsQ8t/0CLOWKXKkkhOE8A1shlex0hGydbGrhObeypxz/QTm+w==", "path": "microsoft.extensions.configuration.binder/9.0.8", "hashPath": "microsoft.extensions.configuration.binder.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-3LVg32iMfR9ENeegXAo73L+877iOcQauLJsXlKZNVSsLA/HbPgClZdeMGdjLSkaidYw3l02XbXTlOdGYNgu91Q==", "path": "microsoft.extensions.configuration.fileextensions/9.0.7", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-3HQV326liEInT9UKEc+k73f1ECwNhvDS/DJAe5WvtMKDJTJqTH2ujrUC2ZlK/j6pXyPbV9f0Ku8JB20JveGImg==", "path": "microsoft.extensions.configuration.json/9.0.7", "hashPath": "microsoft.extensions.configuration.json.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-JJjI2Fa+QtZcUyuNjbKn04OjIUX5IgFGFu/Xc+qvzh1rXdZHLcnqqVXhR4093bGirTwacRlHiVg1XYI9xum6QQ==", "path": "microsoft.extensions.dependencyinjection/9.0.8", "hashPath": "microsoft.extensions.dependencyinjection.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-xY3lTjj4+ZYmiKIkyWitddrp1uL5uYiweQjqo4BKBw01ZC4HhcfgLghDpPZcUlppgWAFqFy9SgkiYWOMx365pw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.8", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-BKkLCFXzJvNmdngeYBf72VXoZqTJSb1orvjdzDLaGobicoGFBPW8ug2ru1nnEewMEwJzMgnsjHQY8EaKWmVhKg==", "path": "microsoft.extensions.diagnostics/9.0.8", "hashPath": "microsoft.extensions.diagnostics.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-UDY7blv4DCyIJ/8CkNrQKLaAZFypXQavRZ2DWf/2zi1mxYYKKw2t8AOCBWxNntyPZHPGhtEmL3snFM98ADZqTw==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.8", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-y9djCca1cz/oz/J8jTxtoecNiNvaiGBJeWd7XOPxonH+FnfHqcfslJMcSr5JMinmWFyS7eh3C9L6m6oURZ5lSA==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.7", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Composite/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-mXh4o0ojulzG+QLfc+lFeqNv3yRHAPuSQOke6RSrq6dJXw8/G8iQvOoigHvjnH/lUcRFngziNB01hBWSJwGh7A==", "path": "microsoft.extensions.fileproviders.composite/9.0.5", "hashPath": "microsoft.extensions.fileproviders.composite.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-tgYXJtPa72hYrQuw+pqJvvhUOOQtZuk5jhRZINxIgR0cXwe4bLCQhCGffN+Ad4+AIQOlz4YyOc+GX+unsHc9Kg==", "path": "microsoft.extensions.fileproviders.embedded/9.0.5", "hashPath": "microsoft.extensions.fileproviders.embedded.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-JYEPYrb+YBpFTCdmSBrk8cg3wAi1V4so7ccq04qbhg3FQHQqgJk28L3heEOKMXcZobOBUjTnGCFJD49Ez9kG5w==", "path": "microsoft.extensions.fileproviders.physical/9.0.7", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-5VKpTH2ME0SSs0lrtkpKgjCeHzXR5ka/H+qThPwuWi78wHubApZ/atD7w69FDt0OOM7UMV6LIbkqEQgoby4IXA==", "path": "microsoft.extensions.filesystemglobbing/9.0.7", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-jDj+4aDByk47oESlDDTtk6LWzlXlmoCsjCn6ihd+i9OntN885aPLszUII5+w0B/7wYSZcS3KdjqLAIhKLSiBXQ==", "path": "microsoft.extensions.http/9.0.8", "hashPath": "microsoft.extensions.http.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-Z/7ze+0iheT7FJeZPqJKARYvyC2bmwu3whbm/48BJjdlGVvgDguoCqJIkI/67NkroTYobd5geai1WheNQvWrgA==", "path": "microsoft.extensions.logging/9.0.8", "hashPath": "microsoft.extensions.logging.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-pYnAffJL7ARD/HCnnPvnFKSIHnTSmWz84WIlT9tPeQ4lHNiu0Az7N/8itihWvcF8sT+VVD5lq8V+ckMzu4SbOw==", "path": "microsoft.extensions.logging.abstractions/9.0.8", "hashPath": "microsoft.extensions.logging.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-OmTaQ0v4gxGQkehpwWIqPoEiwsPuG/u4HUsbOFoWGx4DKET2AXzopnFe/fE608FIhzc/kcg2p8JdyMRCCUzitQ==", "path": "microsoft.extensions.options/9.0.8", "hashPath": "microsoft.extensions.options.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-eW2s6n06x0w6w4nsX+SvpgsFYkl+Y0CttYAt6DKUXeqprX+hzNqjSfOh637fwNJBg7wRBrOIRHe49gKiTgJxzQ==", "path": "microsoft.extensions.options.configurationextensions/9.0.8", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-tizSIOEsIgSNSSh+hKeUVPK7xmTIjR8s+mJWOu1KXV3htvNQiPMFRMO17OdI1y/4ZApdBVk49u/08QGC9yvLug==", "path": "microsoft.extensions.primitives/9.0.8", "hashPath": "microsoft.extensions.primitives.9.0.8.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s/s20YTVY9r9TPfTrN5g8zPF1YhwxyqO6PxUkrYTGI2B+OGPe9AdajWZrLhFqXIvqIW23fnUE4+ztrUWNU1+9g==", "path": "microsoft.io.recyclablememorystream/3.0.1", "hashPath": "microsoft.io.recyclablememorystream.3.0.1.nupkg.sha512"}, "Microsoft.JSInterop/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-arGqasYOND4vG0s1RywqHAKq1NIyFrco/srpM9QDlVGnFrYdtkWZUEuiVcD5GZswfmYOGVD5CLIHVVb8S86ylQ==", "path": "microsoft.jsinterop/9.0.8", "hashPath": "microsoft.jsinterop.9.0.8.nupkg.sha512"}, "Microsoft.JSInterop.WebAssembly/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-MsRXwJ1VmZYp6+497VwywuQ3eD/QRJqE/mXOvTVNW84YCFefZ/VBLgy6gxB5ymJPl8V/VxEzih2Yeu4mOO6wdQ==", "path": "microsoft.jsinterop.webassembly/9.0.7", "hashPath": "microsoft.jsinterop.webassembly.9.0.7.nupkg.sha512"}, "Microsoft.Web.WebView2/1.0.3179.45": {"type": "package", "serviceable": true, "sha512": "sha512-3pokSH5CnN0G6rGhGFo1y87inxYhNxBQ2Vdf0wlvBj99KHxQJormjDACmqRnFeUsmuNFIhWwfAL1ztq7wD5qRA==", "path": "microsoft.web.webview2/1.0.3179.45", "hashPath": "microsoft.web.webview2.1.0.3179.45.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9opKRyOKMCi2xJ7Bj7kxtZ1r9vbzosMvRrdEhVhDz8j8MoBGgB+WmC94yH839NPH+BclAjtQ/pyagvi/8gDLkw==", "path": "microsoft.win32.systemevents/8.0.0", "hashPath": "microsoft.win32.systemevents.8.0.0.nupkg.sha512"}, "OneOf/3.0.223": {"type": "package", "serviceable": true, "sha512": "sha512-YWabiHBFfDdKIO7/TEa72mARVVo5u5FqFnfXuTqjIupMvJlhoH8d0m4uKtczaINuT0vGR1qlXJayeU6bTw5myg==", "path": "oneof/3.0.223", "hashPath": "oneof.3.0.223.nupkg.sha512"}, "OneOf.SourceGenerator/3.0.223": {"type": "package", "serviceable": true, "sha512": "sha512-cEhMSGyKgs14cuBS3b1n1DGXSlMw3Tk5s2O03VGKU8hHspBud/+UdI9Sy849DQ1cygQoDmm3LUj02ArMv+FIjQ==", "path": "oneof.sourcegenerator/3.0.223", "hashPath": "oneof.sourcegenerator.3.0.223.nupkg.sha512"}, "OpenCvSharp4/4.11.0.20250507": {"type": "package", "serviceable": true, "sha512": "sha512-j/R+G6xGC5IV2wGRU0/GF5qG/FrP+Uxp8dmNnXFlIdiw8Gfo4mtvKcBOBfS/bn4pP/7FNHLFX/xvMtgPJeDjAA==", "path": "opencvsharp4/4.11.0.20250507", "hashPath": "opencvsharp4.4.11.0.20250507.nupkg.sha512"}, "OpenCvSharp4.Extensions/4.11.0.20250507": {"type": "package", "serviceable": true, "sha512": "sha512-xVA5xpgvI6cWBx+Zf+kL7NIVc8dpT6ScgHq3nV5tlMBpoG2aClKaLSFHtlPAE99ASfqNieaDzeOduJTe4V1aGw==", "path": "opencvsharp4.extensions/4.11.0.20250507", "hashPath": "opencvsharp4.extensions.4.11.0.20250507.nupkg.sha512"}, "OpenCvSharp4.runtime.win/4.11.0.20250507": {"type": "package", "serviceable": true, "sha512": "sha512-3PxMXyzR+pkL9UOx2PiBjZm+/iyCIu5D6OlA1sq9MH7oWrfvVnCXJlUYhIJv67F7SLUwUuGwXDIQBfdsL/54lg==", "path": "opencvsharp4.runtime.win/4.11.0.20250507", "hashPath": "opencvsharp4.runtime.win.4.11.0.20250507.nupkg.sha512"}, "OpenCvSharp4.Windows/4.11.0.20250507": {"type": "package", "serviceable": true, "sha512": "sha512-Vwbvv8mkK9L8kbaTpQyiguuVj7+2wbXVM2FJkJjsfEIjXjSvzt5mWX/yfYJA6aPH0Mn89FwlBtHo84Cl143C7g==", "path": "opencvsharp4.windows/4.11.0.20250507", "hashPath": "opencvsharp4.windows.4.11.0.20250507.nupkg.sha512"}, "System.CodeDom/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-skI5aEl6XbZelP1hZvSmzzm3mM98k22x19Zu1Lf4rmuYoFEMJr7s7Te/MWUk9twjz4utyXt3q3pYXxGxI/Y+zA==", "path": "system.codedom/9.0.7", "hashPath": "system.codedom.9.0.7.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.Drawing.Common/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-AVyutHHKrX0Mt9C9T8W3Ccat3cVauUwvN+EqnVpSQi92nwBqoQ+2ZRvGP1S+rKK+6TGXmRflSYNShVjl2mMBlw==", "path": "system.drawing.common/8.0.11", "hashPath": "system.drawing.common.8.0.11.nupkg.sha512"}, "System.Management/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-9wHNgKnZRFLZ/vSQ7+Ai9LrKkxEKh9LvC+Ta5dwQgPsyni0ET5igPATg01WW4bx/E5Q3VtRgEGithhOXaKIh0A==", "path": "system.management/9.0.7", "hashPath": "system.management.9.0.7.nupkg.sha512"}, "System.Memory/4.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-qdcDOgnFZY40+Q9876JUHnlHu7bosOHX8XISRoH94fwk6hgaeQGSgfZd8srWRZNt5bV9ZW2TljcegDNxsf+96A==", "path": "system.memory/4.6.3", "hashPath": "system.memory.4.6.3.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "path": "system.security.cryptography.pkcs/8.0.1", "hashPath": "system.security.cryptography.pkcs.8.0.1.nupkg.sha512"}, "System.Text.Encoding.CodePages/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OZIsVplFGaVY90G2SbpgU7EnCoOO5pw1t4ic21dBF3/1omrJFpAGoNAVpPyMVOC90/hvgkGG3VFqR13YgZMQfg==", "path": "system.text.encoding.codepages/8.0.0", "hashPath": "system.text.encoding.codepages.8.0.0.nupkg.sha512"}, "System.Text.Json/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-u/lN2FEEXs3ghj2ta8tWA4r2MS9Yni07K7jDmnz8h1UPDf0lIIIEMkWx383Zz4fJjJio7gDl+00RYuQ/7R8ZQw==", "path": "system.text.json/9.0.7", "hashPath": "system.text.json.9.0.7.nupkg.sha512"}, "Util.Reflection/1.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-uKQLJZ9KKXvQ6JYLbOCISyBpssmBAX4HGXhmTifRsD/kD1E4bZYyivwDqBvTleNIE1/tmj/XhapsukxKNQEK5A==", "path": "util.reflection/1.0.3", "hashPath": "util.reflection.1.0.3.nupkg.sha512"}, "HX.Experiment.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "UFU.CoreFX.Shared/5.0.6": {"type": "project", "serviceable": false, "sha512": ""}, "UFU.IoT.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Web.WebView2.Core/1.0.3179.45": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Web.WebView2.WinForms/1.0.3179.45": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Web.WebView2.Wpf/1.0.3179.45": {"type": "reference", "serviceable": false, "sha512": ""}}}