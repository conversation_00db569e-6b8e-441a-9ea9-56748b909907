import{a as e}from"../../chunks/tslib.es6-68144fbe.js";var r=function(e,r,n){var i=null,t=null,o=n&&n.leading,s=n&&n.trailing;null==o&&(o=!0);null==s&&(s=!o);1==o&&(s=!1);var u=function(){i&&(clearTimeout(i),i=null)},l=function(){var n=o&&!i,u=this,l=arguments;if(t=function(){return e.apply(u,l)},i||(i=setTimeout((function(){if(i=null,s)return t()}),r)),n)return n=!1,t()};return l.cancel=u,l.flush=function(){var e=t;u(),e&&e()},l};function n(n,i){if(!i)throw new Error("the handle from .NET cannot be null");if(!n)return void i.dispose();const t=r((()=>{i&&i.invokeMethodAsync("Invoke")}),300,{trailing:!0}),o=new ResizeObserver(((r=[])=>e(this,void 0,void 0,(function*(){r.length&&t()}))));n._resizeObserver=Object(n._resizeObserver),n._resizeObserver={handle:i,observer:o},o.observe(n)}function i(e){e&&e._resizeObserver&&(e._resizeObserver.observer.unobserve(e),e._resizeObserver.handle.dispose(),delete e._resizeObserver)}function t(e,r){if(e){const i=document.querySelector(e);i&&n(i,r)}}function o(e){if(e){const r=document.querySelector(e);r&&i(r)}}export{n as observe,t as observeSelector,i as unobserve,o as unobserveSelector};
//# sourceMappingURL=index-07a0c3f6.js.map
