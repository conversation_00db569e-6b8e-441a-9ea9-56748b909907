@import '../../style/mixins/index';

@confirm-prefix-cls: ~'@{ant-prefix}-modal-confirm';

.@{confirm-prefix-cls} {
  .@{ant-prefix}-modal-header {
    display: none;
  }

  .@{ant-prefix}-modal-body {
    padding: @modal-confirm-body-padding;
  }

  &-body-wrapper {
    .clearfix();
  }

  &-body {
    .@{confirm-prefix-cls}-title {
      display: block;
      // create BFC to avoid
      // https://user-images.githubusercontent.com/507615/37702510-ba844e06-2d2d-11e8-9b67-8e19be57f445.png
      overflow: hidden;
      color: @heading-color;
      font-weight: 500;
      font-size: @modal-confirm-title-font-size;
      line-height: 1.4;
    }

    .@{confirm-prefix-cls}-content {
      margin-top: 8px;
      color: @text-color;
      font-size: @font-size-base;
    }

    > .@{iconfont-css-prefix} {
      float: left;
      margin-right: 16px;
      font-size: 22px;

      // `content` after `icon` should set marginLeft
      + .@{confirm-prefix-cls}-title + .@{confirm-prefix-cls}-content {
        margin-left: 38px;
      }
    }
  }

  .@{confirm-prefix-cls}-btns {
    margin-top: 24px;
    text-align: right;

    .@{ant-prefix}-btn + .@{ant-prefix}-btn {
      margin-bottom: 0;
      margin-left: 8px;
    }
  }

  &-error &-body > .@{iconfont-css-prefix} {
    color: @error-color;
  }

  &-warning &-body > .@{iconfont-css-prefix},
  &-confirm &-body > .@{iconfont-css-prefix} {
    color: @warning-color;
  }

  &-info &-body > .@{iconfont-css-prefix} {
    color: @info-color;
  }

  &-success &-body > .@{iconfont-css-prefix} {
    color: @success-color;
  }

  // https://github.com/ant-design/ant-design/issues/37329
  .@{ant-prefix}-zoom-leave .@{confirm-prefix-cls}-btns {
    pointer-events: none;
  }
}
