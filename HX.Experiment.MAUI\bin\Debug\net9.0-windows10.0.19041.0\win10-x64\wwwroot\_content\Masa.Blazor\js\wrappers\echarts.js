function __classPrivateFieldGet(e,t,s,n){if("a"===s&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?n:"a"===s?n.call(e):n?n.value:t.get(e)}var _EChartsProxy_instances,_EChartsProxy_registerEvent;"function"==typeof SuppressedError&&SuppressedError;class EChartsProxy{constructor(e,t,s){_EChartsProxy_instances.add(this),this.instance=echarts.init(e,t,s),this.intersectionObserver=new IntersectionObserver((e=>{e.some((e=>e.isIntersecting))&&this.instance.resize()})),this.resizeObserver=new ResizeObserver((e=>{this.instance.resize()})),this.intersectionObserver.observe(this.instance.getDom()),this.resizeObserver.observe(this.instance.getDom())}setDotNetObjectReference(e,t){this.dotNetHelper=e,t.forEach((e=>__classPrivateFieldGet(this,_EChartsProxy_instances,"m",_EChartsProxy_registerEvent).call(this,e)))}getOriginInstance(){return this.instance}setOption(e,t=!1,s=!1){this.instance.setOption(e,t,s)}setJsonOption(option,notMerge=!1,lazyUpdate=!1){this.instance.setOption(eval("option="+option),notMerge,lazyUpdate)}showLoading(e){this.instance.showLoading("default",e)}hideLoading(){this.instance.hideLoading()}resize(e){this.instance.resize(e)}dispose(){this.instance.isDisposed()||(this.intersectionObserver.disconnect(),this.resizeObserver.disconnect(),this.instance.dispose())}}function init(e,t,s){return e?new EChartsProxy(e,t,s):null}_EChartsProxy_instances=new WeakSet,_EChartsProxy_registerEvent=function(e){this.instance.on(e,(t=>{const{componentType:s,seriesType:n,seriesIndex:i,seriesName:r,name:a,dataIndex:o,data:c,dataType:h,value:d,color:p}=t;this.dotNetHelper.invokeMethodAsync("OnEvent",e,"globalout"===e?null:{componentType:s,seriesType:n,seriesIndex:i,seriesName:r,name:a,dataIndex:o,data:c,dataType:h,value:Array.isArray(d)?d:[d],color:p})}))};export{init};
//# sourceMappingURL=echarts.js.map
