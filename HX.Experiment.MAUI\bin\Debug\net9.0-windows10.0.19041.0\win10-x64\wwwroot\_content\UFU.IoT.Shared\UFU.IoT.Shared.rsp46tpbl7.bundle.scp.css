/* _content/UFU.IoT.Shared/Pages/DeviceType/Edit.razor.rz.scp.css */
/* _content/UFU.IoT.Shared/Pages/Device/Edit.razor.rz.scp.css */
/* _content/UFU.IoT.Shared/Pages/VirtualDevice/DevicesModel.razor.rz.scp.css */
.DeviceListModel[b-tlumejbp5n] {
    height: calc(100vh - 200px);
    overflow-y: auto;
}

.searchList[b-tlumejbp5n] {
    padding-bottom: 20px;
    width: 100%;
    background-color: #fff;
}

.searchList[b-tlumejbp5n] {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.searchList li[b-tlumejbp5n] {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-right: 10px
}

.searchList li .label[b-tlumejbp5n] {
    width: 80px;
    margin-right: 6px;
    font-size: 12px;
}

.searchList li .opt[b-tlumejbp5n] {
    display: flex;
    flex-direction: row;
    align-items: center;

}

.searchList li .opt>span[b-tlumejbp5n] {
    margin-right: 5px
}
/* _content/UFU.IoT.Shared/Pages/VirtualDevice/Edit.razor.rz.scp.css */
.VirtualDeviceDetail[b-9i1cebhzl5]{
    background-color: #fff;
    padding: 15px;
}
.VirtualDeviceDetail .form li[b-9i1cebhzl5]{
    margin-bottom: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.VirtualDeviceDetail .form li:last-child[b-9i1cebhzl5]{
    margin-bottom: 0;
}
.VirtualDeviceDetail .form li .label[b-9i1cebhzl5]{
    width: 80px;
}
.VirtualDeviceDetail .form .con[b-9i1cebhzl5]{
    flex: 1;
}
.VirtualDeviceDetail .form .input[b-9i1cebhzl5]{
    flex: 1;
    max-width: 200px;
}
.mappingList[b-9i1cebhzl5] {
    margin-top: 20px;
}
.mappingList .tit[b-9i1cebhzl5]{
    margin-bottom: 10px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}
.mappingList .tit .t[b-9i1cebhzl5]{
    font-size: 13px;
    font-weight: bold;
}
.mappingList .table[b-9i1cebhzl5]{
    width: 100%;
    border:1px solid #eee;
}
.mappingList .table .thead[b-9i1cebhzl5]{
    height: 40px;
    line-height: 40px;
    border-bottom:1px solid #eee;
    font-weight: bold;
}
.mappingList .table  .tbody .tr[b-9i1cebhzl5]{
    border-bottom: 1px solid #eee;
    line-height: 40px;
}
.mappingList .item-list[b-9i1cebhzl5]{
    display: flex;
    flex-direction: row;

}
.mappingList .item-list .item[b-9i1cebhzl5]{
    display: flex;
    align-items: center;
    justify-content: center;
    border-right: 1px solid #eee;
    line-height: 40px;
}
.mappingList .item-list .item:last-child[b-9i1cebhzl5]{
    border-right: none;
}
.mappingList .item-list .item-0[b-9i1cebhzl5]{
    width: 6%;
    height: 40px;
}
.mappingList .item-list .item-1[b-9i1cebhzl5]{
    width: 15%;
}
.mappingList .item-list .item-2[b-9i1cebhzl5]{
    width: 15%;
}
.mappingList .item-list .item-3[b-9i1cebhzl5]{
    width: 15%;
}
.mappingList .item-list .item-4[b-9i1cebhzl5]{
    width: 15%;
}
.mappingList .item-list .item-5[b-9i1cebhzl5]{
    width: 16%;
}
.mappingList .item-list .item-6[b-9i1cebhzl5]{
    width: 18%;
}
.mappingList .opt[b-9i1cebhzl5]{
    display: flex;
    flex-direction: row;
    align-items: center;
}
.mappingList .opt .btn[b-9i1cebhzl5]{
    color: #1890ff;
    cursor: pointer;
    margin-right: 10px;
}
.formFoot[b-9i1cebhzl5]{
    display: flex;
    flex-direction: row;
    margin-top: 25px;
}
.formFoot .item[b-9i1cebhzl5]{
    margin-right: 10px;
}

.mappingModal .form li[b-9i1cebhzl5]{
    display: flex;
    flex-direction: row;
    align-items: center;
}

.mappingModal .form li[b-9i1cebhzl5]{
    margin-bottom: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.mappingModal .form li:last-child[b-9i1cebhzl5]{
    margin-bottom: 0;
}
.mappingModal .form li .label[b-9i1cebhzl5]{
    width: 90px;
}
.mappingModal .form .con[b-9i1cebhzl5]{
    flex: 1;
}
.mappingModal .form .input[b-9i1cebhzl5]{
    flex: 1;
    max-width: 200px;
}

.mappingList2[b-9i1cebhzl5] {
    margin-top: 20px;
}
.mappingList2 .tit[b-9i1cebhzl5]{
    margin-bottom: 10px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}
.mappingList2 .tit .t[b-9i1cebhzl5]{
    font-size: 13px;
    font-weight: bold;
}
.mappingList2 .table[b-9i1cebhzl5]{
    width: 100%;
    border:1px solid #eee;
}
.mappingList2 .table .thead[b-9i1cebhzl5]{
    height: 40px;
    line-height: 40px;
    border-bottom:1px solid #eee;
    font-weight: bold;
}
.mappingList2 .table  .tbody .tr[b-9i1cebhzl5]{
    border-bottom: 1px solid #eee;
}
.mappingList2 .item-list[b-9i1cebhzl5]{
    display: flex;
    flex-direction: row;

}
.mappingList2 .item-list .item[b-9i1cebhzl5]{
    display: flex;
    align-items: center;
    justify-content: center;
    border-right: 1px solid #eee;
}
.mappingList2 .item-list .item:last-child[b-9i1cebhzl5]{
    border-right: none;
}
.mappingList2 .item-list .item-1[b-9i1cebhzl5]{
    width: 25%;
}
.mappingList2 .item-list .item-2[b-9i1cebhzl5]{
    width: 25%;
}
.mappingList2 .item-list .item-3[b-9i1cebhzl5]{
    width: 25%;
}
.mappingList2 .item-list .item-4[b-9i1cebhzl5]{
    width: 25%;
}
.mappingList2 .table  .tbody .item[b-9i1cebhzl5]{
    padding: 10px;
}
.mappingList2 .addbtn[b-9i1cebhzl5]{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 26px;
    background-color: #1877F2;
    border-radius: 4px;
    cursor: pointer;
    color: #fff;
    font-size: 12px;
}
.mappingList2  .delbtn[b-9i1cebhzl5]{
    cursor: pointer;
    color: #1877F2;
}
