import{a as e}from"../../chunks/tslib.es6-68144fbe.js";import{f as t}from"../../chunks/index-cef005e4.js";import{formatToStandardOptions as i}from"../../mixins/intersect/index-f360c115.js";import"../../chunks/helper-6d386307.js";class s{constructor(s,o){this.activeStack=[],this.handle=s,this.options=i(o),this.debouncedUpdate=t((()=>e(this,void 0,void 0,(function*(){yield this.handle.invokeMethodAsync("UpdateActiveTarget",this.activeStack[this.activeStack.length-1])}))),16)}observe(t){const i=document.getElementById(t);if(!i)return void console.warn(`[ScrollToTarget] Element with id '${t}' not found`);if(i._intersectForScrollToTarget)return;const s=new IntersectionObserver(((i=[],s)=>e(this,void 0,void 0,(function*(){i.some((e=>e.isIntersecting))?this.activeStack.push(t):this.activeStack.includes(t)&&this.activeStack.splice(this.activeStack.indexOf(t),1),this.debouncedUpdate()}))),this.options);i._intersectForScrollToTarget=Object(i._intersectForScrollToTarget),i._intersectForScrollToTarget={handle:this.handle,observer:s},s.observe(i)}unobserve(e){const t=document.getElementById(e);if(!t)return;const i=t._intersectForScrollToTarget;i&&(i.observer.unobserve(t),delete t._intersectForScrollToTarget)}dispose(){this.handle&&(this.handle.dispose(),this.handle=null)}}function o(e,t){return new s(e,t)}export{o as init};
//# sourceMappingURL=index-1c14c8ac.js.map
