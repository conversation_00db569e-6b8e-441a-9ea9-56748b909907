import{a as t}from"../../chunks/tslib.es6-68144fbe.js";import{g as o}from"../../chunks/helper-6d386307.js";function r(r,n,i){var a;if(!n)throw new Error("the handle cannot be null");if(!r)return void n.dispose();if(r._intersect)return;const c=null!==(a=null==i?void 0:i.once)&&void 0!==a&&a,g=s(i),u=new IntersectionObserver(((i=[],s)=>t(this,void 0,void 0,(function*(){const t=i.map((t=>({isIntersecting:t.isIntersecting,target:o(t.target)}))),s=t.some((t=>t.isIntersecting));c&&!s||(yield n.invokeMethodAsync("Invoke",{isIntersecting:s,entries:t})),s&&c&&e(r)}))),g);r._intersect=Object(r._intersect),r._intersect={handle:n,observer:u},u.observe(r)}function e(t){if(!t)return;const o=t._intersect;o&&(o.observer.unobserve(t),o.handle.dispose(),delete t._intersect)}function n(t,o,e){if(t){const n=document.querySelector(t);n&&r(n,o,e)}}function i(t){if(t){const o=document.querySelector(t);o&&e(o)}}function s(t){if(!t)return null;const o=t.rootSelector?document.querySelector(t.rootSelector):null;return"None"!==t.autoRootMargin&&("Top"===t.autoRootMargin&&"0px"!==t.rootMarginBottom?t.rootMarginTop=a(o,t.rootMarginBottom,!1)+"px":"Right"===t.autoRootMargin&&"0px"!==t.rootMarginLeft?t.rootMarginRight=a(o,t.rootMarginLeft,!1)+"px":"Bottom"===t.autoRootMargin&&"0px"!==t.rootMarginTop?t.rootMarginBottom=a(o,t.rootMarginTop,!1)+"px":"Left"===t.autoRootMargin&&"0px"!==t.rootMarginRight&&(t.rootMarginLeft=a(o,t.rootMarginRight,!1)+"px")),{rootMargin:`${t.rootMarginTop} ${t.rootMarginRight} ${t.rootMarginBottom} ${t.rootMarginLeft}`,root:o,threshold:t.threshold}}function a(t,o,r){t=t||document.documentElement;const e=parseInt(o);if(isNaN(e))return 0;var n=r?t.clientWidth:t.clientHeight;return Math.abs(e)-n}export{s as formatToStandardOptions,r as observe,n as observeSelector,e as unobserve,i as unobserveSelector};
//# sourceMappingURL=index-f360c115.js.map
