/* _content/UFU.CoreFX.Shared/Components/BreadCrumbs.razor.rz.scp.css */
.breadCrumbs[b-805fqmu0yc] {
    height: 34px;
    border-bottom: 1px solid #ddd;
    background-color: #fff;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

    .breadCrumbs .l[b-805fqmu0yc] {
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    .breadCrumbs .r[b-805fqmu0yc] {
        padding-right: 15px;
        height: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    .breadCrumbs .open[b-805fqmu0yc] {
        height: 100%;
        width: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

        .breadCrumbs .open .icon-right[b-805fqmu0yc] {
            font-size: 14px;
            color: #999;
        }

    .breadCrumbs .crumbs[b-805fqmu0yc] {
        padding-left: 5px;
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 100%;
    }

        .breadCrumbs .crumbs .home[b-805fqmu0yc] {
            margin-right: 10px;
        }

            .breadCrumbs .crumbs .home .icon-home[b-805fqmu0yc] {
                font-size: 14px;
                color: #1877F2;
                margin-right: 10px;
            }

        .breadCrumbs .crumbs .link[b-805fqmu0yc] {
            color: #999;
        }

        .breadCrumbs .crumbs .current[b-805fqmu0yc] {
            color: #333;
        }

        .breadCrumbs .crumbs .arrow[b-805fqmu0yc] {
            padding: 0 3px;
        }

    .breadCrumbs .operation[b-805fqmu0yc] {
        height: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
    }

        .breadCrumbs .operation li[b-805fqmu0yc] {
            position: relative;
            margin: 0 1px;
            padding: 0 8px;
            font-size: 12px;
            color: #2572fd;
            cursor: pointer;
            height: 100%;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
        }

            .breadCrumbs .operation li[b-805fqmu0yc]::before {
                content: "";
                display: block;
                width: 1px;
                height: 12px;
                background-color: #ddd;
                position: absolute;
                right: 0;
                top: 11px;
            }

            .breadCrumbs .operation li:last-child[b-805fqmu0yc]::before {
                width: 0;
            }

            .breadCrumbs .operation li:hover[b-805fqmu0yc] {
                background-color: #1877F2;
                color: #fff;
            }

                .breadCrumbs .operation li:hover[b-805fqmu0yc]::before {
                    width: 0;
                }

                .breadCrumbs .operation li:hover .icon[b-805fqmu0yc] {
                    color: #fff;
                }

            .breadCrumbs .operation li.down:hover .dropDown[b-805fqmu0yc] {
                display: block;
            }

        .breadCrumbs .operation .icon[b-805fqmu0yc] {
            padding-right: 4px;
            font-size: 12px;
            color: #2572fd;
        }

        .breadCrumbs .operation .icon-yichang[b-805fqmu0yc] {
            color: red;
        }

        .breadCrumbs .operation .icon-zhengchang[b-805fqmu0yc] {
            color: #1bbe6b;
        }

    .breadCrumbs .user:hover .dropDown[b-805fqmu0yc] {
        display: block;
    }

    .breadCrumbs .user[b-805fqmu0yc] {
        position: relative;
        line-height: 34px;
        padding-left: 20px;
    }

    .breadCrumbs .dropDown[b-805fqmu0yc] {
        display: none;
        position: absolute;
        top: 34px;
        right: 0;
        padding: 15px;
        background-color: #fff;
        box-shadow: 0 0 8px rgb(0 0 0 / 15%);
        z-index: 999;
    }

        .breadCrumbs .dropDown .arrow[b-805fqmu0yc] {
            left: 80%;
            transform: translateX(-80%);
            background: transparent;
            position: absolute;
            display: block;
            width: 8px;
            height: 8px;
            overflow: hidden;
            background: 0 0;
            pointer-events: none;
            top: -8px;
        }

            .breadCrumbs .dropDown .arrow .arrow-content[b-805fqmu0yc] {
                box-shadow: -2px -2px 5px rgb(0 0 0 / 6%);
                transform: translateY(4.24264069px) rotate(45deg);
                background-color: #fff;
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                display: block;
                width: 6px;
                height: 6px;
                margin: auto;
                background-color: #fff;
                content: '';
                pointer-events: auto;
            }

        .breadCrumbs .dropDown .link[b-805fqmu0yc] {
            line-height: 16px;
            margin: 15px 0;
            white-space: nowrap;
        }





.topbackContainer[b-805fqmu0yc] {
    display: none;
    flex-direction: row;
    align-items: center;
    background-color: #fff;
    height: 40px;
    width: 100%;
    box-shadow: 1px 3px 1px #eee;
}

    .topbackContainer .back[b-805fqmu0yc], .topbackContainer .menu[b-805fqmu0yc] {
        width: 40px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
    }

    .topbackContainer .menu[b-805fqmu0yc] {
        font-size: 20px;
        color: #3A926D;
        font-weight: bold;
    }

    .topbackContainer .tit[b-805fqmu0yc] {
        width: calc(100vw - 40px - 40px);
        font-size: 14px;
        text-align: center;
        color: #333;
    }

    .topbackContainer .menu .erpfont[b-805fqmu0yc] {
        font-size: 16px;
        color: #999;
    }

@media screen and (max-width: 760px) {
    .topbackContainer[b-805fqmu0yc] {
        display: flex;
    }

    .breadCrumbs[b-805fqmu0yc] {
        display: none;
    }
}
/* _content/UFU.CoreFX.Shared/Components/ImageVerify.razor.rz.scp.css */
.imageVerifyContainer[b-n6zd7nrw3s] {
    box-sizing:content-box;
    width:fit-content;
}
.imageVerifyContainer .hd[b-n6zd7nrw3s] {
    display:flex;
    flex-direction:row;
    justify-content:space-between;
    align-items:center;
    font-size:14px;
    margin-bottom:10px;
}
.point[b-n6zd7nrw3s] {
    width: 28px;
    height: 28px;
    line-height: 28px;
    font-size: 16px;
    font-weight: 600;
    color: #fff;
    border-radius: 14px;
    background-color: #3c78d8;
    position: absolute;
    text-align: center;
    cursor: default;
}
.imageVerifyBox[b-n6zd7nrw3s] {
    position: relative;
}
.imageVerifyBox img[b-n6zd7nrw3s] {
    width:100%;
    height:100%;
    
}
/* _content/UFU.CoreFX.Shared/Pages/Core/OrganUser/AddUser.razor.rz.scp.css */
