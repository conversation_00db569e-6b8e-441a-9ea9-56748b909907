.@{steps-prefix-cls}-flex-not-supported {
  &.@{steps-prefix-cls}-horizontal.@{steps-prefix-cls}-label-horizontal {
    .@{steps-prefix-cls}-item {
      margin-left: -16px;
      padding-left: 16px;
      background: @steps-background;
    }

    &.@{steps-prefix-cls}-small .@{steps-prefix-cls}-item {
      margin-left: -12px;
      padding-left: 12px;
    }
  }

  &.@{steps-prefix-cls}-dot {
    .@{steps-prefix-cls}-item {
      &:last-child {
        overflow: hidden;

        .@{steps-prefix-cls}-icon-dot::after {
          right: -200px;
          width: 200px;
        }
      }

      .@{steps-prefix-cls}-icon-dot::before,
      .@{steps-prefix-cls}-icon-dot::after {
        position: absolute;
        top: 0;
        left: -10px;
        width: 10px;
        height: 8px;
        background: @steps-background;
        content: '';
      }

      .@{steps-prefix-cls}-icon-dot::after {
        right: -10px;
        left: auto;
      }
    }

    .@{steps-prefix-cls}-item-wait
      .@{steps-prefix-cls}-item-icon
      > .@{steps-prefix-cls}-icon
      .@{steps-prefix-cls}-icon-dot {
      background: #ccc;
    }
  }
}
