using HX.Experiment.Shared.Model;
using UFU.IoT.Models;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared.Services;
using Microsoft.AspNetCore.Components;
using Masa.Blazor;
using HX.Experiment.Shared.Services;

namespace HX.Experiment.Shared.Pages.HXDevice;

public partial class StudentTestDialog : ComponentBase
{
    [Parameter] public StudentInfo StudentModel { get; set; } = new();
    public DeviceModel? DeviceModel => DeviceService.CurrentDevice;
    [Parameter] public EventCallback CloseDialog { get; set; }
    [Inject] private IDeviceService DeviceService { get; set; }
    [Inject] public StateService StateService { get; set; } = null!;
    [Inject] public IPopupService PopupService { get; set; } = null!;

    protected bool _isIdCardLoading;
    protected bool SelectedDialog { get; set; } = false;
    protected List<StudentInfo> StudentEntries = new();
    protected List<ClassInfo> ClassList = new();
    protected List<int> CheckTimeList = new() { 5, 10, 15, 20, 30 }; // 默认检测时长选项
    public int CheckTimeValue = 0;

    public async Task UpdateSearchInputAsync(string val)
    {
        _isIdCardLoading = true;
        try
        {
            var result = await StateService.GetAsJsonAsync<List<DataModel<StudentInfo>>>(
                "/api/v2/hx_experiment/Student/List",
                new Dictionary<string, string>
                {
                    { nameof(StudentInfo.CardId), val }
                });
            StudentEntries = result?.Data?.Select(m => m.Data)?.ToList() ?? new List<StudentInfo>();
        }
        catch (Exception ex)
        {
            await PopupService.EnqueueSnackbarAsync($"搜索学生失败: {ex.Message}", AlertTypes.Error);
        }
        finally
        {
            _isIdCardLoading = false;
        }
    }

    public async Task SaveAsync()
    {
        await HandleSubmit();
    }
    private async Task HandleSubmit()
    {

        if (CheckTimeValue <= 0)
        {
            await PopupService.EnqueueSnackbarAsync("检测时长不能为空", AlertTypes.Error);
            return;
        }

        try
        {
            // 创建检测记录
            var testRecord = new
            {
                StudentId = StudentModel.CardId, // 使用身份证号作为学生ID
                StudentName = StudentModel.Name,
                DeviceId = DeviceModel?.Id,
                DeviceSN = DeviceModel?.DeviceSN,
                CheckTime = CheckTimeValue,
                StartTime = DateTime.Now,
                EndTime = DateTime.Now.AddMinutes(CheckTimeValue),
                Status = "检测中"
            };
            // 调用API创建检测记录
            var result = await StateService.PostAsJsonAsync<DataModel<object>>(
                "/api/v2/hx_experiment/TestRecord/Add", testRecord);

            if (result.Success)
            {
                await PopupService.EnqueueSnackbarAsync("检测已开始！", AlertTypes.Success);

                // 发送开始检测命令到设备
                await SendStartTestCommand();
            }
            else
            {
                await PopupService.EnqueueSnackbarAsync(result.Message ?? "开始检测失败", AlertTypes.Error);
            }
        }
        catch (Exception ex)
        {
            await PopupService.EnqueueSnackbarAsync($"开始检测失败: {ex.Message}", AlertTypes.Error);
        }
        finally
        {
            PopupService.HideProgressCircular();
        }
    }

    private async Task SendStartTestCommand()
    {
        try
        {
            if (DeviceModel == null) return;

            // 发送设备查找命令，让设备显示绿色指示灯
            var findCommand = new
            {
                MsgId = 44444,
                Time = DateTimeOffset.Now.ToUnixTimeMilliseconds(),
                Device = new
                {
                    SN = DeviceModel.DeviceSN,
                    Type = DeviceModel.TypeId,
                },
                CMD = 1, // Write命令
                Data = new
                {
                    FindCode = (byte)16,
                    FindColor = 0x4CAF50 // 绿色
                }
            };
            await StateService.PostAsJsonAsync<object>(
                "/api/v2/hx_experiment/Device/SendCommand", findCommand);
            await Task.Delay(1000);
            var startCommand = new
            {
                MsgId = 11111,
                Time = DateTimeOffset.Now.ToUnixTimeMilliseconds(),
                Device = new
                {
                    SN = DeviceModel.DeviceSN,
                    Type = DeviceModel.TypeId,
                },
                CMD = 1, // Write命令
                Data = new
                {
                    CollectMode = 1 // 连续采集模式
                }
            };
            await StateService.PostAsJsonAsync<object>(
                "/api/v2/hx_experiment/Device/SendCommand", startCommand);
        }
        catch (Exception ex)
        {
            // 设备命令发送失败不影响检测记录的创建
            Console.WriteLine($"发送设备命令失败: {ex.Message}");
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
    }

    protected async Task CancelAsync()
    {
        StudentModel = new StudentInfo();
        await CloseDialog.InvokeAsync();
    }
   
}
