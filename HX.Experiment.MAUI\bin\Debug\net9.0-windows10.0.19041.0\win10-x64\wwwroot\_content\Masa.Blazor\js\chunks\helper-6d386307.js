const e="$parent.",t="undefined"!=typeof window;let n=!1;try{if(t){const e=Object.defineProperty({},"passive",{get:()=>{n=!0}});window.addEventListener("testListener",e,e),window.removeEventListener("testListener",e,e)}}catch(e){console.warn(e)}function r(e){if(!e)return null;let t=e.getAttributeNames().find((e=>e.startsWith("_bl_")));return t&&(t=t.substring(4)),t}function i(e){const t=e,n={},r=t.getAttributeNames().find((e=>e.startsWith("_bl_")));return r?(n.elementReferenceId=r,n.selector=`[${r}]`):n.selector=function(e){if(e instanceof Element){for(var t=[];e.nodeType===Node.ELEMENT_NODE;){var n=e.nodeName.toLowerCase();if(e.id){n="#"+e.id,t.unshift(n);break}for(var r=e,i=1;r=r.previousElementSibling;)r.nodeName.toLowerCase()==n&&i++;1!=i&&(n+=":nth-of-type("+i+")"),t.unshift(n),e=e.parentNode}return t.join(" > ")}}(t),n.class=t.getAttribute("class"),n}function o(e){return"string"==typeof e?document.querySelector(e):e}function s(e,t="px"){return null==e||""===e?void 0:isNaN(+e)?String(e):isFinite(+e)?`${Number(e)}${t}`:void 0}function a(t){var n;if(function(t){return t.startsWith(e)}(t)){const r=t.replace(e,""),i=null===(n=document.querySelector(r))||void 0===n?void 0:n.parentElement;return i?i.classList.contains("m-btn__content")?i.parentElement:i:null}return null}function u(e){return a(e)||document.querySelector(e)}Object.freeze({enter:13,tab:9,delete:46,esc:27,space:32,up:38,down:40,left:37,right:39,end:35,home:36,del:46,backspace:8,insert:45,pageup:33,pagedown:34,shift:16});export{o as a,r as b,s as c,u as d,a as e,i as g};
//# sourceMappingURL=helper-6d386307.js.map
