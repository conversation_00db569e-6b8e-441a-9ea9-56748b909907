﻿.listContainer .ant-table-thead > tr > th{
    border-bottom:1px solid #ddd;
    border-top:1px solid #ddd;
    padding:5px 10px;
}
.page-Container{
    height:100%;
    display:flex;
    flex-direction:column;
}
.page-ContainerScroll {
    display: flex;
    flex-direction: column;
    flex:1;
    min-height:0;
    overflow: hidden;
}
.pageContainer{
    height:100%;
}
    .pageContainer .ant-input-number-input{
        text-align:center;
    }
    .page-ContainerScroll .listContainer {
        flex: 1;
        position: relative;
        min-height: 0;
    }
.paginationContainer {
    display:flex;
    justify-content: flex-start;
    align-items:center;
    height: 30px;
    background-color: #f5f5f5;
    border-top: 1px solid #ddd;
    /*border-left: 1px solid #ddd;*/
    padding-left: 10px;
}
/*.pageSearchContainer .label{
    white-space:nowrap;
}*/


.mobile-search {
    background-color: #fff;
    padding: 10px;
    position: relative;
    z-index: 100;
    font-size: 14px;
}

.search-input {
    position: relative;
    height: 32px;
    border: 1px solid #1877f2;
    border-radius: 5px;
    display: flex;
    align-items: center;
    /*margin-bottom: 10px;*/
}
.search-input input {
    height: 25px;
    margin-left: 5px;
    padding-left: 5px;
    flex: 1;
    margin-right: 5px;
    font-size: 14px;
}
.search-input .btn {
    margin-right: 2px;
    height: 28px;
    line-height: 28px;
    width: 60px;
    background-color: #1877f2;
    color: #fff;
    text-align: center;
    font-size: 14px;
    border-radius: 5px;
}
.search-tabs {
    display: flex;
}
.search-input + .search-tabs{
    margin-top:10px;
}
.search-tabs li {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666;
    margin-right: 14px;
}
.search-tabs li.active {
    color: #1877f2;
}
.search-tabs li.active .arrow {
    background-color: #1877f2;
    color: #fff;
}
.search-tabs li.active .arrow .iconfont {
    transform: rotate(180deg) scale(0.8);
}
.search-tabs li .arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 10px;
    background-color: #f5f5f5;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    text-align: center;
    color: #666;
}
.search-tabs li .arrow .iconfont {
    font-size: 12px;
    vertical-align: middle;
    transform: scale(0.8);
}


.mobile-search + .mask {
    content: '';
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: rgb(0, 0, 0);
    z-index: 99;
    opacity: 0.3;
}
.search-options {
    position: absolute;
    width: 100%;
    max-height: 170px;
    top: 100%;
    left: 0;
    background-color: #fff;
    padding: 0 15px;
    overflow: auto;
    z-index: 999;
    box-shadow: 0 2px 2px #eee;
}
.search-options li {
    height: 34px;
    display: flex;
    align-items: center;
    font-size: 14px;
    justify-content: space-between;
}
.search-options li .icon-ok {
    visibility: hidden;
}
.search-options li.active {
    color: #1877f2;
}
.search-options li.active .icon-ok {
    visibility: visible;
}
.search-datetime {
    padding: 0;
    overflow: inherit;
}
.search-datetime .datetime-input {
    margin: 15px 0;
    padding: 0 15px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.search-btns {
    display: flex;
    align-items: center;
    border-top: 1px solid #ddd;
    padding: 10px 10px;
    background-color: #fff;
}
.search-btns .clear {
    width: 20%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.search-btns .search {
    width: 80%;
    height: 40px;
    text-align: center;
    line-height: 40px;
    background-color: #1877f2;
    border-radius: 5px;
    color: #fff;
}


.data-list {
    flex: 1;
    /*height: calc(100vh - 125px);*/
    min-height: 0;
    /*padding: 10px 0;*/
    overflow:auto;
}
.data-list-item {
    width: 100%;
    margin: 0 10px;
    background-color: #fff;
    padding: 10px;
    border-radius: 5px;
}

.data-list-item li {
    line-height: 26px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ddd;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
}

.data-list-item li > label {
    color: #999;
}

.data-list-item li > span {
    color: #333;
}
.item-btns {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}
.item-btns span {
    display: inline-block;
    width: 65px;
    height: 25px;
    line-height: 23px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    margin-left: 10px;
}
.item-btns span.primary {
    color: #1877f2;
    border-color: #1877f2;
}
.item-btns span.danger {
    color: #fff;
    border-color: #f56c6c;
    background-color: #f56c6c;
}
.table-empty {
    height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}



.footer-btns {
    background-color: #fff;
    display: flex;
    align-items: center;
    box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.3);
    font-size: 14px;
}
.footer-btns li {
    flex: 1;
    height: 100%;
    text-align: center;
    padding: 0 10px;
    height: 35px;
    line-height: 35px;
}
.footer-btns li span {
    vertical-align: middle;
}

@media (max-width: 767px){
    .ant-modal-header{
        padding:10px;
    }
    .ant-modal-close-x {
        height: 43px;
        line-height: 43px;
    }
    .ant-modal-body{
        padding:10px;
    }
    .ant-form-item{
        margin-bottom:0px;
    }
    .ant-form-item .ant-form-item-label{
        padding:0!important;
    }
    .ant-form .ant-form-item {
        flex-wrap: wrap;
    }
    .ant-form .ant-form-item .ant-form-item-label, .ant-form .ant-form-item .ant-form-item-control {
        flex: 0 0 100%;
        max-width: 100%;
    }
    .ant-form-item .ant-form-item-label {
        padding: 0 0 8px;
        line-height: 1.5715;
        white-space: initial;
        text-align: left;
    }
    .ant-form-item .ant-form-item-label > label {
        margin: 0;
    }
    .ant-form .ant-form-item .ant-form-item-label, .ant-form .ant-form-item .ant-form-item-control {
        flex: 0 0 100%;
        max-width: 100%;
    }
} 
