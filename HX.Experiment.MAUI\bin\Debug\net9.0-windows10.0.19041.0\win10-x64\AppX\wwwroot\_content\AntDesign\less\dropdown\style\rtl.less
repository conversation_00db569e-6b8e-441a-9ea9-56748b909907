@import '../../style/themes/index';
@import '../../style/mixins/index';

@dropdown-prefix-cls: ~'@{ant-prefix}-dropdown';

.@{dropdown-prefix-cls} {
  &-rtl {
    direction: rtl;
  }

  &::before {
    .@{dropdown-prefix-cls}-rtl& {
      right: -7px;
      left: 0;
    }
  }

  &-menu {
    &&-rtl {
      direction: rtl;
      text-align: right;
    }

    &-item-group-title {
      .@{dropdown-prefix-cls}-rtl &,
      .@{dropdown-prefix-cls}-menu-submenu-rtl & {
        direction: rtl;
        text-align: right;
      }
    }

    &-submenu-popup {
      &.@{dropdown-prefix-cls}-menu-submenu-rtl {
        transform-origin: 100% 0;
      }

      ul,
      li {
        .@{dropdown-prefix-cls}-rtl & {
          text-align: right;
        }
      }
    }

    &-item,
    &-submenu-title {
      .@{dropdown-prefix-cls}-rtl & {
        text-align: right;
      }

      > .@{iconfont-css-prefix}:first-child,
      > span > .@{iconfont-css-prefix}:first-child {
        .@{dropdown-prefix-cls}-rtl & {
          margin-right: 0;
          margin-left: 8px;
        }
      }

      .@{dropdown-prefix-cls}-menu-submenu-expand-icon {
        .@{dropdown-prefix-cls}-rtl & {
          right: auto;
          left: @padding-xs;
        }

        .@{dropdown-prefix-cls}-menu-submenu-arrow-icon {
          .@{dropdown-prefix-cls}-rtl & {
            margin-left: 0 !important;
            transform: scaleX(-1);
          }
        }
      }
    }

    &-submenu-title {
      .@{dropdown-prefix-cls}-rtl & {
        padding-right: @control-padding-horizontal;
        padding-left: @control-padding-horizontal + @font-size-sm;
      }
    }

    &-submenu-vertical > & {
      .@{dropdown-prefix-cls}-rtl & {
        right: 100%;
        left: 0;
        margin-right: 4px;
        margin-left: 0;
      }
    }
  }
}
