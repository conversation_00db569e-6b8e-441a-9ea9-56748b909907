﻿@charset "UTF-8";
@import "//at.alicdn.com/t/font_2456522_l2zo8fze8x.css";

html,
body,
ul,
li,
ol,
dl,
dd,
dt,
p,
h1,
h2,
h3,
h4,
h5,
h6,
form,
fieldset,
legend,
img,
td {
    margin: 0;
    padding: 0;
}

fieldset,
img,
input,
button {
    border: none;
    padding: 0;
    margin: 0;
    outline-style: none;
}

img {
    border: 0;
    vertical-align: middle;
}

table {
    border-collapse: collapse;
}

html,
body {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

body {
    font-size: 12px;
    font-family: "微软雅黑", "Helvetica Neue", Helvetica, Arial, sans-serif;
    color: #666;
    background: #fff;
    line-height: 1;
}

.flex {
    display: flex;
}

.example {
    text-align: center;
    background: #fff;
    border-radius: 4px;
    margin-bottom: 20px;
    padding: 30px 50px;
    margin: 20px 0;
}

.clearfix:before,
.clearfix:after {
    content: "";
    display: table;
}

.clearfix:after {
    clear: both;
}

.clearfix {
    *zoom: 1;
}

input,
textarea {
    font-family: "微软雅黑", Arial, Helvetica, sans-serif;
}

a {
    color: #666;
    text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: normal;
    font-size: 100%;
}

s,
i,
em {
    font-style: normal
}

ul,
ol,
li {
    list-style: none;
}

input::-webkit-input-placeholder {
    /* WebKit, Blink, Edge */
    color: #ddd;
    font-size: 12px !important;
}

input:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 */
    color: #ddd;
    font-size: 12px !important;
}

input::-moz-placeholder {
    /* Mozilla Firefox 19+ */
    color: #ddd;
    font-size: 12px !important;
}

input:-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    color: #ddd;
    font-size: 12px !important;
}


/* base */
.mt5 {
    margin-top: 5px;
}

.mt10 {
    margin-top: 10px;
}

.mt15 {
    margin-top: 15px;
}

.mt20 {
    margin-top: 20px;
}

.mt25 {
    margin-top: 25px;
}

.mt30 {
    margin-top: 30px;
}

.ml5 {
    margin-left: 5px;
}

.ml10 {
    margin-left: 10px;
}

.ml15 {
    margin-left: 15px;
}

.ml20 {
    margin-left: 20px;
}

.ml25 {
    margin-left: 25px;
}

.ml30 {
    margin-left: 30px;
}

.mb5 {
    margin-bottom: 5px;
}

.mb10 {
    margin-bottom: 10px;
}

.mb15 {
    margin-bottom: 15px;
}

.mb20 {
    margin-bottom: 20px;
}

.mb25 {
    margin-bottom: 25px;
}

.mb30 {
    margin-bottom: 30px;
}

.mr5 {
    margin-bottom: 5px;
}

.mr10 {
    margin-bottom: 10px;
}

.mr15 {
    margin-bottom: 15px;
}

.mr20 {
    margin-bottom: 20px;
}

.mr25 {
    margin-bottom: 25px;
}

.mr30 {
    margin-bottom: 30px;
}


.red {
    color: red;
}

.mainColor {
    color: #3771e4;
}

.primary-color {
    color: #1877f2 !important;
}

.pointer {
    cursor: pointer;
}

.borderRNone {
    border-right: none !important;
}

.borderTNone {
    border-top: none !important;
}

.borderBNone {
    border-bottom: none !important;
}

.borderLNone {
    border-left: none !important;
}

::-webkit-scrollbar {
    width: 4px;
    height: 4px;
}

::-webkit-scrollbar-thumb {
    -webkit-box-shadow: inset 1px 1px 0 rgba(0, 0, 0, .1), inset 0 -1px 0 rgba(0, 0, 0, .07);
    background-clip: padding-box;
    background-color: #c9c9c9;
    min-height: 28px;
}

/* tab */
.baseTabs{
    height: 40px;
    border-bottom: 1px solid #ddd;
}
.baseTabs .tabs{
    display: flex;
    flex-direction: row;
    transition: transform .3s;
    height: 100%;
}
.baseTabs .tabs .nav-link{
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
    cursor: pointer;
    margin: 0 20px 0 0;
    padding: 0 5px;
}
.baseTabs .tabs .nav-link.active{
    color: #1877f2;
    cursor: default;
}
.baseTabs .tabs .nav-link.active::before{
    position: relative;
    content: "";
    position: absolute;
    bottom: -1px;
    left: 0;
    background-color: #1877f2;
    height: 2px;
    width: 100%;
}
.inputBoxBase {
    display: flex;
    flex-direction: row;
    align-items: center;
    position: relative;
}

.inputBoxBorderBase {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 32px;
    border: 1px solid #ddd;
    border-radius: 4px;
    flex: 1;
    padding: 6px;
}

    .inputBoxBorderBase input {
        flex: 1;
        font-size: 13px;
        color: #666;
    }

    .inputBoxBorderBase .downList {
        padding: 5px 0;
        position: absolute;
        top: 32px;
        left: 0;
        z-index: 1;
        background-color: #fff;
        width: 100%;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    }

        .inputBoxBorderBase .downList .li {
            font-size: 13px;
            padding: 0 20px;
            position: relative;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #606266;
            width: 100%;
            height: 34px;
            line-height: 34px;
            box-sizing: border-box;
            cursor: pointer;
        }

            .inputBoxBorderBase .downList .li:hover {
                background-color: #f5f7fa;
            }


.inputBase {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex: 1;
    height: 32px;
    padding: 6px;
    box-sizing: border-box;
    font-size: 13px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

    .inputBase .input {
        flex: 1;
        height: 100%;
    }

    .inputBase:hover, .inputBoxBorderBase:hover, .baseSelect:hover {
        border-color: #40a9ff;
    }

    .inputBase:focus, .baseSelect:focus {
        border-color: #40a9ff;
        border-right-width: 1px !important;
        outline: 0;
        box-shadow: 0 0 0 2px rgba(24,144,255,.2);
    }

.errorBase {
    display: block;
    margin: 5px 0;
    min-height: 12px;
    color: red;
    font-size: 12px;
}

.baseSelect {
    position: relative;
    flex: 1;
    height: 30px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0 5px;
    cursor: pointer;
    font-size: 13px;
    width: 100%;
}

    .baseSelect option {
        line-height: 30px;
    }

.iconeyes {
    display: flex;
    padding-right: 10px;
    padding-left: 4px;
    color: #999;
    height: 80%;
    align-items: center;
}

input::-webkit-input-placeholder { /* WebKit, Blink, Edge */
    color:#ddd;
}
input:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
   color:#ddd;
}
input::-moz-placeholder { /* Mozilla Firefox 19+ */
   color:#ddd;
}
input:-ms-input-placeholder { /* Internet Explorer 10-11 */
   color:#ddd;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}


input[type="number"]{
    -moz-appearance: textfield;
}

input{
    color: #333;
}

.c-cacaca{
    color: #cacaca;   
}
.c-f5222d{
    color: #f5222d;    
}
.c-fe9900{
    color: #fe9900;    
}
.c-1bbe6b{
    color: #1bbe6b;    
}
.c-3771e4{
    color: #3771e4;    
}
.c-1877F2{
    color: #1877F2;    
}
.c-ff2af3{
    color: #ff2af3;    
}

.pointer{
    cursor: pointer;
}
.border-bottom-none{
    border-bottom: none !important;
}
.border-bottom{
    border-bottom: 1px solid #ddd;
}
.red{
    color: red !important;
}
.green{
    color: green;
}
.yellow{
    color: yellow;
}
.c999{
    color: #999;
}
.orange{
    color: orange;
}


.empty{
    margin: 30px 0;
    text-align: center;
    color: rgba(0,0,0,.25);
    font-size: 13px;
}
.loading{
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 160px;
    padding: 80px 0;
    height: 100%;
    width: 100%;
    text-align: center;
    color: rgba(0,0,0,.5);
    font-size: 24px;
    background-color: #fff;
}

.icon-tb{
    color: #FF4200;
}
.icon-alibaba{
    color: #FF5200;
}
.icon-pdd{
    color: #F40009;
}
.icon-doudian{
    color: #111111;
}
.icon-xianxiadianpu{
    color: #E12319;
}


.c_checkbox{
    margin-right: 5px;
    cursor: pointer;
    width: 12px;
    height: 12px;
    position: relative;
    
}
  
.c_checkbox:after {
    content: " ";
    font-size: 12px;
    position: absolute;
    width: 12px;
    height: 12px;
    top: 0;
    background-color: #fff;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    visibility: visible;
    border-radius: 2px;
    border: 1px solid #ddd;
    
    font-family: "iconfont" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.c_checkbox:disabled:after {
    border-color: #dcdfe6;
    background-color:#edf2fc
}
.c_checkbox:checked:after {
content: "\e640";
font-size: 12px;
background-color: #1877F2;
color: #fff;
border: 1px solid #1877F2;
}

/* 搜索列表 */
.baseSearchForm {
    padding: 10px;
}

    .baseSearchForm .list {
        display: flex;
        flex-direction: row;
        align-items: center;
    }

        .baseSearchForm .list li {
            margin-right: 10px;
        }

            .baseSearchForm .list li:last-child {
                margin-right: 0;
            }

    .baseSearchForm .submit {
        height: 32px;
        border-radius: 4px;
        background-color: #1877F2;
        border: none;
        cursor: pointer;
        min-width: 80px;
        color: #fff;
        font-size: 13px;
    }

        .baseSearchForm .submit:hover {
            opacity: 0.8;
        }

    .baseSearchForm .clear {
        height: 32px;
        border-radius: 4px;
        background-color: #fff;
        border: 1px solid #ddd;
        cursor: pointer;
        min-width: 80px;
        color: #999;
        font-size: 13px;
    }

        .baseSearchForm .clear:hover {
            background-color: #f2f2f2;
        }
/* 操作选项列表 */
.page-operation {
    display: flex;
    color: #fff;
    padding: 0 5px;
    background-color: #fff;
    border-bottom: 1px solid #ddd;
}

    .page-operation > li {
        padding: 0 10px;
        margin: 5px;
        height: 30px;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #1877F2;
        cursor: pointer;
        white-space: nowrap;
        position: relative;
    }

        .page-operation > li i {
            margin-right: 3px;
            font-size: 15px;
        }

        .page-operation > li.light-blue {
            background-color: #429AFF;
        }

        .page-operation > li.slight-blue {
            background-color: #6BB5FF;
        }

        .page-operation > li.light-red {
            background-color: #F56C6C;
        }


/* 表格列表布局 */
.item-list {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
}

.page-table {
    width: 100%;
    /* max-height: 100%;*/
    background-color: #fff;
    /*    overflow-y: auto;*/
}

    .page-table .thead {
        background-color: #f5f5f5;
        height: 30px;
        line-height: 30px;
        border-top: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
    }

    .page-table .tbody {
        background-color: #fff;
    }

    .page-table .table-tr {
        padding: 10px 0;
        border-bottom: 1px solid #ddd;
    }

    .page-table .item {
        box-sizing: border-box;
        padding: 0 10px;
    }

    .page-table .item-center {
        text-align: center;
    }

    .page-table .btn {
        padding: 0 10px;
        height: 24px;
        border-radius: 4px;
        color: #fff;
        cursor: pointer;
        margin-right: 5px;
    }

        .page-table .btn:hover {
            opacity: 0.8;
        }

        .page-table .btn.btnDetail {
            background-color: #1877F2;
        }

        .page-table .btn.btnDel {
            background-color: #F56C6C;
        }

.errorBase.visible {
    visibility: visible;
}

.errorBase.hidden {
    visibility: hidden;
}


/* 左侧菜单及布局 */
.erp-pageContainer {
    display: flex;
    background-color: #f5f5f5;
    height: 100%;
    width: 100%;
    overflow: hidden;
}

.erp-content {
    flex: 1;
    height: 100vh;
    min-width: 0;
}

.erp-aside-container {
    transition: all 0.3s;
    min-width: 200px;
    max-width: 200px;
    height: 100vh;
    background-color: #fff;
    border-right: 1px solid #ddd;
}

    .erp-aside-container.collapse {
        min-width: 56px;
        max-width: 56px;
    }

        .erp-aside-container.collapse .erp-aside-menuItem {
            margin: 0 auto;
            width: 28px;
        }

.erp-aside-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 56px;
    background-color: #1877F2;
    overflow: hidden;
}

    .erp-aside-logo .icon-haoyongERPzuoyou {
        font-size: 160px;
        color: #fff;
    }

    .erp-aside-logo .icon-logo {
        font-size: 40px;
        color: #fff;
    }

.erp-aside-menu {
    height: calc(100vh - 56px - 30px);
    overflow-y: auto;
}

    .erp-aside-menu li {
        margin: 8px 0;
    }

    .erp-aside-menu .erp-aside-menuItem {
        position: relative;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding-left: 20px;
        height: 40px;
        font-size: 14px;
        color: #666;
    }


        .erp-aside-menu .erp-aside-menuItem:hover .erp-aside-childMenuCollapse {
            display: block;
        }

        .erp-aside-menu .erp-aside-menuItem .move {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            z-index: 9;
        }

        .erp-aside-menu .erp-aside-menuItem.active {
            color: #1877F2;
        }

            .erp-aside-menu .erp-aside-menuItem.active .iconAside {
                color: #1877F2;
            }

        .erp-aside-menu .erp-aside-menuItem > .iconAside {
            padding-right: 8px;
            font-size: 20px;
            color: #666;
        }

    .erp-aside-menu .erp-aside-childMenu {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        padding-left: 20px;
    }

        .erp-aside-menu .erp-aside-childMenu .erp-aside-childMenuItemName {
            width: 50%;
            line-height: 30px;
            font-size: 12px;
            color: #666;
            position:relative;
        }

            .erp-aside-menu .erp-aside-childMenu .erp-aside-childMenuItemName.active {
                color: #1877F2;
            }

            .erp-aside-menu .erp-aside-childMenu .erp-aside-childMenuItemName:hover {
                color: #1877F2;
            }
            .erp-aside-menu .erp-aside-childMenu .erp-aside-childMenuItemName .num {
                position: absolute;
                top: -5px;
                left: 45px;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 18px;
                border-radius: 9px;
                color: #fff;
                background-color: red;
                min-width: 18px;
                padding: 0 2px;
            }
            .erp-aside-fold {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                height: 30px;
                background-color: #f5f5f5;
                cursor: pointer;
            }

    .erp-aside-fold .iconFold {
        font-size: 16px;
        color: #999;
        font-weight: bold;
    }

.erp-aside-container.collapse .erp-aside-menu li {
    margin: 8px 0;
}

.erp-aside-container.collapse .erp-aside-menu .erp-aside-menuItem {
    box-sizing: content-box;
    padding: 10px 0;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

    .erp-aside-container.collapse .erp-aside-menu .erp-aside-menuItem:hover {
        background-color: #f7f8fa;
        color: #1877F2;
        cursor: pointer;
    }


        .erp-aside-container.collapse .erp-aside-menu .erp-aside-menuItem:hover .iconAside {
            color: #1877F2;
        }

    .erp-aside-container.collapse .erp-aside-menu .erp-aside-menuItem .iconAside {
        padding-right: 0;
        padding-bottom: 5px;
    }

.erp-aside-childMenuCollapse {
    position: fixed;
    left: 70px;
    top: 0;
    z-index: 99;
    padding: 12px 20px;
    width: 500px;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 3px 0px 6px 0px rgba(0, 0, 0, 0.15);
    display: none;
}

    .erp-aside-childMenuCollapse::before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 100%;
        position: absolute;
        left: -16px;
        top: 0;
        opacity: 0;
    }

    .erp-aside-childMenuCollapse .menuItem {
        display: flex;
        flex-direction: row;
        align-items: center;
        font-size: 14px;
        font-weight: bold;
        color: #666;
        line-height: 32px;
    }

        .erp-aside-childMenuCollapse .menuItem .iconAside {
            padding-right: 8px;
            font-size: 18px;
            color: #666;
        }

    .erp-aside-childMenuCollapse .childMenu {
        column-width: 120px;
    }

        .erp-aside-childMenuCollapse .childMenu > p {
            width: 100%;
            border-right: 1px solid #eee;
        }

        .erp-aside-childMenuCollapse .childMenu .erp-aside-childMenuItemName {
            display: inline-block;
            height: 34px;
            line-height: 34px;
            font-size: 12px;
        }

            .erp-aside-childMenuCollapse .childMenu .erp-aside-childMenuItemName.active {
                color: #1877F2;
            }

            .erp-aside-childMenuCollapse .childMenu .erp-aside-childMenuItemName:hover {
                color: #1877F2;
            }

            .erp-aside-childMenuCollapse .childMenu .erp-aside-childMenuItemName .iconAside {
                padding-right: 5px;
            }


/* antd */
.ant-modal-footer {
    border-top: none !important;
}

    .ant-modal-footer .ant-btn-primary {
        background: #1877F2 !important;
        border-radius: 4px !important;
        min-width: 70px !important;
        font-size: 13px !important;
    }

    .ant-modal-footer .ant-btn-default {
        min-width: 70px !important;
        font-size: 13px !important;
    }

.ant-checkbox-wrapper, .ant-select-item, .ant-select {
    font-size: 12px !important;
    align-items: center;
}

/*login*/
.loginFormContainer .ant-spin-nested-loading {
    width: 100%;
}

.loginFormContainer .formInput {
    height: 38px;
    width: 100%;
}

.loginFormContainer .ant-col {
    flex: 1 !important;
    max-width: 100% !important;
}

.loginFormContainer .submit {
    width: 100%;
    height: 40px;
    background: #3771e4;
    border-radius: 4px;
    color: #fff;
    text-align: center;
    cursor: pointer;
}

.loginFormContainer .ant-col-offset-8 {
    margin-left: 0 !important;
}

.loginFormContainer .code {
    color: #3771e4;
    font-size: 14px;
    cursor: pointer;
}

.loginFormContainer .nav-tabs {
    margin-bottom: 40px;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

    .loginFormContainer .nav-tabs .nav-link {
        position: relative;
        font-size: 14px;
        font-weight: 400;
        cursor: pointer;
    }

        .loginFormContainer .nav-tabs .nav-link.active {
            color: #3771e4;
        }

            .loginFormContainer .nav-tabs .nav-link.active::after {
                content: "";
                display: block;
                position: absolute;
                width: 80px;
                height: 2px;
                background: #3771e4;
                bottom: -11px;
                left: 50%;
                transform: translateX(-50%);
            }

.loginFormContainer .nav-tabs-body {
    width: 100%;
}

.loginFormContainer {
    width: 370px;
    display: flex;
    padding: 0 60px 30px;
    flex-direction: column;
    align-items: center;
    background: #fff;
    border-radius: 10px;
    position: relative;
    color: #333;
}

    .loginFormContainer .tit {
        font-size: 24px;
        font-weight: 700;
        padding: 34px 0 40px 0;
    }

    .loginFormContainer .foot {
        width: 100%;
        color: #999;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }

    .loginFormContainer .loginForm {
        width: 100%;
    }

        .loginFormContainer .loginForm .formInput {
            display: flex;
            flex-direction: row;
            align-items: center;
            width: 100%;
            height: 40px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

            .loginFormContainer .loginForm .formInput .inputText {
                flex: 1;
                font-size: 13px;
                color: #666;
            }

            .loginFormContainer .loginForm .formInput .code {
                width: 120px;
                flex: auto;
            }

            .loginFormContainer .loginForm .formInput .iconfont {
                font-size: 18px;
                color: #999;
                padding: 0 5px;
            }

        .loginFormContainer .loginForm .submit {
            margin: 0 0 15px 0;
            width: 100%;
            height: 40px;
            background-color: #3771E4;
            border-radius: 4px;
            font-size: 14px;
        }

        .loginFormContainer .loginForm .getCode,
        .loginFormContainer .loginForm .count {
            padding-right: 6px;
            font-size: 13px;
            color: #3771E4;
            text-align: right;
        }

        .loginFormContainer .loginForm .agree {
            font-size: 12px;
        }

        .loginFormContainer .loginForm .count {
            color: #666;
        }

.getCode {
    color: #3771E4;
}

.verifyContainer {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    top: 0;
    left: 0;
    z-index: 9;
    width: 100%;
    height: 100%;
    padding: 20px;
    border-radius: 10px;
    background-color: #fff;
}

@media screen and (max-width: 640px) {

    .loginContent {
        position: absolute;
        width: 100%;
        transform: translate(-50%, -50%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index:99;
    }

        .loginContent .loginLeft {
            display: none;
        }

        .loginContent .loginFormContainer {
        }
}




/* 个人中心 */
.pageAccount {
    height: 100%;
    overflow: auto;
}

    .pageAccount .content {
        padding: 20px;
    }

.userInfo .ant-btn-round.ant-btn-sm {
    font-size: 12px
}

.userInfo {
    background-color: #fff;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    border-radius: 10px;
}

.headPortrait {
    display: flex;
    align-items: center;
    padding-right: 80px;
    border-right: 1px solid #ededed;
}

    .headPortrait .nikeName .text {
        line-height: 2.5;
        font-weight: 600;
        color: #333333;
    }

.userInfo .portrait img {
    margin-right: 10px;
    height: 80px;
    width: 80px;
    background-color: #ccc;
    border-radius: 50%;
    min-width: 80px;
}

.userInfo .info {
    width: 800px;
    display: flex;
    flex-direction: column;
    font-size: 12px;
    color: #333;
    margin-left: 80px;
}

.personalInfo {
    padding: 10px 0;
    background-color: #fff;
    width: 100%;
    margin-bottom: 20px;
    border-radius: 10px;
}

    .personalInfo .personalTitle {
        width: 100%;
        border-bottom: 1px solid #ededed;
        padding: 5px 0;
        display: flex;
        align-items: center;
    }

.personalTitle .erpfont {
    padding: 0 15px;
    font-size: 20px;
}

.personalInfo .personalTitle .mes {
    color: #333;
}

.personalInfo .personalContent {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
}

.personalContent li .info_item {
    display: flex;
}

.personalContent li {
    width: 50%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 35px 0 15px;
    margin: 20px 0;
}

    .personalContent li:nth-child(odd) {
        border-right: 1px solid #ededed;
    }

    .personalContent li .erpfont {
        display: inline-block;
        width: 40px;
        height: 40px;
        background-color: #1890ff;
        font-size: 25px;
        line-height: 40px;
        text-align: center;
        color: #fff;
        border-radius: 5px;
        margin-right: 20px;
        min-width: 40px;
    }

    .personalContent li .info_name {
        height: 40px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

        .personalContent li .info_name p {
            color: #333;
        }

        .personalContent li .info_name span {
            color: #999;
        }

.userInfo .info li {
    display: flex;
    flex-direction: row;
    line-height: 26px;
    width: 260px;
}

    .userInfo .info li .inputBase {
        height: 26px;
    }

    .userInfo .info li .label {
        color: #999;
        width: 65px;
        text-align: right;
    }

    .userInfo .info li .icon-edit, .userInfo .info li .icon-baocun {
        margin-left: 10px;
        font-size: 13px;
        color: #999;
        cursor: pointer;
        visibility: hidden;
    }

    .userInfo .info li:hover .icon-edit {
        visibility: visible;
    }

    .userInfo .info li .icon-baocun {
        visibility: visible;
        font-size: 18px;
        color: #1877F2;
    }

.userInfo .info .ant-select {
    font-size: 12px !important;
}

.userInfo .info .ant-select-selector {
    height: 26px !important;
}

.userInfo .info .ant-select-selection-item {
    line-height: 24px !important;
}

.organContainer {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    background-color: #fff;
    padding: 15px;
}

    .organContainer .organ {
        box-sizing: border-box;
        width: 300px;
        height: 190px;
        border: 1px solid #ddd;
        border-radius: 8px;
        margin: 0 20px 20px 0;
        overflow: hidden;
        color: #333;
    }

        .organContainer .organ .hd {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 5px 15px;
            height: 35px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #ddd;
        }


            .organContainer .organ .hd .status {
                color: rgb(25,145,255);
            }

        .organContainer .organ .bd .status {
            width: 80px;
            height: 30px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #E6F5FF;
            border: 1px solid #94CDFF;
            color: #1877F2;
            margin-right: -22px;
        }

            .organContainer .organ .bd .status.set {
                background-color: #f9fafb;
                border-color: #e6f3ff;
                color: #8eb6eb;
            }

        .organContainer .organ .hd .status.disable {
            background-color: #FFF2F0;
            border: 1px solid #FFC6C2;
            color: #F56C6C;
        }

        .organContainer .organ .hd .opt {
            width: 60px;
            height: 30px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #1877F2;
            color: #fff;
            cursor: pointer;
        }

        .organContainer .organ .bd {
            display: flex;
            justify-content: space-between;
            padding: 10px;
        }

            .organContainer .organ .bd .bd_list {
                line-height: 26px;
                display: flex;
                flex-direction: column;
            }

            .organContainer .organ .bd p {
                display: flex;
                flex-direction: row;
            }

            .organContainer .organ .bd .label {
                color: #999;
                width: 50px;
                text-align: right;
            }

        .organContainer .organ .add {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #1877F2;
            font-size: 13px;
            cursor: pointer;
        }

            .organContainer .organ .add .icon-add-circle {
                padding-right: 5px;
                color: #1877F2;
                font-size: 16px;
            }

/* 加入公司弹窗 */
.addCompanyContainer {
}

    .addCompanyContainer .form {
        margin-top: 25px;
    }

    .addCompanyContainer li .label {
        width: 80px;
        color: #999;
        font-size: 13px;
    }

    .addCompanyContainer .errorBase {
        padding-left: 80px;
    }

/* 修改密码弹窗 */
.updatePasswordModal {
    font-size: 12px;
}

    .updatePasswordModal li .label {
        width: 80px;
        text-align: right;
        color: #999;
    }

.changeForm {
    color: #1877F2;
    font-size: 12px;
    margin-bottom: 15px;
    cursor: pointer;
}

.updatePasswordModal .submit {
    width: 100%;
    height: 40px;
    background: #3771e4;
    border-radius: 4px;
    color: #fff;
    text-align: center;
    cursor: pointer;
}

/* 首页 */
.pageHome {
    font-size: 13px;
    height: calc(100vh - 34px);
    overflow-y: auto;
    padding: 15px 0 0 15px;
    width: 100%;
}

.homeTitle {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    line-height: 16px;
    font-size: 16px;
    color: #333;
}

    .homeTitle .text {
        padding-left: 13px;
        position: relative;
        font-size: 16px;
        color: #333;
        line-height: 16px;
    }

        .homeTitle .text::before {
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 16px;
            background-color: #1877F2;
            content: "";
        }

    .homeTitle .update {
        font-size: 14px;
    }

.homeTitle_left {
    display: flex;
}

.homeBox {
    padding: 15px;
    background-color: #fff;
}

.pageHome .statistics {
    background-color: #fff;
    height: 134px;
    color: #333;
}

.statistics .list {
    display: flex;
    flex-direction: row;
    margin-top: 20px;
}

    .statistics .list li {
        display: flex;
        flex-direction: column;
        flex: 1;
    }

        .statistics .list li .label {
            font-size: 13px;
        }

            .statistics .list li .label::before {
                display: inline-block;
                width: 10px;
                height: 10px;
                background-color: #1877F2;
                content: "";
                border-radius: 50%;
                margin-right: 5px;
            }

        .statistics .list li:nth-child(1) .label::before {
            background-color: #3771E4;
        }

        .statistics .list li:nth-child(2) .label::before {
            background-color: #7563FF;
        }

        .statistics .list li:nth-child(3) .label::before {
            background-color: #33D3DD;
        }

        .statistics .list li:nth-child(4) .label::before {
            background-color: #FC6D94;
        }

        .statistics .list li:nth-child(5) .label::before {
            background-color: #44D655;
        }

        .statistics .list li:nth-child(6) .label::before {
            background-color: #FF9D3D;
        }

        .statistics .list li:nth-child(7) .label::before {
            background-color: #8080FF;
        }

        .statistics .list li:nth-child(8) .label::before {
            background-color: #36B1ED;
        }

        .statistics .list li .number {
            font-size: 20px;
            padding-left: 15px;
            padding-top: 15px;
        }

.pageHome .content {
    margin-top: 15px;
    width: 100%;
    display: flex;
    flex-direction: row;
}

.content .left, .content .center, .content .right {
}

.content .left {
    width: 450px;
}

.content .center {
    flex: 1;
    margin: 0 15px;
}

.content .right {
    width: 450px;
    min-width: 450px;
}

.left .banner {
    height: 300px;
    width: 450px;
    min-width: 450px;
    background-color: #fff;
}

    .left .banner img {
        width: 100%;
        height: 100%;
    }

.left .notice {
    margin-top: 15px;
    height: 380px;
    font-size: 13px;
    color: #333;
}

.notice .list {
    margin-top: 20px;
    min-height: 300px;
    height: 300px;
    overflow-y: scroll;
    word-break: break-all;
}

    .notice .list li {
        margin-bottom: 20px;
    }

        .notice .list li:last-child {
            margin-bottom: 0;
        }

        .notice .list li .hd {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }

            .notice .list li .hd .text {
                font-weight: bold;
                flex: 1;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
            }

            .notice .list li .hd .dateTime {
                color: #999;
                font-weight: normal;
                width: 135px;
                text-align: right;
            }

        .notice .list li .bd {
            line-height: 1.8;
        }

.center .navs {
    height: 300px;
    /* min-width: 650px*/
}

    .center .navs .list {
        min-width: 480px;
        margin-top: 30px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        overflow-y: auto;
    }

        .center .navs .list li {
            margin-bottom: 25px;
            width: 20%;
            display: flex;
            justify-content: center;
        }

        .center .navs .list .nav {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            font-size: 0.9rem;
            text-align: center;
        }

            .center .navs .list .nav:hover {
                background-color: #F5F5F5;
            }

            .center .navs .list .nav .erpfont {
                margin-bottom: 8px;
                font-size: 28px;
                color: #999;
            }

.center .orderData {
    margin-top: 15px;
    height: 170px;
}

    .center .orderData .bd {
        margin-top: 20px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        text-align: center;
    }

.bd .icon-logo1 {
    color: #40a9ff;
}

.center .orderData .bd .li_item div {
    margin: 5px 0;
    font-weight: 400;
    color: #333333;
}

.center .orderData .bd .li_item span {
    font-weight: 400;
    color: #333333;
}

.center .orderData .bd .erpfont {
    font-size: 65px;
}

.center .orderData .bd .icon-chunlogo {
    color: rgb(24,119,242);
}

.center .orderData .bd .icon-cainiao {
    color: #1877F2;
}

.center .orderData .bd .icon-pinduoduo2 {
    color: #D50D00;
}

.center .orderData .bd .icon-douyin {
    color: #000;
}

.center .tool {
    margin-top: 15px;
    height: 195px;
}

    .center .tool .bd {
        margin: 10px;
        display: flex;
        justify-content: space-between;
    }

        .center .tool .bd .li {
            margin-bottom: 20px;
            display: flex;
            flex-direction: column;
        }

            .center .tool .bd .li:last-child {
                margin-bottom: 0;
            }

            .center .tool .bd .li .label {
                width: 94px;
                text-align: right;
                color: #999;
                margin-right: 10px;
            }

            .center .tool .bd .li .text {
                font-weight: bold;
                color: #333;
            }

            .center .tool .bd .li .down {
                display: flex;
                flex-direction: column;
                margin-left: -10px;
            }

                .center .tool .bd .li .down .link {
                    line-height: 30px
                }

                    .center .tool .bd .li .down .link:last-child {
                        margin-right: 0;
                    }



        .center .tool .bd .codes li {
            margin-right: 60px;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 auto;
        }

        .center .tool .bd .codes {
            display: flex;
            flex-direction: row;
            width: 75%;
            text-align: center;
        }

            .center .tool .bd .codes li img {
                width: 6.4rem;
            }

.right .stores {
    height: 300px;
}

.right .item-list {
    display: flex;
    flex-direction: row;
}

    .right .item-list .item-1 {
        padding: 0 10px;
        width: 190px;
    }

    .right .item-list .item-2 {
        width: 105px;
    }

    .right .item-list .item-3 {
        width: 70px;
    }

.stores .table {
    margin-top: 20px;
    border: 1px solid #ddd;
}

    .stores .table .hd {
        height: 40px;
        line-height: 40px;
        background-color: #F5F5F5;
    }

    .stores .table .bd {
        max-height: 170px;
        overflow-y: auto;
    }

    .stores .table .noData {
        display: inline-block;
        padding: 20px;
        color: #999;
    }

    .stores .table .br {
        height: 40px;
        line-height: 40px;
        border-bottom: 1px solid #ddd;
    }

        .stores .table .br:last-child {
            border-bottom: none;
        }

.stores .note {
    margin-top: 10px;
    font-size: 13px;
}

.right .contact {
    margin-top: 15px;
    height: 380px;
}

    .right .contact .bd {
        margin-top: 20px;
    }

        .right .contact .bd .online {
            display: flex;
            flex-direction: row;
            align-items: center;
        }

            .right .contact .bd .online .list {
                display: flex;
                flex-direction: row;
                align-items: center;
            }

                .right .contact .bd .online .list .link {
                    margin-right: 10px;
                }

            .right .contact .bd .online .time {
                display: flex;
                flex-direction: column;
                line-height: 22px;
            }

.online .icon-kefu1 {
    font-size: 40px;
    color: #1877F2;
}

.right .contact .bd .form {
    margin-top: 15px;
}

    .right .contact .bd .form ul {
        margin-top: 10px;
    }

    .right .contact .bd .form li {
        margin-bottom: 15px;
    }

    .right .contact .bd .form .submit {
        width: 100px;
        height: 40px;
        background-color: #1877F2;
        border-radius: 4px;
        font-size: 14px;
        color: #fff;
        cursor: pointer;
    }

.showBottom {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50px;
    background: #ffffff;
    box-shadow: 0px 0px 3px 0px rgba(51, 51, 51, 0.35);
    z-index:9999;
}

    .showBottom .bottom {
        height: 50px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        text-align: center;
    }

        .showBottom .bottom .iconfont {
            font-size: 20px;
        }

        .showBottom .bottom a {
            font-size: 12px;
        }
/*底部的统计表*/
    .cbottom {
    width: 100%;
    height: 380px;
    display: flex;
    justify-content: space-between;
    align-self: center;
    margin-top: 15px;
}

        .cbottom .bottom_left {
            height: 100%;
            /*min-width: 1115px;*/
            flex: 1;
            overflow: hidden;
        }

        .cbottom .bottom_left .chart1 {
            height: 100%;
            background-color: #fff;
            padding-right: 25px;
        }

            .cbottom .bottom_left .chart1 canvas {
                height: 320px !important;
            }

    .cbottom .bottom_right {
        margin-left: 15px;
       
        min-width: 450px;
        width: 450px;
        
        background-color: #fff;
    }

        .cbottom .bottom_right .timeBtn {
            text-align: end;
        }

        .cbottom .bottom_right .rank_content .image {
            width: 60px;
            min-width: 60px;
            height: 60px;
            border-radius: 5px;
        }

            .cbottom .bottom_right .rank_content .image img {
                width: 100%;
                border-radius: 5px;
            }

        .cbottom .bottom_right .rank_content {
            display: flex;
            margin-bottom: 10px;
        }

            .cbottom .bottom_right .rank_content .rank_right {
                height: 60px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                margin-left: 10px;
                flex: 1;
            }

        .cbottom .bottom_right .ranking {
            height: 80%;
            overflow: auto;
            margin: 0 15px;
        }

        .cbottom .bottom_right .rank_content .rank_right .introduce {
            font-weight: 400;
            color: #333333;
            word-wrap: break-word;
            word-break: normal;
            line-height: 1.3;
        }

    .cbottom .size {
        font-weight: 400;
        color: #3771E4;
        line-height: 9px;
    }

    .cbottom .num {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 5px;
    }

.logo {
    height: 50px;
    line-height: 50px;
    text-align: center;
    width: 100%;
    font-weight: 400;
    color: #333333;
}

.statistics {
    /*min-width: 1580px;*/
}
/*平板适配首页*/
@media screen and (max-width:1280px) {
    .pageContainer {
        width: 100%;
        padding: 0;
        margin: 0;
        overflow-y: auto;
        overflow-x: auto;
    }

    .statistics {
        padding: 10px 15px;
        margin: 10px;
        border-radius: 5px;
        height: auto !important;
        min-width: 905px;
    }

        .statistics .list {
            display: flex;
            margin-top: 20px;
        }

            .statistics .list li {
                display: inline-block;
            }

                .statistics .list li .number {
                    display: block;
                    font-size: 0.9rem;
                    padding-top: 10px;
                    padding-bottom: 10px;
                }

    .pageHome .content {
        width: 100%;
        overflow: auto;
        margin-top: 10px;
        display: block;
        min-width: 925px;
    }

    .cbottom {
        margin-top: 0px;
        width: auto;
        height: auto;
        margin-left: 10px;
        margin-right: 10px;
        min-width: 905px;
    }

        .cbottom .bottom_left {
            min-width: 0;
            height: 370px;
            width: 49.5%;
            flex: none;
        }

        .cbottom .bottom_right {
            min-width: 0;
            margin-left: 0;
            height: 370px;
            width: 49.5%;
        }

    .content .left {
        display: flex;
        width: auto;
        height: auto;
        margin-left: 10px;
        margin-right: 10px;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;
    }

        .content .left .banner {
            min-width: 0;
            height: 370px;
            width: 49.5%
        }

        .content .left .notice {
            min-width: 0;
            height: 370px;
            margin-top: 0;
            width: 49.5%
        }

    .content .center {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 10px;
        height: auto;
    }

    .center .orderData {
        margin-top: 0;
    }

    .content .center .navs {
        height: 380px;
        width: 49.5%;
        min-width: 0;
    }

        .content .center .navs .list {
            min-width: 0;
        }

    .content .center .center_right {
        height: 380px;
        width: 49.5%
    }

    .center .navs .list li {
        width: 25%;
    }

    .content .right {
        width: auto;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 10px;
    }

        .content .right .stores {
            height: 320px;
            width: 49.5%
        }

        .content .right .contact {
            margin-top: 0;
            height: 320px;
            width: 49.5%
        }
}

.right .contact .bd .form .submit {
    width: 100px;
    height: 40px;
    background-color: #1877F2;
    border-radius: 4px;
    font-size: 14px;
    color: #fff;
    cursor: pointer;
}

.right .contact .bd .form .submit {
    width: 100px;
    height: 40px;
    background-color: #1877F2;
    border-radius: 4px;
    font-size: 14px;
    color: #fff;
    cursor: pointer;
}

@media screen and (max-width:1100px) {
    .statistics .list {
        display: block;
        margin-top: 20px;
    }

        .statistics .list li {
            width: 24%;
            display: inline-block;
        }
}

@media screen and (max-width:800px) {
    .pageContainer {
        height: calc(100% - 84px);
    }

    .showBottom {
        display: inline-block;
    }

    .erp-aside-container {
        display: none;
    }
}
/*移动端首页*/
@media screen and (max-width:600px) {
    .pageContainer {
        min-width: 0;
        overflow-x: hidden;
    }

    .homeTitle_left {
        display: block;
    }

    .center .tool .bd .codes {
        display: flex;
        flex-direction: row;
        width: 69%;
        text-align: center;
    }

        .center .tool .bd .codes li img {
            width: 4.6rem;
        }
    /*数据面板*/
    .statistics {
        min-width: 0;
        padding: 0 15px;
        background: #ffffff;
        margin: 10px;
        border-radius: 5px;
        height: auto !important;
    }

        .statistics .homeTitle {
            padding: 10px 0;
            display: block;
        }

        .statistics .text {
            display: flex
        }

        .statistics .homeTitle .right {
            display: block;
            margin-top: 15px;
            margin-bottom: 15px;
            color: #999999;
        }

        .statistics .homeTitle .mainColor {
            display: none;
        }

        .statistics .list {
            display: block;
            margin-top: 0;
            margin-bottom: 10px;
        }

            .statistics .list li {
                width: 49%;
                display: inline-block;
            }



    .cbottom {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0;
        margin-bottom: 10px;
        min-width: 0;
    }

        .cbottom .bottom_left {
            width: -webkit-fill-available;
            height: auto;
            margin: 0 10px;
            height: 370px;
        }

        .cbottom .bottom_right {
            width: -webkit-fill-available;
            height: auto;
            margin: 0 10px;
            margin-top: 10px;
            height: 370px;
        }
    /*轮播图*/
    .pageHome .content {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        overflow: hidden;
        min-width: 0;
    }

    .content .left {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0;
    }

        .content .left .banner {
            width: auto;
            height: auto;
            margin: 0 10px;
            background-color: none !important;
        }

            .content .left .banner img {
                border-radius: 10px;
            }

        .content .left .notice {
            width: -webkit-fill-available;
            margin: 10px;
            border-radius: 5px;
        }

    .content .center {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0;
    }

        .content .center .navs {
            width: -webkit-fill-available;
            border-radius: 5px;
            margin: 0 10px;
        }

            .content .center .navs .list {
                min-width: 315px;
                overflow: hidden;
            }

        .content .center .center_right {
            width: 100%;
            height: auto;
        }

    .content .orderData {
        border-radius: 5px;
        margin: 10px;
    }

    .content .tool {
        border-radius: 5px;
        margin: 10px;
    }

    .center .tool .bd .li {
        display: none;
    }

    .center .tool {
        height: auto;
    }

        .center .tool .bd .codes {
            width: 100%;
        }
    /*表格*/
    .content .right {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0;
        min-width: 0;
    }

        .content .right .stores {
            height: auto;
            width: -webkit-fill-available;
            margin: 0 10px;
        }

        .content .right .contact {
            height: auto;
            width: -webkit-fill-available;
            margin: 10px;
        }
}


/* 用户管理列表 */
.pageUserList {
    height: calc(100vh - 34px);
    overflow-y: auto;
    background-color: #fff;
}

    .pageUserList .item-list .item-1 {
        width: 40px;
    }

    .pageUserList .item-list .item-2 {
        width: 50px;
    }

    .pageUserList .item-list .item-3 {
        width: 150px;
    }

    .pageUserList .item-list .item-4 {
        width: 120px;
    }

    .pageUserList .item-list .item-5 {
        width: 120px;
    }

    .pageUserList .item-list .item-6 {
        width: 60px;
    }

    .pageUserList .item-list .item-7 {
        width: 100px;
    }

    .pageUserList .item-list .item-8 {
        width: 100px;
    }

    .pageUserList .item-list .item-9 {
        width: 120px;
    }

    .pageUserList .item-list .item-10 {
        width: 180px;
    }

    .pageUserList .item-list .item-11 {
        width: 80px;
    }

    .pageUserList .item-list .item-12 {
        width: 140px;
    }

/* 添加用户/用户详细弹窗 */
.addUserContainer li .label {
    color: #999;
    width: 80px;
    text-align: right;
    font-size: 13px;
}

.addUserContainer .role {
    flex: 1;
    border: 1px solid #ddd;
    padding: 10px 10px 0 10px;
}

.role .system {
    margin-bottom: 10px;
}

.role .system, .role .custom {
    display: flex;
    flex-direction: row;
}

.role .label1 {
    width: 90px;
    font-size: 13px;
    color: #333;
}

.role .list {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    font-size: 12px !important;
}

    .role .list .item {
        width: 100px;
    }

.addUserContainer .errorBase {
    padding-left: 80px;
}

/*  */
.dropdownOrgan {
    flex: 1;
}

    .dropdownOrgan .downBtn {
        width: 100%;
        background: #fff;
        border: 1px solid #ddd;
        height: 32px;
        border-radius: 4px;
        text-align: right;
        padding-right: 10px;
    }

.dropdownOrganBox {
    width: 422px;
    box-shadow: 0 3px 6px -4px rgb(0 0 0 / 12%), 0 6px 16px 0 rgb(0 0 0 / 8%), 0 9px 28px 8px rgb(0 0 0 / 5%);
}

/* 组织机构列表 */
.addOrganContainer li .label {
    color: #999;
    width: 80px;
    text-align: right;
    font-size: 13px;
}

.pageOrganList {
    height: calc(100vh - 34px);
    overflow-y: auto;
    background-color: #fff;
}

    .pageOrganList .item {
        font-size: 12px !important;
    }

    .pageOrganList .item-list .item-1 {
        width: 40px;
    }

    .pageOrganList .item-list .item-2 {
        width: 50px;
    }

    .pageOrganList .item-list .item-3 {
        width: 150px;
    }

    .pageOrganList .item-list .item-4 {
        width: 160px;
    }

    .pageOrganList .item-list .item-5 {
        width: 160px;
    }

    .pageOrganList .item-list .item-6 {
        width: 150px;
    }

/* 角色列表 */
.pageRoleList {
    height: calc(100vh - 34px);
    overflow-y: auto;
    background-color: #fff;
}

    .pageRoleList .item {
        font-size: 12px !important;
    }

    .pageRoleList .item-list .item-1 {
        width: 40px;
    }

    .pageRoleList .item-list .item-2 {
        width: 50px;
    }

    .pageRoleList .item-list .item-3 {
        width: 150px;
    }

    .pageRoleList .item-list .item-4 {
        width: 160px;
    }

    .pageRoleList .item-list .item-5 {
        width: 150px;
    }

    .pageRoleList .item-list .item-6 {
        width: 150px;
    }

    .pageRoleList .item-list .item-7 {
        width: 150px;
    }

.ant-checkbox-group {
    display: block !important;
}

/* 新增/编辑角色 */
.addRoleContainer {
    height: calc(100vh - 34px);
    overflow-y: auto;
    background-color: #fff;
}

    .addRoleContainer .form {
        padding: 20px;
    }

        .addRoleContainer .form li .label {
            color: #999;
            width: 80px;
            text-align: right;
            font-size: 13px;
        }

        .addRoleContainer .form .inputBase {
            width: 200px !important;
            flex: none;
        }

        .addRoleContainer .form textarea.inputBase {
            width: 750px !important;
            flex: none;
            height: 60px;
        }

.functionalPermissions {
    width: 750px;
    border: 1px solid #ddd;
}

    .functionalPermissions .module {
        display: flex;
        flex-direction: row;
        align-items: center;
        border-bottom: 1px solid #ddd;
    }

        .functionalPermissions .module:last-child {
            border-bottom: none;
        }

    .functionalPermissions .moduleName {
        padding-left: 10px;
        width: 100px;
    }

    .functionalPermissions .childMenus {
        padding: 5px 10px;
        border-left: 1px solid #ddd;
        flex: 1;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        line-height: 30px;
    }

        .functionalPermissions .childMenus .menu {
            width: 25%;
        }

.dataPermissions .addOrganBtn {
    padding: 0 10px;
    margin: 5px;
    height: 30px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #1877F2;
    cursor: pointer;
    white-space: nowrap;
    position: relative;
    color: #fff;
}

.dataPermissions {
    border: 1px solid #ddd;
}

    .dataPermissions .thead {
        height: 30px;
        line-height: 30px;
        background-color: #f2f2f2;
    }

    .dataPermissions .item {
        padding: 0 5px;
    }

    .dataPermissions .item-1, .dataPermissions .formName {
        width: 150px;
    }

    .dataPermissions .item-2 {
        width: 200px;
    }

    .dataPermissions .item-3 {
        margin-left: 10px;
        width: 280px;
    }

    .dataPermissions .item-4 {
        width: 120px;
    }

    .dataPermissions .item-5 {
        width: 150px;
    }

    .dataPermissions .formName {
        padding: 0 5px;
    }

    .dataPermissions .formDatas {
        border-left: 1px solid #ddd;
    }

    .dataPermissions .tr {
        display: flex;
        flex-direction: row;
        align-items: center;
        border-bottom: 1px solid #ddd;
    }

        .dataPermissions .tr:last-child {
            border-bottom: none;
        }

        .dataPermissions .tr .row {
            padding: 8px 0;
            border-bottom: 1px solid #ddd;
        }

            .dataPermissions .tr .row:last-child {
                border-bottom: none;
            }

        .dataPermissions .tr .opt {
            display: flex;
            flex-direction: row;
            color: #1877F2;
        }

            .dataPermissions .tr .opt .it {
                margin-right: 10px;
                cursor: pointer;
            }

                .dataPermissions .tr .opt .it:last-child {
                    margin-right: 0;
                }

    .dataPermissions .checkBoxs {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        line-height: 30px;
    }

        .dataPermissions .checkBoxs .check {
            margin-right: 15px;
        }

            .dataPermissions .checkBoxs .check:last-child {
                margin-right: 0;
            }


/* 权限列表 */
.pagePermissionList {
    height: calc(100vh - 34px);
    overflow-y: auto;
    background-color: #fff;
}

    .pagePermissionList .item {
        font-size: 12px !important;
    }

    .pagePermissionList .item-list .item-1 {
        width: 40px;
    }

    .pagePermissionList .item-list .item-2 {
        width: 50px;
    }

    .pagePermissionList .item-list .item-3 {
        width: 150px;
    }

    .pagePermissionList .item-list .item-4 {
        width: 160px;
    }

    .pagePermissionList .item-list .item-5 {
        width: 150px;
    }

    .pagePermissionList .item-list .item-6 {
        width: 150px;
    }

    .pagePermissionList .item-list .item-7 {
        width: 180px;
    }

    .pagePermissionList .item-list .item-8 {
        width: 240px;
    }

/* 新增/编辑权限 */
.addPermissionContainer {
    height: calc(100vh - 34px);
    overflow-y: auto;
    background-color: #fff;
}

    .addPermissionContainer .form {
        padding: 20px;
    }

        .addPermissionContainer .form li .label {
            color: #999;
            width: 80px;
            text-align: right;
            font-size: 13px;
        }

        .addPermissionContainer .form .inputBase {
            width: 200px !important;
            flex: none;
        }

        .addPermissionContainer .form textarea.inputBase {
            width: 700px !important;
            flex: none;
            height: 60px;
        }

.functionalPermissions2 {
    width: 700px;
    border: 1px solid #ddd;
}

    .functionalPermissions2 .module {
        padding: 10px;
        border-bottom: 1px solid #ddd;
    }

        .functionalPermissions2 .module:last-child {
            border-bottom: none;
        }

    .functionalPermissions2 .moduleName {
        font-weight: bold;
        cursor: pointer;
        line-height: 1.5;
    }

        .functionalPermissions2 .moduleName .erpfont {
            margin-left: 5px;
            font-size: 16px;
            color: #999;
        }


    .functionalPermissions2 .functionals .functional {
        margin: 10px 0;
    }

.my {
    display: none;
}
/*移动端返回*/
.mobile-crumb {
    display: none;
    background-color: #f5f5f5;
    height: 40px;
    line-height: 40px;
    border: 1px solid #ddd;
    font-size: 16px;
    color: #333;
    font-weight: 400;
    padding-left: 12px;
}

    .mobile-crumb i {
        font-size: 18px;
        font-weight: 400;
        margin-right: 10px;
        height: 18px;
        width: 18px;
    }
/*移动端搜索*/
.sreachAll {
    display: none;
}






/*移动端底部*/
.bottomMenu {
    display: none;
}

.bottomNav {
    box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.3);
    width: 100vw;
    height: 35px;
    background-color: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0 0 5px;
    z-index: 1000;
}

    .bottomNav .total {
        font-size: 13px;
        color: #666666;
        align-items: center;
        padding: 0;
        height: 20px;
        flex: 1;
    }

    .bottomNav .btn {
        display: flex;
        height: 35px;
        font-size: 14px;
        text-align: center;
        color: #fff;
        justify-content: flex-end;
        width: 75%;
    }

        .bottomNav .btn .more {
            background-color: #e6f5ff;
            line-height: 35px;
            width: 33%;
            height: 35px;
            color: #666666;
        }

        .bottomNav .btn .cancel {
            background-color: #bde2ff;
            line-height: 35px;
            width: 33%;
            height: 35px;
            color: #1877f2;
        }

        .bottomNav .btn .save {
            background-color: #1877f2;
            line-height: 35px;
            width: 33%;
            height: 35px;
        }



.userTable {
    display: none;
}

.showBtnone {
    display: none !important;
}
/*用户移动端*/
@media screen and (max-width: 640px) {
    .pageLogin {
        overflow: hidden;
    }

    .page-operation {
        /*display: none;*/
    }

    .baseSearchForm {
        display: none;
    }

    .pageAccount {
        display: none;
    }

    .bottomMenu {
        display: block;
    }

    .page-table {
        display: none;
    }

    .mobile-crumb {
        display: flex;
        justify-content: space-between;
    }

    .sreachAll {
        display: block;
    }

    .showBtnone {
        display: inline-flex !important;
        background-color: darkgray !important;
    }
    /*角色管理*/
    .userTable {
        height: calc(100vh - 116px);
        background-color: #f5f5f5;
        overflow-y: auto;
        display: block;
        padding: 0px 10px 10px 10px;
    }

    .pageUserList {
        height: 100vh;
    }

    .userTable .ant-checkbox-inner {
        border-radius: 50% !important;
    }

    .userTable .allCheck {
        background-color: #fff;
        height: 22px;
        border-radius: 5px;
        padding-left: 5px;
    }

    .role_item {
    }

        .role_item .table-role {
            display: flex;
            justify-content: space-between;
            background-color: #fff;
            margin: 10px 0;
            padding: 5px;
            border-radius: 5px;
        }

    .pageUserList .role_item .table-role {
        margin: 0;
        margin-bottom: 10px;
    }

    .table-role .itemone {
        width: 20px;
        margin-top: 3px;
    }

    .table-role .table-list {
        flex: 1;
    }

        .table-role .table-list .item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 14px !important;
            justify-content: space-between;
            line-height: 2;
            border-bottom: 1px solid #ddd;
        }

            .table-role .table-list .item label {
                color: #999;
            }

            .table-role .table-list .item span {
                color: #333;
            }

        .table-role .table-list .opt {
            text-align: end;
            margin: 8px 0;
        }

            .table-role .table-list .opt .btn {
                padding: 0 10px;
                height: 24px;
                border-radius: 4px;
                color: #fff;
                cursor: pointer;
                margin-right: 5px;
            }

                .table-role .table-list .opt .btn.btnDetail {
                    background-color: #1877F2;
                }

                .table-role .table-list .opt .btn.btnDel {
                    background-color: #F56C6C
                }

    .functionalPermissions2 {
        width: 200px;
    }
    /*角色管理添加页*/
    .addRoleContainer .form {
        padding: 10px 0;
    }

    .functionalPermissions {
        width: auto;
    }

        .functionalPermissions .childMenus {
            display: block;
        }

    .dataRange .label {
        display: block;
        margin-bottom: 10px;
    }

    .dataPermissions .item-1 {
        width: 25%
    }

    .dataPermissions .item-1, .dataPermissions .formName {
        width: 110px;
        padding: 0;
        text-align: center;
    }

    .dataPermissions .item-2 {
        width: 50%
    }

    .dataPermissions .item-3 {
        width: 40%
    }

    .pageUserList .baseSearchForm .list {
        display: block;
    }

        .pageUserList .baseSearchForm .list li {
            display: inline-block;
            width: 46%;
            margin-bottom: 10px;
        }

            .pageUserList .baseSearchForm .list li input {
                width: 100%;
            }

    .baseSearchForm .list li:last-child {
        width: 100%;
        text-align: end;
        padding-right: 20px;
    }
    /*个人中心*/
    .my {
        display: block;
        width: 100vw;
        height: calc(100vh - 84px);
        overflow: auto;
        background-color: #f5f5f5;
        font-size: 0.8rem;
    }

        .my .portrait {
            text-align: center;
            margin-bottom: 10px;
        }

            .my .portrait img {
                height: 50px;
                width: 50px;
                background-color: #ccc;
                border-radius: 50%;
                min-width: 50px;
            }

        .my .top {
            display: flex;
            background-color: #fff;
            padding: 15px;
            margin: 10px;
            border-radius: 5px;
        }

        .my .erpfont {
            font-size: 25px;
            line-height: 40px;
            margin-right: 5px;
        }

        .my .top_right {
            margin-left: 20px;
        }

            .my .top_right .user_name {
                font-size: 1.1rem;
                color: #333;
                line-height: 1.5;
            }

            .my .top_right .right_item {
                line-height: 1.5;
            }

                .my .top_right .right_item label {
                    color: #999;
                }

                .my .top_right .right_item span {
                    color: #333;
                }

        .my .middle {
            background-color: #fff;
            margin: 0 10px 10px;
            border-radius: 5px;
        }

        .my .title {
            display: flex;
            align-items: center;
            margin-left: 15px;
            border-bottom: 1px solid #ddd;
        }

        .my .personalContent {
            margin-left: 15px;
        }

            .my .personalContent li {
                width: 100%;
                border-bottom: 1px solid #ddd;
                padding-bottom: 10px;
                padding-right: 10px;
                padding: 10px 10px 10px 0;
                margin: 0;
            }

                .my .personalContent li:last-child {
                    border: 0;
                }

    .addCompany {
        background-color: #fff;
        margin: 10px;
        border-radius: 5px;
    }

        .addCompany .addCompany_title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-right: 10px;
            border-bottom: 1px solid #ddd;
            margin-left: 15px;
        }

            .addCompany .addCompany_title .title {
                border: 0;
                margin-left: 0
            }

    .organContainer {
        padding-right: 10px;
    }

        .organContainer .organ {
            margin: 0;
            margin-bottom: 20px;
            border-radius: 4px;
            width: 100%;
        }
}

.my-reconnect-modal > div {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
    overflow: hidden;
    background-color: rgb(140,140,140);
    opacity: 0.8;
    text-align: center;
    font-weight: bold;
    padding-top: 45vh;
    color: #333;
}

.my-reconnect-modal .submit {
    width: 90px;
    height: 30px;
    color: #fff;
    background: #3771e4;
    margin-top: 10px;
}

.components-reconnect-hide div {
    display: none;
}

.components-reconnect-show > div {
    display: none;
}

.components-reconnect-show > .show {
    display: block !important;
}

.components-reconnect-failed > div {
    display: none;
}

.components-reconnect-failed > .failed {
    display: block !important;
}

.components-reconnect-rejected > div {
    display: none !important;
}

.components-reconnect-rejected > .rejected {
    display: block;
}

.dataTable {
    background-color: #fff;
}

    .dataTable .ant-image-img {
        width: 60px;
        height: 60px;
        margin-right: 10px;
    }

    .dataTable .goods {
        display: flex;
    }

    .dataTable .ant-table-tbody > tr > td {
        padding: 10px !important;
    }

    .dataTable .goods .goods_content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 60px;
    }

        .dataTable .goods .goods_content .content_title {
            width: 240px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 17px;
        }

.btnList {
    padding: 0 10px 10px 10px;
    border-bottom: 1px solid #ddd;
}

.pageSearchContainer {
    padding: 10px;
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
}

    .pageSearchContainer .searchList {
        display: flex;
        flex-direction: row;
        align-items: center;
        flex-wrap: wrap;
    }

        .pageSearchContainer .searchList li {
            margin-right: 10px;
            display: flex;
            flex-direction: row;
            align-items: center;
        }

            .pageSearchContainer .searchList li Button {
                margin-right: 10px;
            }

            .pageSearchContainer .searchList li label {
                white-space: nowrap;
            }


.loadingContainer {
    padding: 50px
}

.modal .ant-table {
    height: 650px;
    overflow: scroll;
}

/* 店铺管理 */


    .addStoreContainer .addOnlineStore .notice {
        padding: 10px 0;
        font-size: 13px;
        color: #999;
    }

    .addStoreContainer .addOnlineStore .list {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
    }

        .addStoreContainer .addOnlineStore .list li {
            margin-right: 15px;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

            .addStoreContainer .addOnlineStore .list li img {
                width: 100%;
                border-radius: 4px;
            }
.addofflineStoreContainer .label {
font-size:12px;
}

.pagehelpDetail {
    padding:20px;
    background:#fff;
overflow:auto;
}
.pagehelpDetail .t {
    font-size:14px;
    font-weight:bold;

}
    .pagehelpDetail p {
    
        margin:15px;
    }

    .pagehelpDetail img {
   
        max-width:1000px;
        height:auto;
        }

.pagehelpList {
    padding: 20px;
    background: #fff;
    overflow: auto;
}
    .pagehelpList .list li{
        line-height:1.6;
        color:#666;
        font-size:13px;
    }
        .pagehelpList .list li a {
            line-height: 1.6;
            color: #2572fd;
            font-size: 13px;
        }

/*帮助中心*/
.helpHeaderWarp {
    box-shadow: 0 1px 3px 1px #ddd;
    border-bottom: 1px solid #fff;
}
.helpHeader {
    margin: 0 auto;
    width: 1200px;
    height:60px;
    display:flex;
    flex-direction:row;
    align-items:center;
}
    .helpHeader .logo2 {
        display:flex;
        align-items:center;
        height:100%;
        margin-right:40px;
    }
        .helpHeader .logo2 .icon-chunlogo {
            color: #1877F2;
            font-size:38px;
        }
        .helpHeader .logo2 .name {
            font-size: 24px;
            color: #1877F2;
            font-weight: bold;
            padding-left: 6px;
        }
    .helpHeader .nav {
        display:flex;
        flex-direction:row;
        align-items:center;
    }
        .helpHeader .nav li {
            margin-right:30px;
        }
        .helpHeader .nav li a {
            color: #666;
            font-size: 14px;
        }
           
            .helpHeader .nav li a:hover {
                color: #1877F2;
            }
.helpBodyWarp {
    height: calc(100vh - 60px);
    overflow: auto;
}
.helpBody {
    
   
    width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: row;
}
    .helpBody .left {
        padding:20px 15px 0 0;
    width:200px;
    border-right:1px solid #ddd;
    }
        .helpBody .left li {
            line-height: 30px;
            color: #666;
            font-size: 13px;
        }
            .helpBody .left li a {
                line-height: 1.6;
                color: #666;
                font-size: 13px;
            }
                .helpBody .left li a.active {
                    color: #1877F2;
                }
.helpBody .left li a:hover {
    color: #1877F2;
}
        .helpBody .helpDetail {
            flex: 1;
        }

.helpDetail .t {
    font-size: 14px;
    font-weight: bold;
}

.helpDetail p {
    margin: 15px;
}

.helpDetail img {
    max-width: 800px;
    height: auto;
}

/*供销代发*/
.pageDropshippingBuyerlist{
    overflow: auto;
    display: flex;
}
.orderSearch {
    width: 240px;
    border-right: 1px solid #ddd;
    height: calc(100vh - 34px);
}

.orderSearch .keywords {
padding: 10px 10px;
}

.orderSearch .keywords .keywordType {
width: 70px;
margin-bottom: 8px;
}

.orderSearch .keywords .keywordType .el-input__inner {
padding: 0 !important;
border: none !important;
height: 20px !important;
line-height: 20px !important;
font-size: 12px !important;
}

.orderSearch .keywords .keywordType .el-input--small .el-input__icon {
line-height: 20px !important;
}

.orderSearch .orderSearchOperation {
display: flex;
flex-direction: row;
align-items: center;
justify-content: space-between;
background: #fff;
height: 34px;
border-bottom: 1px solid #ddd;
}

.orderSearch .orderSearchOperation .button {
margin-left: 8px;
height: 26px;
border-radius: 4px;
cursor: pointer;
}

.orderSearch .orderSearchOperation .button-search {
width: 80px;
background-color: #1877F2;
color: #fff;
font-size: 13px;
}

.orderSearch .orderSearchOperation .button-search .icon-search {
padding-right: 8px;
font-size: 14px;
}

.orderSearch .orderSearchOperation .button-clear {
width: 60px;
border: 1px solid #ddd;
background-color: #fff;
}

.orderSearch .orderSearchOperation .r {
display: flex;
flex-direction: row;
align-items: center;
}

.orderSearch .orderSearchOperation .operation {
display: flex;
align-items: center;
justify-content: center;
width: 30px;
height: 34px;
cursor: pointer;
}

.orderSearch .orderSearchOperation .operation .icon-refresh {
color: #1877F2;
font-size: 16px;
}

.orderSearch .orderSearchOperation .operation .icon-left {
color: #999;
font-size: 16px;
}

.orderSearch .orderSearchList {
padding: 5px;
height: calc(100vh - 34px);
overflow-y: auto;
}

.orderSearch .orderSearchList li {
margin-bottom: 5px;
}

.orderSearch .orderSearchList .input-keywork {
width: 100%;
}

.orderSearch .orderSearchList .condition-item {
border: 1px solid #ddd;
background-color: #fff;
}

.orderSearch .orderSearchList .condition-item .label {
margin-bottom: 6px;
display: flex;
flex-direction: row;
align-items: center;
justify-content: space-between;
padding: 0 10px;
height: 30px;
font-weight: 400;
font-size: 12px;
color: #333;
cursor: pointer;
}

.orderSearch .orderSearchList .condition-item .label .icon {
font-size: 14px;
color: #999;
transition: all .3s;
}

.orderSearch .orderSearchList .condition-item .label.close .icon {
transform: rotate(180deg);
}

.orderSearch .orderSearchList .condition-item .borderSelect {
display: flex;
flex-direction: row;
flex-wrap: wrap;
padding: 0 0 0 10px;
}

.orderSearch .orderSearchList .condition-item .borderSelect .item {
position: relative;
box-sizing: border-box;
padding: 0 6px;
height: 24px;
display: flex;
align-items: center;
justify-content: center;
border: 1px solid #ddd;
font-size: 12px;
color: #333;
border-radius: 4px;
min-width: 50px;
margin: 0 18px 10px 0;
cursor: pointer;
}

.orderSearch .orderSearchList .condition-item .borderSelect .item,
.orderSearch .orderSearchList .condition-item .sellerFlags .flag {
position: relative;
}

.orderSearch .orderSearchList .condition-item .borderSelect .item:hover,
.orderSearch .orderSearchList .condition-item .sellerFlags .flag:hover {
color: #1877F2;
border-color: #1877F2;
}

.orderSearch .orderSearchList .condition-item .borderSelect .item.active,
.orderSearch .orderSearchList .condition-item .sellerFlags .flag.active {
color: #1877F2;
border-color: #1877F2;
}

.orderSearch .orderSearchList .condition-item .borderSelect .item.active::before,
.orderSearch .orderSearchList .condition-item .sellerFlags .flag.active::before {
content: '\e6c6';
font-family: "iconfont" !important;
font-size: 14px;
font-style: normal;
-webkit-font-smoothing: antialiased;
-moz-osx-font-smoothing: grayscale;
position: absolute;
bottom: -1px;
right: -1px;
font-size: 12px;
color: #1877F2;
}

.orderSearch .orderSearchList .condition-item .cnd .item {
margin-right: 10px;
}

.orderSearch .orderSearchList .condition-item .sellerFlags {
display: flex;
flex-direction: row;
align-items: center;
padding: 0 0 0 10px;
}

.orderSearch .orderSearchList .condition-item .sellerFlags li.flag {
margin-right: 10px;
width: 24px;
height: 24px;
display: flex;
align-items: center;
justify-content: center;
border: 1px solid #ddd;
border-radius: 4px;
cursor: pointer;
}

.orderSearch .orderSearchList .condition-item .datePicker {
padding: 5px 5px 10px 10px;
}

.orderSearch .orderSearchList .search-select {
position: relative;
padding-right: 30px;
padding-left: 10px;
width: 100%;
height: 40px;
border: 1px solid #ddd;
border-radius: 4px;
background-color: #fff;
cursor: pointer;
}

.orderSearch .orderSearchList .search-select::before {
content: '\e60c';
font-family: "iconfont" !important;
font-size: 14px;
font-style: normal;
-webkit-font-smoothing: antialiased;
-moz-osx-font-smoothing: grayscale;
position: absolute;
top: 12px;
right: 10px;
font-size: 14px;
color: #999;
}

.orderSearch .orderSearchList .search-select .input {
height: 100%;
width: 100%;
font-size: 12px;
color: #333;
white-space: normal;
text-overflow: ellipsis;
overflow: hidden;
}

.orderSearch .orderSearchList .fastDate {
padding-left: 10px;
display: flex;
flex-direction: row;
}

.orderSearch .orderSearchList .fastDate li {
margin-right: 10px;
cursor: pointer;
font-size: 12px;
background-color: #e8f0f9;
padding: 1px 3px;
color: #1877f2;
border-radius: 2px;
}

.orderSearch .orderSearchList .num {
position: absolute;
top: -12px;
left: 50px;
display: flex;
align-items: center;
justify-content: center;
height: 18px;
border-radius: 9px;
color: #fff;
background-color: red;
min-width: 14px;
padding: 0 2px;
}

/*操作*/
.orderContent{
    position: relative;
    flex: 1;
    height: 100%;
}
.baseSelect {
    background-color: rgba(255, 255, 255, 0);
  }
  
  .top-nav {
    height: 34px;
    border-bottom: 1px solid #ddd;
    background-color: #fff;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  
  .top-nav .l {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  
  .top-nav .r {
    padding-right: 15px;
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  
  .top-nav .open {
    height: 100%;
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  
  .top-nav .open .icon-right {
    font-size: 14px;
    color: #999;
  }
  
  .top-nav .crumbs {
    padding-left: 5px;
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 100%;
  }
  
  .top-nav .crumbs .home {
    margin-right: 10px;
  }
  
  .top-nav .crumbs .home .icon-home {
    font-size: 14px;
    color: #1877F2;
  }
  
  .top-nav .crumbs .link {
    color: #999;
  }
  
  .top-nav .crumbs .current {
    color: #333;
  }
  
  .top-nav .crumbs .arrow {
    padding: 0 3px;
  }
  
  .top-nav .operation {
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  
  .top-nav .operation li {
    position: relative;
    margin: 0 1px;
    padding: 0 8px;
    font-size: 12px;
    color: #2572fd;
    cursor: pointer;
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
  
  .top-nav .operation li::before {
    content: "";
    display: block;
    width: 1px;
    height: 12px;
    background-color: #ddd;
    position: absolute;
    right: 0;
    top: 11px;
  }
  
  .top-nav .operation li:last-child::before {
    width: 0;
  }
  
  .top-nav .operation li:hover {
    background-color: #1877F2;
    color: #fff;
  }
  
  .top-nav .operation li:hover::before {
    width: 0;
  }
  
  .top-nav .operation li:hover .icon {
    color: #fff;
  }
  
  .top-nav .operation li.down:hover .dropDown {
    display: block;
  }
  
  .top-nav .operation .icon {
    padding-right: 4px;
    font-size: 12px;
    color: #2572fd;
  }
  
  .top-nav .operation .icon-yichang {
    color: red;
  }
  
  .top-nav .operation .icon-zhengchang {
    color: #1bbe6b;
  }
  
  .top-nav .operation .dropDown {
    transition: all 0.3s;
    display: none;
    padding-top: 1px;
    position: absolute;
    top: 33px;
    left: 0;
    z-index: 999;
    width: 100%;
    background-color: #fff;
    border: 1px solid #1877F2;
    color: #666;
  }
  
  .top-nav .operation .dropDown p {
    line-height: 33px;
    text-align: center;
    font-size: 12px;
    border-bottom: 1px solid #eee;
  }
  
  .top-nav .operation .dropDown p:last-child {
    border-bottom: none;
  }
  
  .top-nav .operation .dropDown p:hover {
    background-color: #1877F2;
    color: #fff;
  }
  
  .order-list .icon-sort {
    font-size: 12px;
    color: #ddd;
  }
  
  .order-list .icon-arrow-down,
  .order-list .icon-arrow-up {
    font-size: 12px;
    color: #1877F2;
  }
  
  .sortPopover {
    font-size: 12px;
    line-height: 24px;
  }
  
  .sortPopover p {
    cursor: pointer;
  }
  
  .footChecked {
    padding-left: 5px;
    font-size: 13px;
  }
  
  .order-list {
    width: 100%;
    height: calc(100vh - 34px - 34px - 32px);
    overflow: auto;
  }
  
  .order-list .item-list {
    align-items: stretch;
  }
  
  .order-list .item-list .item {
    padding: 0 10px;
    border-right: 1px solid #ddd;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  
  .order-list .item-list .item-center {
    justify-content: center;
    padding: 0 !important;
  }
  
  .order-list .item-list .item-checkbox {
    width: 26px;
  }
  
  .order-list .item-list .item-sort {
    width: 34px;
  }
  
  .order-list .item-list .item-recipient {
    width: 176px;
    align-items: center;
  }
  
  .order-list .item-list .item-order {
    padding: 0 !important;
  }
  
  .order-list .item-list .item-dateTime {
    width: 180px;
  }
  
  .order-list .item-list .item-product {
    width: 260px;
  }
  
  .order-list .item-list .item-number {
    width: 60px;
  }
  
  .order-list .item-list .item-amount {
    width: 60px;
  }
  
  .order-list .item-list .item-status {
    width: 65px;
    flex-direction: column;
  }
  
  .order-list .item-list .item-message {
    width: 254px;
  }
  
  .order-list .item-list .item-waybill {
    flex: 1;
  }
  
  .order-list .table {
    width: fit-content;
  }
  
  .order-list .thead {
    position: sticky;
    top: 0;
    background: #f5f5f5;
    z-index: 99;
    height: 30px;
    border-bottom: 1px solid #ddd;
  }
  
  .order-list .thead .item {
    height: 30px;
    line-height: 14px;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  
  .order-list .thead .icon-sort {
    font-size: 12px;
    color: #ddd;
  }
  
  .order-list .thead .icon-arrow-down,
  .order-list .thead .icon-arrow-up {
    font-size: 12px;
    color: #1877F2;
  }
  
  .order-list .tbody {
    width: fit-content;
  }
  
  .order-list .tbody .table-tr {
    width: fit-content;
    font-size: 12px;
    color: #666;
    border-bottom: 1px solid #ddd;
  }
  
  .order-list .tbody .table-tr:nth-child(2n+1) {
    background-color: #fff;
  }
  
  .order-list .tbody .table-tr .item {
    padding-top: 10px;
    padding-bottom: 10px;
  }
  
  .order-list .tbody .table-tr .orderGroup {
    border-bottom: 1px solid #eee;
  }
  
  .order-list .tbody .table-tr .orderGroup:last-child {
    border-bottom: none;
  }
  
  .order-list .tbody .tagPlatform {
    margin-right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    padding: 0 4px;
    height: 20px;
    background: #ED3F14;
    border-radius: 4px;
  }
  
  .order-list .tbody .recipient {
    width: 100%;
    line-height: 20px;
  }
  
  .order-list .tbody .recipient .store {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  
  .order-list .tbody .recipient .store .iconfont {
    font-size: 18px;
    margin-right: 4px;
  }
  
  .order-list .tbody .recipient .nickname {
    display: flex;
    flex-direction: row;
    align-items: center;
    color: #2572FD;
    cursor: pointer;
  }
  
  .order-list .tbody .recipient .nickname .icon-wangwang {
    font-size: 14px;
  }
  
  .order-list .tbody .recipient .bottom {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
  
  .order-list .tbody .recipient .tags {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  
  .order-list .tbody .recipient .tags .tag {
    margin-right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    width: 20px;
    height: 20px;
    background: #1877F2;
    border-radius: 4px;
  }
  
  .order-list .tbody .recipient .tags .tag:last-child {
    margin-right: 0;
  }
  
  .order-list .tbody .recipient .tags .tag.detail {
    background: #1877F2;
  }
  
  .order-list .tbody .recipient .tags .tag.tui {
    background: #F5222D;
  }
  
  .order-list .tbody .recipient .tags .tag.merge {
    background: #1BBE6B;
  }
  
  .order-list .tbody .recipient .tags .tag.split {
    background: #FE9900;
  }
  
  .order-list .tbody .recipient .tags .tag.special {
    background: #ff2af3;
  }
  
  .order-list .tbody .recipient .tags .tag.modifyAddress {
    border: 1px solid #b8d7ff;
    color: #1877f2;
    background: #f0f7ff;
    box-sizing: border-box;
  }
  
  .order-list .tbody .recipient .tags .tag.modifySku {
    border: 1px solid #93e6b3;
    color: #1bbe6b;
    background: #f0fff5;
    width: 30px;
    box-sizing: border-box;
  }
  
  .order-list .tbody .recipient .tags .tag.cui {
    border: 1px solid #ff8f93;
    color: #f34f5f;
    background: #fff1f0;
    box-sizing: border-box;
  }
  
  .order-list .tbody .recipient .tags .tag.isReachable {
    background: #ff2af3;
    width: 70px;
  }
  
  .order-list .tbody .recipient .tags .tag.orderType {
    background: #d30fc9;
  }
  
  .order-list .tbody .recipient .opt .icon-operation {
    color: #1877F2;
    cursor: pointer;
  }
  
  .order-list .tbody .dateTime {
    line-height: 20px;
  }
  
  .order-list .tbody .products {
    width: 100%;
  }
  
  .order-list .tbody .products .productItem {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
  }
  
  .order-list .tbody .products .checkbox {
    width: 18px;
  }
  
  .order-list .tbody .products .pic {
    margin: 0 10px;
    width: 50px;
    height: 50px;
  }
  
  .order-list .tbody .products .pic img {
    width: 100%;
    height: 100%;
    border-radius: 4px;
  }
  
  .order-list .tbody .products .info {
    min-width: 102px;
    line-height: 20px;
    flex: 1;
  }
  
  .order-list .tbody .products .info .tit {
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  
  .order-list .tbody .number {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .order-list .tbody .number .tag.quehuo {
    margin-top: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 20px;
    border-radius: 4px;
    background-color: #FFC069;
    color: #fff;
  }
  
  .order-list .tbody .message p {
    margin: 10px 0;
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  
  .order-list .tbody .message .icon {
    padding-right: 4px;
    font-size: 13px;
  }
  
  .order-list .tbody .item-order,
  .order-list .tbody .item-mainOrder {
    display: flex;
    flex-direction: column;
  }
  
  .order-list .tbody .item-order .item-list,
  .order-list .tbody .item-mainOrder .item-list {
    flex: 1;
  }
  
  .order-list .tbody .item-order .item-list:last-child,
  .order-list .tbody .item-mainOrder .item-list:last-child {
    border-bottom: none;
  }
  
  .order-list .tbody .item-mainOrder>.item-list {
    flex: 1;
    border-bottom: 1px solid #ddd;
  }
  
  .order-list .tbody .waybillList .it {
    margin: 6px 0;
    display: flex;
    flex-direction: column;
    line-height: 1.4;
  }
  
  .order-list .tbody .waybillList .templateName {
    position: relative;
    display: flex;
    width: 160px;
    height: 24px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
    padding-right: 30px;
    box-sizing: border-box;
  }
  
  .order-list .tbody .waybillList .templateName .input {
    width: 100%;
    padding: 4px;
    color: #666;
    font-size: 12px !important;
  }
  
  .order-list .tbody .waybillList .templateName .icon-more {
    position: absolute;
    top: 0;
    right: 0;
    width: 30px;
    height: 24px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #999;
    cursor: pointer;
  }
  
  .order-list .tbody .waybillList .waybillCode {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-right: 5px;
  }
  
  .order-list .tbody .waybillList .waybillCode .code {
    width: 108px;
  }
  
  .order-list .tbody .waybillList .tag.isReachable {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    color: #fff;
    border-radius: 4px;
    background: #ff2af3;
    width: 70px;
  }
  
  .order-list .tbody .productList {
    border-bottom: 1px solid #f2f2f2;
  }

.mallPageWarp {
    height: 100vh;
    overflow:auto;
    display: flex;
    flex-direction: column;
}
.mallPageContent{
    flex: 1;
}

/* 供货管理 */
.SupplyPage{
    overflow: auto;
    padding: 10px 0 0 10px;
}
.SupplyTop{
    display: flex;
    flex-direction: row;
    background-color: #fff;
    padding: 15px;
    margin-bottom: 10px;
}

.SupplyTop .dropshippingStore .t{
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 10px;
}
.SupplyTop .dropshippingStore .table{
    margin-top: 50px;
    width: 420px;
    border: 1px solid #eee;   
}
.SupplyTop .dropshippingStore .table .thead{
    height: 30px;
    line-height: 30px;
    background-color: #f5f5f5;
    font-weight: bold;
}
.SupplyTop .dropshippingStore .table .opt{
    display: flex;
    flex-direction: row;
}
.SupplyTop .dropshippingStore .table .opt .btn{
    margin-right: 10px;
    cursor: pointer;
    color: #1877F2;
}
.SupplyTop .dropshippingStore .table .opt .btn:last-child{
    margin-right: 0;
}
.SupplyTop .dropshippingStore .table .item{
    padding-left: 5px;
}
.SupplyTop .dropshippingStore .table .tr{
    padding: 15px 0;
    border-bottom: 1px dashed #eee;
}
.SupplyTop .dropshippingStore .table .tr:last-child {
    border-bottom: none;
}
.SupplyTop .dropshippingStore .item-list .item-1{
    width: 300px;
}
.SupplyTop .dropshippingStore .item-list .item-2{
    width: 100px;
}
.SupplyTop .dropshippingStore .item-list .item-3{
    width: 100px;
}


.SupplyTop .buyerList{
    margin-right: 20px;
}
.SupplyTop .buyerList .t{
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 10px;
}
.SupplyTop .buyerList .addBtn{
    padding: 0 10px;
    margin-bottom:10px ;
    height: 30px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #1877F2;
    cursor: pointer;
    white-space: nowrap;
    position: relative;
   color: #fff;
}

.SupplyTop .buyerList .table{
    width: 420px;
    border: 1px solid #eee;   
}
.SupplyTop .buyerList .table .thead{
    height: 30px;
    line-height: 30px;
    background-color: #f5f5f5;
    font-weight: bold;
}
.SupplyTop .buyerList .table .opt{
    display: flex;
    flex-direction: row;
}
.SupplyTop .buyerList .table .opt .btn{
    margin-right: 10px;
    cursor: pointer;
    color: #1877F2;
}
.SupplyTop .buyerList .table .opt .btn:last-child{
    margin-right: 0;
}
.SupplyTop .buyerList .table .item{
    padding-left: 5px;
}
.SupplyTop .buyerList .table .tr{
    padding: 15px 0;
    border-bottom: 1px dashed #eee;
}
.SupplyTop .buyerList .table .tr:last-child {
    border-bottom: none;
}
.SupplyTop .buyerList .item-list .item-1{
    width: 200px;
}
.SupplyTop .buyerList .item-list .item-2{
    width: 100px;
}
.SupplyTop .buyerList .item-list .item-3{
    width: 100px;
}

.SupplyGoods{
   padding: 15px;
    background-color: #fff;
}
.SupplyGoods .t{
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 15px;
}
.SupplyGoods .page-operation{
    border-bottom: none;
    padding: 0;
}
.SupplyGoods .page-operation li{
    margin: 0;
}
.SupplyGoods .product{
    display: flex;
    flex-direction: row;
    align-items: center;
}
.SupplyGoods .product .pic{
    width: 60px;
    height: 60px;
    border-radius: 4px;
}
.SupplyGoods .product .info{
    margin-left: 10px;
    line-height: 1.6;
}
.SupplyGoods .product .info .id{
    color: #999;
}
.SupplyGoods .table{
    margin: 15px 0 15px 0;
}
.SupplyGoods .table .thead{
    height: 30px;
    line-height: 30px;
    background-color: #f5f5f5;
    font-weight: bold;
}
.SupplyGoods .table .opt{
    display: flex;
    flex-direction: row;
}
.SupplyGoods .table .opt .btn{
    margin-right: 10px;
    cursor: pointer;
    color: #1877F2;
}
.SupplyGoods .table .opt .btn:last-child{
    margin-right: 0;
}
.SupplyGoods .table .item{
    padding-left: 5px;
}
.SupplyGoods .table .tr{
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}
.SupplyGoods .item-list .item-1{
    width: 500px;
}
.SupplyGoods .item-list .item-2{
    width: 100px;
}
.SupplyGoods .item-list .item-3{
    width: 100px;
}
.SupplyGoods .item-list .item-4{
    width: 100px;
}

.AddDropshipingStoreContainer .form li{
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
}
.AddDropshipingStoreContainer .form li .label{
    width: 100px;
    font-size: 13px;
    text-align: right;
}

.notdata{
    display: block;
    padding: 20px;
    color: #999;
}

/* 批发商城 */

.boxContent1200{
    width: 1200px;
    margin: 0 auto;
}


.bannerContainer {
    position: relative;
    /*width: 100%;*/
    width: 1200px;
    height: 420px;
    left: 50%;
    transform: translateX(-50%);
}
.bannerContent{
    position: absolute;
    width: 1200px;
    height: 0px;
    left: 50%;
    top: 0px;
    transform: translateX(-50%);
    overflow: visible;
    z-index: 99;
}
.banner-side{
    position: absolute;
    left: 0px;
    top: 0px;
    height: 420px;
}
.banner-side  .category-menu-wrapper{
    padding: 10px 0;
}
.banner-side .category-menu{
    width: 240px;
    height: 100%;
    background-color: rgba(0,0,0,.5);
}
.banner-side .category-menu-item{
    padding-left: 15px;
    width: 100%;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    box-sizing: border-box;
}
.banner-side .category-menu-item:hover{
    background-color: #fff;
}
.banner-side .category-menu-item:hover .category-menu-item-label{
    color: #193f70;
}
.banner-side .category-menu-item .category-menu-item-label{
    display: flex;
    margin-right: 10px;
    color: #fff;
    font-size: 14px;
}
.banner-side .category-list{
    z-index: 999;
    position: absolute;
    top: 0;
    left: 240px;
    height: 100%;
    width: 700px;
    background-color: #fff;
    box-sizing: border-box;
    padding: 20px;
}
.banner-side .category-list .category-list-item{
    margin-bottom: 25px;
}
.banner-side .category-list .category-list-item{
    margin-bottom: 0;
}
.banner-side .category-list .category-list-item .hd{
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 15px;
}
.banner-side .category-list .category-list-item .hd .link{
    font-size: 14px;
    color: #333;
}
.banner-side .category-list .category-list-item .bd{
   display: flex;
   flex-direction: row;
   flex-wrap: wrap;
}
.banner-side .category-list .category-list-item .bd .link{
    margin: 0 15px 15px 0;
    font-size: 12px;
    color: #666;
 }




.mallProductList{
    height: auto;
    margin: 0 auto;
}
.mallProductList .list{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}
.mallProductList .list li{
    margin-right: 20px;
    margin-bottom: 20px;
    width: calc(1120px / 5);
    background-color: #fff;
}
.mallProductList .list li .c{
    display: block;
}

.mallProductList .list li .picture{
    width: 100%;
    height: 224px;
}
.mallProductList .list li .picture img{
    width: 100%;
    height: 100%;
}
.mallProductList .list li .con{
    height: 64px;
    padding: 10px 12px;
}
.mallProductList .list li .tit{
    margin-bottom: 5px;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.mallProductList .list li .price{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}
.mallProductList .list li .addcart{
    cursor: pointer;
    color: #999;
}
.mallProductList .list li .money{
    color: #fb3434;
}
.mallProductList .list li .price-text{
    font-size: 18px;
    
}
.mallProductList .list li .price-unit{
    font-size: 12px; 
}
.mallProductList .list li .store{
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 36px;
    padding: 0 12px;
    border-top: 1px solid #eee;
    color: #999;
    font-size: 12px;
}
.mallProductList .list li .store a{
    display: block;
    width: 100%;
    height: 100%;
    color: #999;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.mallProductList .list li .store .icon-reportStoreSale{
    font-size: 14px;
    margin-right: 4px;
}


.ant-carousel .slick-slide {
    text-align: center;
    height: 420px;
    line-height: 420px;
    background: #364d79;
    overflow: hidden;
}

.ant-carousel .slick-slide h3 {
    color: #fff;
}

.ant-carousel .slick-slide img{
    width: 100%;
    height: 420px;
}


.paginationContainer2{
    background-color: #fff;
    padding: 5px;
}


@media screen and (min-width: 1000px) {
    .mallProductList .list li:nth-child(5n){
        margin-right: 0;
    }
}

@media screen and (max-width: 1200px) {
    .boxContent1200{
        width: 100%;
    }
    .mallProductList .list li{
        width: calc((100vw - 95px) / 5);
    }
    .mallProductList .list li .picture{
        height: calc((100vw - 95px) / 5);
    }
    .mallProductList{
        padding: 0 5px;
        width: 100%;
    }
    .bannerContainer{
        width: 100%;
    }
}
@media screen and (max-width: 1000px) {
    .mallProductList .list li{
        width: calc((100vw - 59px) / 4);
        margin-right: 15px;
        margin-bottom: 15px;
    }
    .mallProductList .list li .picture{
        height: calc((100vw - 59px) / 4);
    }
    .mallProductList .list li:nth-child(4n){
        margin-right: 0;
    }
}
@media screen and (max-width: 760px) {
    .mallPageWarp{
        padding-bottom: 48px;
        box-sizing: border-box;
    }
    .mallProductList .list li{
        width: calc((100vw - 24px) / 2);
        margin-right: 10px;
        margin-bottom: 10px;
    }
    .mallProductList .list li .picture{
        height: calc((100vw - 24px) / 2);
    }
    .mallProductList .list li:nth-child(2n){
        margin-right: 0;
    }
    

    .ant-carousel .slick-slide{
        height: 300px;
        line-height: 300px;
    }
    .ant-carousel .slick-slide img{
        width: 100%;
        height: 300px;
    }
    .mobileFooter li a{
        display: flex;
        width: 100%;
        height: 100%;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 13px;
    }
    .paginationContainer{
     padding: 5px;
    }
}



/* 零售商城首页 */
.mallBannerContainer{
    display: flex;
    flex-direction: row;
    height: 510px !important;
}
.mallBannerContainer .banner-side{
    position: relative;
    top: 0;
    left: 0;
    height: 510px;
}

.mallBannerContainer .banner-side .category-menu{
    background-color: #fff;
    width: 200px;
}
.mallBannerContainer .banner-side .category-menu-item .category-menu-item-label{
    color: #666;
}
.mallBannerContainer .category-list{
    border-left: 1px solid #eee;
    width: 500px;
    box-shadow: 1px 1px 3px 1px #ddd;
    height: 510px;
    left: 200px;
}
.mallBannerContainer .mallBannerUserInfo{
    width: 320px;
    height: 100%;
    background-color: #fff;
}
.mallBannerContainer .mallBannerUserInfo .user{
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
}
.mallBannerContainer .mallBannerUserInfo .hellow{
    padding: 20px 0;
    font-size: 14px;
    font-weight: bold;
}
.mallBannerContainer .mallBannerUserInfo .portrait{
    width: 60px;
    height: 60px;
}
.mallBannerContainer .mallBannerUserInfo .portrait img{
    width: 100%;
    height: 100%;
    border-radius: 50%;
}
.mallBannerContainer .mallBannerUserInfo .login{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}
.mallBannerContainer .mallBannerUserInfo .btn{
    width: 90px;
    height: 46px;
    border-radius: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f5f5;
    font-size: 14px;
    color: #fff;
    cursor: pointer;
}
.mallBannerContainer .mallBannerUserInfo .loginBtn{
    margin-right: 20px;
    background-color: #ff5000 ;
}
.mallBannerContainer .mallBannerUserInfo .registerBtn{
    background-color: #ff9000  ;
}
.mallBannerContainer .mallBannerUserInfo .user .nv{
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    width: 100%;
    padding: 0 10px;
}
.mallBannerContainer .mallBannerUserInfo .user .nv li{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 14px;
}
.mallBannerContainer .mallBannerUserInfo .user .nv li .link{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 14px;
}
.mallBannerContainer .mallBannerUserInfo .user .nv li .num{
    color: red;
    font-weight: bold;
    margin-bottom: 6px;
}
.mallBannerContainer .mallBannerUserInfo .quickNav{
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    padding: 0 10px;
    margin-top: 30px;
}
.mallBannerContainer .mallBannerUserInfo .quickNav li{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;    
}
.mallBannerContainer .mallBannerUserInfo .quickNav li .link{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;    
}
.mallBannerContainer .mallBannerUserInfo .quickNav li .erpfont{
    font-size: 24px;
    color: #666;
}
.mallBannerContainer .mallBannerUserInfo .quickNav li .t{
    font-size: 14px;
    margin-top: 10px;
}
/* 零售商城购物车 */
.mallCartWarp{
   width: 100%;
   height: auto;
   background-color: #f5f5f5;
   padding: 15px 0;
}
.mallCartContainer {
   
    background-color: #fff;
}
.mallCartContainer .cartHead{
    height: 50px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    border-bottom: 1px solid #eee;
    padding: 0 15px;
}
.mallCartContainer .cartHead .l,.mallCartContainer .cartHead .r{
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.mallCartContainer .cartHead .l .t{
   font-size: 14px;
   font-weight: bold;
   color: #333;
}
.mallCartContainer .cartHead .r .price{
    font-size: 16px;
    color: #ff4000;
    margin: 0 10px;
}
.mallCartContainer .cartHead .r .btn{
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    width: 80px;
    height: 30px;
    cursor: pointer;
    background: linear-gradient(90deg,#ff7044,#ff4000);
    font-size: 14px;
    color: #fff;
}
.mallCartContainer .table{
    padding: 15px;
}
.mallCartContainer .table .item-1{
    width: 50%;
}
.mallCartContainer .table .item-2{
    width: 12%;
}
.mallCartContainer .table .item-3{
    width: 12%;
}
.mallCartContainer .table .item-4{
    width: 13%;
}
.mallCartContainer .table .item-5{
    width: 13%;
}
.mallCartContainer  .thead{
    height: 30px;
    font-weight: bold;
    margin-bottom: 10px;
}
.mallCartContainer  .thead .item-list,.mallCartContainer  .thead .item-list .item{
    height: 100%;
}
.mallCartContainer  .item{
  display: flex;
  align-items: center;
}
.mallCartContainer .thead .product_head{
    display: flex;
    flex-direction: row;
}
.mallCartContainer .thead .product_head .ant-checkbox-wrapper{
    position: relative;
    top: -3px;
}
.mallCartContainer .storeGroup {
    margin-bottom: 20px;
}
.mallCartContainer .storeGroup:last-child{
    margin-bottom: 0;
}
.mallCartContainer .storeGroup .store{
    height: 30px;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 5px;
}
.mallCartContainer .storeGroup .store .name{
    margin-left: 10px;
}
.mallCartContainer .storeGroup .list{
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
}
.mallCartContainer .storeGroup .list .tr{
   margin-bottom: 10px;
}
.mallCartContainer .storeGroup .list .tr:last-child{
    margin-bottom: 0;
 }
.mallCartContainer .product_info {
    display: flex;
    flex-direction: row;
}
.mallCartContainer .product_info .pic{
    width: 60px;
    height: 60px;
}
.mallCartContainer .product_info .pic img{
    width: 100%;
    height: 100%;
}
.mallCartContainer .product_info  .info{
    margin-left: 10px;
    line-height: 1.5;
}
.mallCartContainer .product_info .specification{
    color: #999;
}
.mallCartContainer .opt{
    display: flex;
    flex-direction: column;
  
}
.mallCartContainer .opt .btn{
    margin: 5px 0;
    cursor: pointer;
}
.mallCartContainer .cartFoot{
    height: 50px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    border-top: 1px solid #eee;
    padding: 0 15px;
}
.mallCartContainer .cartFoot .l,.mallCartContainer .cartFoot .r{
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.mallCartContainer .cartFoot .l .btn{
    margin-left: 10px;
    cursor: pointer;
}
.mallCartContainer .cartFoot .r .price{
    padding: 0 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.mallCartContainer .cartFoot .txt{
    display: flex;
    flex-direction: row;
    align-items: center;
}
.mallCartContainer .cartFoot .num{
    padding: 0 5px;
    color: #ff4000;
    font-size: 14px;
}
.mallCartContainer .cartFoot .r .price  .m{
    font-size: 16px;
    color: #ff4000;
    margin: 0 10px;
}
.mallCartContainer .cartFoot .r .btn{
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    width: 80px;
    height: 30px;
    cursor: pointer;
    background: linear-gradient(90deg,#ff7044,#ff4000);
    font-size: 14px;
    color: #fff;
}

.mobileMallCartContainer {
    display: none;
    width: 100%;
    padding: 10px;
    background: #fff;
    /* padding-bottom: 50px; */
    height: calc(100vh - 54px - 46px - 25px);
}
.mobileMallCartContainer .storeGroup{
    background-color: #fff;
    border-radius: 6px;
    overflow: hidden;
    padding: 10px;
    margin-bottom: 5px;
}
.mobileMallCartContainer .storeGroup:last-child{
    margin-bottom: 0;
}
.mobileMallCartContainer .storeGroup .store{
    
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 6px;
}
.mobileMallCartContainer .store .ant-checkbox-wrapper{
    position: relative;
    top: -2px;
}
.mobileMallCartContainer .store .name{
    font-weight: bold;
    color: #333;
    margin-left: 5px;
}
.mobileMallCartContainer .list{

}
.mobileMallCartContainer .list .tr{
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
}
.mobileMallCartContainer .list .tr:last-child{
    margin-bottom: 0;
}
.mobileMallCartContainer .list .check{
    width: 24px;
    height: 60px;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.mobileMallCartContainer .list .pic{
    width: 60px;
    height: 60px;
}
.mobileMallCartContainer .list .pic img{
    width: 100%;
    height: 100%;
    border-radius: 4px;
}
.mobileMallCartContainer .list .info{
    width: calc(100vw - 80px - 30px);
    padding-left: 10px;
}
.mobileMallCartContainer .list  .title{
    line-height: 1.5;
    margin-bottom: 5px;
    color: #333;
}
.mobileMallCartContainer .list  .specification{
    margin-bottom: 5px;
    font-size: #999;
}
.mobileMallCartContainer .list  .price{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}
.mobileMallCartContainer .list  .price .money{
    color: #ff4000;
    font-size: 14px;
    font-weight: bold;
}
.mobileMallCartFoot{
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 46px;
    background-color: #fff;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
}
.mobileMallCartFoot .l{
    display: flex;
    flex-direction: row;
    align-items: center;
}
.mobileMallCartFoot .l .price{
    font-size: 14px;
}
.mobileMallCartFoot .l .money{
    font-weight: bold;
}
.mobileMallCartFoot .r .btn{
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 15px;
    width: 60px;
    height: 30px;
    cursor: pointer;
    background: linear-gradient(90deg,#ff7044,#ff4000);
    font-size: 14px;
    color: #fff;
}
@media screen and (max-width: 760px) {
    .mallCartWarp{
        padding: 5px;
        height: calc(100vh - 52px);
    }
    .mobileMallCartContainer{
        display: block;
    }
    .mallCartContainer{
        display: none;
    }
    .mobileMallCartFoot{
        display: flex;
    }
}

/* 零售商城订单结算 */
.mallConfirmOrderHeaderWarp{
    height: 80px;
    width: 100%;
    border-bottom: 1px solid #eee;
}
.mallConfirmOrderHeaderContainer{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 100%;
}
.mallConfirmOrderHeaderContainer .l{
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.mallConfirmOrderHeaderContainer .logo2 .icon-chunlogo {
    color: #fb3434;
    font-size: 38px;
}

.mallConfirmOrderHeaderContainer .logo2 .name {
    font-size: 24px;
    color: #fb3434;
    font-weight: bold;
    padding-left: 6px;
}
.mallConfirmOrderHeaderContainer .r{
   flex: 1;
   max-width: 600px;

}
.mallConfirmOrderWarp{
    padding: 15px 0;

}
.mallConfirmOrderContainer .tit{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}
.mallConfirmOrderContainer .tit .name{
    font-size: 14px;
    font-weight: bold;
}
.mallConfirmOrderContainer .tit .add{
    margin-left: 20px;
    color: #1890ff;
    cursor: pointer;
}
.mallConfirmOrderContainer .tit .link{
    color: #c97;
    cursor: pointer;
}
.mallConfirmOrderContainer .addressList .list .default{
    position: absolute;
    top: 1px;
    right: 1px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 60px;
    height: 20px;
    background-color: #f2f2f2;
}
.mallConfirmOrderContainer .addressList .list ul{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}
.mallConfirmOrderContainer .addressList .list li{
    width: 240px;
    height: 104px;
    border: 1px solid #ccc;
    margin: 0 10px 10px 0;
    border-radius: 6px;
    position: relative;
    cursor: pointer;
}
.mallConfirmOrderContainer .addressList .list li:hover{
    border-color: #1877f2;
}
.mallConfirmOrderContainer .addressList .list li .hd{
    height: 30px;
    line-height: 30px;
    font-weight: bold;
    padding-left: 10px;
    border-bottom: 1px solid #eee;
}
.mallConfirmOrderContainer .addressList .list li .bd{
    padding: 10px;
}
.mallConfirmOrderContainer .addressList .list li .bd .con{
    line-height: 1.5;
    height: 36px;
}
.mallConfirmOrderContainer .addressList .list li .opt{
    display: flex;
    flex-direction: row;
    margin-right: 10px;
}
.mallConfirmOrderContainer .addressList .list li .opt .btn{
    color: #c97;
}
.mallConfirmOrderContainer  .addressList .list li.active{
    border-color: #1877f2;
}
.mallConfirmOrderContainer  .addressList .list li.active::before{
    content: "\E6C6";
    font-family: iconfont!important;
    font-size: 14px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    bottom: -2px;
    right: -2px;
    font-size: 26px;
    color: #1877f2
}
.mallConfirmOrderContainer .table{
    
}
.mallConfirmOrderContainer .table .item-1{
    width: 60%;
}
.mallConfirmOrderContainer .table .item-2{
    width: 13%;
}
.mallConfirmOrderContainer .table .item-3{
    width: 13%;
}
.mallConfirmOrderContainer .table .item-4{
    width: 14%;
}
.mallConfirmOrderContainer  .thead{
    height: 30px;
    font-weight: bold;
    margin-bottom: 10px;
}
.mallConfirmOrderContainer  .thead .item-list,.mallConfirmOrderContainer  .thead .item-list .item{
    height: 100%;
}
.mallConfirmOrderContainer  .item{
  display: flex;
  align-items: center;
}
.mallConfirmOrderContainer .thead .product_head{
    display: flex;
    flex-direction: row;
}
.mallConfirmOrderContainer .thead .product_head .ant-checkbox-wrapper{
    position: relative;
    top: -3px;
}
.mallConfirmOrderContainer .storeGroup {
    margin-bottom: 20px;
}
.mallConfirmOrderContainer .storeGroup:last-child{
    margin-bottom: 0;
}
.mallConfirmOrderContainer .storeGroup .store{
    height: 30px;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 5px;
}
.mallConfirmOrderContainer .storeGroup .store .name{
    margin-left: 10px;
}
.mallConfirmOrderContainer .storeGroup .list{
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
}
.mallConfirmOrderContainer .storeGroup .list .tr{
   margin-bottom: 10px;
}
.mallConfirmOrderContainer .storeGroup .list .tr:last-child{
    margin-bottom: 0;
 }
.mallConfirmOrderContainer .product_info {
    display: flex;
    flex-direction: row;
}
.mallConfirmOrderContainer .product_info .pic{
    width: 60px;
    height: 60px;
}
.mallConfirmOrderContainer .product_info .pic img{
    width: 100%;
    height: 100%;
}
.mallConfirmOrderContainer .product_info  .info{
    margin-left: 10px;
    line-height: 1.5;
}
.mallConfirmOrderContainer .product_info .specification{
    color: #999;
}
.mallConfirmOrderContainer .opt{
    display: flex;
    flex-direction: column;
  
}
.mallConfirmOrderContainer .opt .btn{
    margin: 5px 0;
    cursor: pointer;
}
.mallConfirmOrderContainer  .paymentMethod {
    margin: 20px 0 20px 0
}
.mallConfirmOrderContainer  .paymentMethod .list{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}
.mallConfirmOrderContainer  .paymentMethod .list li{
    margin: 0 10px 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 36px;
    border: 1px solid #ccc;
    color: #333;
    border-radius: 6px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}
.mallConfirmOrderContainer  .paymentMethod .list li.active{
    border-color: #1877f2;
}
.mallConfirmOrderContainer  .paymentMethod .list li.active::before{
    content: "\E6C6";
    font-family: iconfont!important;
    font-size: 14px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    bottom: -1px;
    right: -1px;
    font-size: 18px;
    color: #1877f2
}
.mallConfirmOrderContainer .payTotal{
    margin-top: 20px;
}
.mallConfirmOrderContainer .payTotal .total{
    text-align: right;
    line-height: 1.6;
}

.mallConfirmOrderContainer .payTotal .total2{
    margin-top: 5px;
    background-color: #F5F5F5;
    padding: 10px;
    text-align: right;
    line-height: 1.6;
}
.mallConfirmOrderContainer .payTotal .total2 .money{
    font-size: 14px;
    color: #ff4000;
    font-weight: bold;
}
.mallConfirmOrderContainer .payTotal .total2 .address{
    margin-top: 6px;
    color: #999;
}
.mallConfirmOrderContainer .submitCon{
    margin-top: 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: end;
}
.mallConfirmOrderContainer .submitCon .payBtn{
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    width: 120px;
    height: 40px;
    cursor: pointer;
    background: linear-gradient(90deg,#ff7044,#ff4000);
    font-size: 14px;
    color: #fff;
}

.mobileMallConfirmOrderContainer .addressContainer{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    background-color: #fff;
    border-bottom: 1px solid #b8d7ff;
    margin-bottom: 5px;
}
.mobileMallConfirmOrderContainer .addressContainer .p2{
    padding: 10px 0;
    font-size: 16px;
    font-weight: bold;
    color: #333;
}
.mobileMallConfirmOrderContainer .addressContainer .arrow{
    font-size: 16px;
    color: #e1d9d9;
}
.mobileMallConfirmOrderContainer{
   
    background-color: #f5f5f5;
    height: calc(100vh -90px);
}
.mobileMallConfirmOrderList {
    background-color:#fff;
    padding:5px;
}
.mobileMallConfirmOrderList .storeGroup {
    background-color: #fff;
    border-radius: 6px;
    overflow: hidden;
    padding: 10px;
    margin-bottom: 5px;
}
.mobileMallConfirmOrderList .storeGroup:last-child{
    margin-bottom: 0;
}
.mobileMallConfirmOrderList .storeGroup .store{
    
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 6px;
}
.mobileMallConfirmOrderList .storeGroup .store .ant-checkbox-wrapper{
    position: relative;
    top: -2px;
}
.mobileMallConfirmOrderList .storeGroup .store .name{
    font-weight: bold;
    color: #333;
    margin-left: 5px;
}
.mobileMallConfirmOrderList .storeGroup .list{

}
.mobileMallConfirmOrderList .tr{
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
}
.mobileMallConfirmOrderList .tr:last-child{
    margin-bottom: 0;
}
.mobileMallConfirmOrderList  .check{
    width: 24px;
    height: 60px;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.mobileMallConfirmOrderList  .pic{
    width: 60px;
    height: 60px;
}
.mobileMallConfirmOrderList .pic img{
    width: 100%;
    height: 100%;
    border-radius: 4px;
}
.mobileMallConfirmOrderList .info{
    width: calc(100vw - 80px - 30px);
    padding-left: 10px;
}
.mobileMallConfirmOrderList  .title{
    line-height: 1.5;
    margin-bottom: 5px;
    color: #333;
}
.mobileMallConfirmOrderList .specification{
    margin-bottom: 5px;
    font-size: #999;
}
.mobileMallConfirmOrderList .price{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}
.mobileMallConfirmOrderList  .price .money{
    color: #ff4000;
    font-size: 14px;
    font-weight: bold;
}
.mobileMallConfirmOrderTotal{
    margin-top: 5px;
    background-color: #fff;
    padding: 10px;
    
}
.mobileMallConfirmOrderTotal li{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    line-height: 24px;
}
.mobileMallConfirmOrderTotal li .money{
    font-weight: bold;
}
.mobileMallTopbackContainer{
    position: fixed;
    top: 0;
    left: 0;
    display: none;
    flex-direction: row;
    align-items: center;
    background-color: #fff;
    height: 40px;
    width: 100%;
}
.mobileMallTopbackContainer .back{
    width: 40px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
}
.mobileMallTopbackContainer .tit{
    padding-right: 40px;
    font-size: 14px;
    text-align: center;
    flex: 1;
    color: #333;
}
.mobileMallConfirmOrderFoot{
    display: none;
    padding: 0 10px;
    position: fixed;
    bottom: 0;
    left: 0;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 50px;
    background-color: #fff;
}
.mobileMallConfirmOrderFoot .price{
    color: #ff4000;
    font-size: 14px; 
    font-weight: bold;
}
.mobileMallConfirmOrderFoot .payBtn{
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 15px;
    width: 80px;
    height: 30px;
    cursor: pointer;
    background: linear-gradient(90deg,#ff7044,#ff4000);
    font-size: 14px;
    color: #fff;
}
.mobileSelectAddress{
    width: 100vw;
    height: 100vh;
    background-color: #fff;
    position: fixed;
    top: 0;
    left: 100%;
    z-index: 999;
    transition: transform 1s;
}

.mobileSelectAddress .mobileSelectAddressContent{
    height: calc(100vh - 95px);
    background-color: #f5f5f5;
    overflow: auto;
}
.mobileSelectAddress .list{
    width: 100%;
    background-color: #fff;
   
}
.mobileSelectAddress .list li{
    padding: 10px;
    border-bottom: 1px solid #f2f2f2;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.mobileSelectAddress .list li .p2{
    padding: 10px 0;
    font-size: 14px;
    font-weight: bold;
    color: #333;
}
.mobileSelectAddress .list li .check{
    color: #ff4000;
    width: 26px;
    font-size: 18px;
}
.mobileSelectAddress .list li .edit{
    padding-left: 10px;
    height: 100%;
    color: #999;
    width: 36px;
}
.mobileSelectAddress .list li .con{
    flex: 1;
}
.mobileSelectAddress .mobileSelectAddressFoot{
    padding: 0 10px;
    position: relative;
    bottom: 0;
    left: 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 50px;
    background-color: #fff;
}
.mobileSelectAddress .mobileSelectAddressFoot .btn{
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 15px;
    width: 80%;
    height: 30px;
    cursor: pointer;
    background: linear-gradient(90deg,#ff7044,#ff4000);
    font-size: 14px;
    color: #fff;
}
.mobileSelectAddress .mobileMallTopbackContainer{
    position: relative;
}
@media screen and (max-width: 760px) {
    .mobileMallPageWarp{
        display: block;
        padding-top: 40px;
    }
    .pcMallPageWarp{
        display: none;
    }
    .mobileMallTopbackContainer{
        display: flex;
    }
    .mobileMallConfirmOrderFoot{
        display: flex;
    }
}
/* 零售商城收藏商品 */
.mallCollectGoodsHeaderWarp{
    height: 80px;
    width: 100%;
    border-bottom: 1px solid #eee;
}
.mallCollectGoodsHeaderContainer{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 100%;
}
.mallCollectGoodsHeaderContainer .l{
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.mallCollectGoodsHeaderContainer .logo2 .icon-chunlogo {
    color: #fb3434;
    font-size: 38px;
}

.mallCollectGoodsHeaderContainer .logo2 .name {
    font-size: 24px;
    color: #fb3434;
    font-weight: bold;
    padding-left: 6px;
}
.mallCollectGoodsHeaderContainer .r{
   flex: 1;
   max-width: 600px;
}
.mallCollectGoodsWarp{
    width: 100%;
    background-color: #f5f5f5;
}
.mallCollectGoodsContent{
    padding-top: 10px;
}
@media screen and (max-width: 760px) {
    .mallCollectGoodsContent{
        padding-top: 5px;
    }
    .mallCollectGoodsHeaderWarp{
        display: none;
    }
    .mallCollectGoodsWarp{
        padding-top: 40px;
        height: calc(100vh);
        box-sizing: border-box;
    }
}

/* 零售商城收藏商品 */
.browseHistoryHeaderWarp{
    height: 80px;
    width: 100%;
    border-bottom: 1px solid #eee;
}
.browseHistoryHeaderContainer{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 100%;
}
.browseHistoryHeaderContainer .l{
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.browseHistoryHeaderContainer .logo2 .icon-chunlogo {
    color: #fb3434;
    font-size: 38px;
}

.browseHistoryHeaderContainer .logo2 .name {
    font-size: 24px;
    color: #fb3434;
    font-weight: bold;
    padding-left: 6px;
}
.browseHistoryHeaderContainer .r{
   flex: 1;
   max-width: 600px;
}
.browseHistoryWarp{
    width: 100%;
    background-color: #f5f5f5;
}
.browseHistoryContent{
    padding-top: 10px;
}
.browseHistoryContent .dateTime{
    font-size: 20px;
    font-weight: bold;
    padding: 20px 0;
}
@media screen and (max-width: 760px) {
    .browseHistoryContent{
        padding-top: 5px;
    }
    .browseHistoryHeaderWarp{
        display: none;
    }
    .browseHistoryWarp{
        padding-top: 40px;
        height: calc(100vh);
        box-sizing: border-box;
    }
}
/* 零售商城PC端Footer */
.mallFooterWarp{
    border-top: 1px solid #eee;
    background-color: #fff;
    width: 100%;
}
.mallFooterNavWarp{
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}
.mallFooterWarp .promiseContent {
    padding: 50px 0;
}
.mallFooterWarp .promiseContent .list{
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 16px;
    color: #333;
}
.mallFooterWarp .promiseContent .list li{
    display: flex;
    flex-direction: row;
    align-items: center;
    flex: 1;
}
.mallFooterWarp .promiseContent .list .erpfont{
    font-size: 30px;
    margin-right: 5px;
}
.mallFooterNavContainer{
    padding: 30px 0;
    display: flex;
    flex-direction: row;
    font-size: 13px;
}
.mallFooterNavContainer .footNav,.mallFooterNavContainer .contact{
    flex: 1;
}
.mallFooterNavContainer .footNav dt{
    color: #333;
    font-weight: bold;
    margin-bottom: 10px;
}
.mallFooterNavContainer .footNav dd{
    line-height: 24px;
}
.mallFooterNavContainer .contact {
    font-size: 13px;
}
.mallFooterNavContainer .contact .phone{
    color: #F11415;
    font-size: 20px;
    margin-bottom: 15px;
}
.mallFooterNavContainer .contact .time{
    
}
.copyrightContainer{
    padding: 15px 0;
   display: flex;
   align-items: center;
   justify-content: center;
   font-size: 16px;
   color: #333;
}
@media screen and (max-width: 760px) {
    .mallFooterWarp{
        display: none;
    }
}
.symbol {
    font-size: 12px;
}

.big-num {
    font-size: 16px;
}

.small-num {
    font-size: 12px;
}


/* 新权限详情 */

.permissionFunCore.permissionFun {
    flex: 1;
}

.permissionFunCore.permissionFun .ant-checkbox-wrapper {
    font-size: 14px !important;
    color: #414852;
}

.permissionFunCore.permissionFun .level1 {
    border: 1px solid #eee;
    margin-bottom: 15px;

}

.permissionFunCore.permissionFun .level1 .con {
    padding: 10px;
}

.permissionFunCore.permissionFun .level1 .tit1 {
    height: 40px;
    background-color: #f2fbff;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-left: 15px;
    cursor: pointer;
}

.permissionFunCore.permissionFun .level1 .tit1 .icon-down {
    font-size: 14px;
    color: #333;
    cursor: pointer;
    padding: 10px;
    margin-right: 10px;
    transition: transform .24s;
}
.permissionFunCore.permissionFun .level1 .tit1 .icon-down::before{
    /* content: "\e65f"; */
}

.permissionFunCore.permissionFun .level1 .tit1 .icon-down.show {
    transform: rotate(-90deg);
}

.permissionFunCore.permissionFun .ant-checkbox {
    top: 0;
}

.permissionFunCore.permissionFun .level1 .name {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-weight: bold;
}

.permissionFunCore.permissionFun .level2 {
    border: 1px solid #eee;
    margin-bottom: 10px;
}

.permissionFunCore.permissionFun .level2:last-child {
    margin-bottom: 0;
}

.permissionFunCore.permissionFun .level2 .tit2 {
    height: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-left: 15px;
    border-bottom: 1px solid #eee;
    font-weight: bold;
    cursor: pointer;
}

.permissionFunCore.permissionFun .level2 .tit2 .icon-down {
    font-size: 14px;
    color: #333;
    cursor: pointer;
    padding: 10px;
    font-weight: normal;
    transition: transform .24s;
}

.permissionFunCore.permissionFun .level2 .tit2 .icon-down.close {
    transform: rotate(-90deg);
}

.permissionFunCore.permissionFun .level3 {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding: 15px;
    padding-bottom: 5px;
}

.permissionFunCore.permissionFun .level3 .it {
    margin-bottom: 10px;
    width: calc(100% / 6);
}
@media screen and (max-width: 1200px) {
    .permissionFunCore.permissionFun .level3 .it {
        width: calc(100% / 4);
    }
}
@media screen and (max-width: 960px) {
    .permissionFunCore.permissionFun .level3 .it {
        width: calc(100% / 3);
    }
}
@media screen and (max-width: 768px) {
    .permissionFunCore.permissionFun .level3 .it {
        width: calc(100% / 2);
    }
}
@media screen and (max-width: 640px) {
    .permissionFunCore.permissionFun .level3 .it {
        width: calc(100% / 1);
    }
}


/* 表单新样式 */
.corePageContainerNew {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 100%;
    padding-top: 5px;
}
.coreTitleBarNew {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    padding: 15px;
    border-bottom: 1px solid #eee;
    position: sticky;
    top: 0;
    z-index: 99;
}
.coreTitleBarNew .left{
    position: relative;
}
.coreTitleBarNew .left::before {
    content: "";
    width: 2px;
    height: 16px;
    background-color: #218ceb;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
}

.coreTitleBarNew .name {
  
    display: inline-block;
    font-size: 16px;
    font-weight: 700;
    color: #000;
    padding-left: 10px;
}

.coreFormDataSelectFormNew{
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 10px;
}
.coreFormDataSelectFormNew .label{
    margin-right: 10px;
}
.coreSearchListNew {
    padding: 15px;
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: #fff;
}

.coreSearchListNew li {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-right: 10px
}

.coreSearchListNew li .label {
    margin-right: 6px;
    font-size: 13px;
}

.coreSearchListNew li .opt {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.baseTableContainerNew {
    width: 100%;
    flex: 1;
    overflow-x: auto;
}
.coreTableNew{
    overflow-x: auto;
    background-color: #fff;
}
.coreTableNew .thead{
    width: 100%;
    position: sticky;
    top: 0;
}
.coreTableNew .itemListNew{
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: stretch;
}
.coreTableNew .itemListNew .item{
    padding: 10px;
    position: relative;
    flex-shrink: 0;
    flex-grow: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
    word-break: break-all;
    line-height: 1.6;
    font-size: 12px !important;
    color: #666;
}
.coreTableNew .itemListNew .item.pointer{
    cursor: pointer;
}
.coreTableNew .itemListNew .item.checkbox{
    width: 60px;flex-grow:0;flex-shrink: 0;
}
.coreTableNew .thead .itemListNew .item.opt{
    width:100px;position:sticky;right:0;flex-grow:0;
}
.coreTableNew .tbody .itemListNew .item.opt{
    width:100px;position:sticky;right:0;background-color:#fff;z-index:90;flex-grow:0;
}
.coreTableNew .thead .itemListNew .item{
    background-color: #fafafa;
   padding: 12px 10px;
}
.coreTableNew .thead .itemListNew .item::before{
    position: absolute;
    top: 50%;
    right: 0;
    width: 1px;
    height: 1.6em;
    background-color: rgba(0, 0, 0, 0.06);
    transform: translateY(-50%);
    transition: background-color 0.3s;
    content: '';
}
.coreTableNew .itemListNew .item .sort{
    width: 13px;
    color: #999;
    margin-left: 5px;
}

.formDataPaginationContainer{
    width: 100%;
    padding: 5px;
    border-top: 1px solid #f0f0f0;
    background-color: #fafafa;
}

/* 移动端列表 */
.moblieOperation{
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: #fff;
    padding: 10px;
    border-bottom: 1px solid #eee;
}
.moblieOperation button{
    margin-right: 10px;
}

.baseTableContainerNew .baseTableContainerNew .moblieTableList{
    background-color: #f5f5f5;
}
.baseTableContainerNew .moblieTableList .list li{
    background-color: #fff;
    margin-bottom: 6px;
    display: flex;
    flex-direction: column;
}
.baseTableContainerNew .moblieTableList .ant-checkbox{
    top: 0 !important;
}
.baseTableContainerNew .moblieTableList .list li .head{
   
    border-bottom: 1px solid #eee;
    font-weight: bold;
    font-size: 14px;
    color: #333;
    padding: 10px 0 ;
    padding-left: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.baseTableContainerNew .moblieTableList .list li .body{
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 10px;
}
.baseTableContainerNew .moblieTableList .list li.item-text .link{
    display: flex;
    flex-direction: column;
    flex: 1;
}
.baseTableContainerNew .moblieTableList .list li.item-text .data{
    display: flex;
    flex-direction: column;
    flex: 1;
}
.baseTableContainerNew .moblieTableList .list li.item-text .tit{
    border-bottom: 1px solid #eee;
    font-weight: bold;
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
    padding: 5px 0 15px 0;
}
.baseTableContainerNew .moblieTableList .list li.item-text  .p{
    line-height: 1.8;
}
.baseTableContainerNew .moblieTableList .list li.item-text .label{
    color: #999;
}


.baseTableContainerNew .moblieTableList .list li.item-picture .link{
    display: flex;
    flex-direction: row;
    align-items: center;
    flex: 1;
}
.baseTableContainerNew .moblieTableList .list li.item-picture .img img{
    width: 60px;
    height: 60px;
    border-radius: 8px;
}
.baseTableContainerNew .moblieTableList li.item-picture .info{
    margin-left: 10px;
}
.baseTableContainerNew .moblieTableList .list li.item-picture .info .tit{
    font-weight: bold;
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
}
.baseTableContainerNew .moblieTableList .list li.item-picture .info  .p{
    line-height: 1.8;
}
.baseTableContainerNew .moblieTableList .list li.item-picture .info .label{
    color: #999;
}
.baseTableContainerNew .moblieTableList .body .data dd{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px dashed #eee;
    padding: 10px 0;
    font-size: 12px;
    color: #666;
}
.baseTableContainerNew .moblieTableList .body .data .text{
    color: #333;
}

.baseTableContainerNew .moblieTableList .optContainer{
    border-top: 1px solid #eee;
    padding: 10px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}
.baseTableContainerNew .moblieTableList .optContainer .right,.baseTableContainerNew .moblieTableList .optContainer .left{
    display: flex;
    flex-direction: row;
    align-items: center;
}
.baseTableContainerNew .moblieTableList .optContainer .btn{
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 10px;
    color: #a5a8ab;
    border: 1px solid #d4d4d5;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
    line-height: 1;
}
.baseTableContainerNew .moblieTableList .optContainer .btn.edit{
    color: #1890ff;
    border-color: #1890ff;
}
.coreEditPageContentNew{
    flex: 1;
    overflow-x: auto;
    padding: 20px;
    background-color: #fff;
}
.coreEditPageContentNew .form{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}
.coreEditPageContentNew .form li{
    width: calc(100% / 3);
}
.coreEditPageContentNew .form .label{
    text-align: right;
    width: 90px;
}
.coreEditPageContentNew .form .con{
    flex: 1;
}

@media screen and (max-width: 1000px) {
    .coreEditPageContentNew .form li{
        width: calc(100% / 2);
    }
}


@media screen and (max-width: 760px) {
    .coreTitleBarNew{
        flex-direction: column;
        align-items: flex-start;
    }
    .coreTitleBarNew .operation{
        margin-top: 15px;
    }
    .coreEditPageContentNew .form li{
        width: 100%;
    }
}

.ant-btn{
    font-size: 13px !important;
    border-radius: 4px !important;
}
.ant-btn-sm{
    padding: 0 10px;
    font-size: 12px !important;
}
.ant-input-affix-wrapper{
    border-radius: 4px !important;
}

.moblieSearchForm {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    
}
.moblieSearchForm .form{
    flex: 1;
    overflow-y: auto;
    padding: 15px;
}
.moblieSearchForm .form li{
    margin-bottom: 15px;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.moblieSearchForm .form li .label{
    width: 80px;
    text-align: right;
}
.moblieSearchForm .form li .con{
    flex: 1;
}
.moblieSearchForm .submitCon{
    padding: 12px 15px;
    border-top: 1px solid #eee;
    display: flex;
    flex-direction: row;
    align-items: center;
}
.moblieSelectInput{
    height: 100%;
    overflow-y: auto;
}
.moblieSelectInput li{
    padding: 15px 24px;
    border-bottom: 1px solid #eee;
    font-size:14px;
}
    .moblieSelectInput li.active {
        color: #1877F2;
    }
.moblieRadioGroup{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}
.moblieRadioGroup .radio{
    position: relative;
    box-sizing: border-box;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f2f2f2;
    border:1px solid #f2f2f2;
    font-size: 12px;
    color: #666;
    border-radius: 4px;
    width: calc((100% - 30px) / 3);
    margin: 0 10px 10px 0;
    cursor: pointer;
}
.moblieRadioGroup .radio.active{
    border:1px solid #1890ff;
    background-color: #def0ff;
    color: #1890ff;
}
.ant-drawer-body{
    padding: 0 !important;
}


/* 角色管理 */
.functionalPermissionsNew {
    flex: 1;
    border: 1px solid #edf1f5;
    border-bottom: none;
}

.functionalPermissionsNew .groupName {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 46px;
    padding: 0 15px;
    border-bottom: 1px solid #edf1f5;
    background-color: #f8f8f8;
    cursor: pointer;
}

.functionalPermissionsNew .childMenus {
    background-color: #fff;
    padding: 15px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding-bottom: 5px;
    border-bottom: 1px solid #edf1f5;
}

.functionalPermissionsNew .childMenus .menu {
    margin-bottom: 10px;
    width: calc(100% / 6);
}

.functionalPermissionsNew .ant-checkbox-wrapper {
    font-size: 14px !important;
    color: #414852;
}

.coreEditPageContent .ant-checkbox {
    top: 0;
}

.functionalPermissionsNew .icon-down {
    font-size: 14px;
    color: #333;
    cursor: pointer;
    padding: 10px;
    transition: transform .24s;
}

.functionalPermissionsNew .icon-down.close {
    transform: rotate(-90deg);
}
@media screen and (max-width: 1200px) {
    .functionalPermissionsNew .childMenus .menu {
        width: calc(100% / 4);
    }
}
@media screen and (max-width: 960px) {
    .functionalPermissionsNew .childMenus .menu {
        width: calc(100% / 3);
    }
}
@media screen and (max-width: 768px) {
    .functionalPermissionsNew .childMenus .menu {
        width: calc(100% / 2);
    }
}
@media screen and (max-width: 640px) {
    .functionalPermissionsNew .childMenus .menu {
        width: calc(100% / 1);
    }
}