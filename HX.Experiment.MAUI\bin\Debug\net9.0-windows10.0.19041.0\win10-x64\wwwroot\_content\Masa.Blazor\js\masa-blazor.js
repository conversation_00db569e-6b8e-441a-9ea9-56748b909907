!function(){"use strict";function e(e,t,n,o){return new(n||(n=Promise))((function(r,i){function l(e){try{c(o.next(e))}catch(e){i(e)}}function s(e){try{c(o.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(l,s)}c((o=o.apply(e,t||[])).next())}))}"function"==typeof SuppressedError&&SuppressedError;const t="$parent.",n="undefined"!=typeof window;let o=!1;try{if(n){const e=Object.defineProperty({},"passive",{get:()=>{o=!0}});window.addEventListener("testListener",e,e),window.removeEventListener("testListener",e,e)}}catch(e){console.warn(e)}const r=Object.freeze({enter:13,tab:9,delete:46,esc:27,space:32,up:38,down:40,left:37,right:39,end:35,home:36,del:46,backspace:8,insert:45,pageup:33,pagedown:34,shift:16});function i(e){if(!e)return null;let t=e.getAttributeNames().find((e=>e.startsWith("_bl_")));return t&&(t=t.substring(4)),t}function l(e){if(e instanceof Element){for(var t=[];e.nodeType===Node.ELEMENT_NODE;){var n=e.nodeName.toLowerCase();if(e.id){n="#"+e.id,t.unshift(n);break}for(var o=e,r=1;o=o.previousElementSibling;)o.nodeName.toLowerCase()==n&&r++;1!=r&&(n+=":nth-of-type("+r+")"),t.unshift(n),e=e.parentNode}return t.join(" > ")}}function s(e){let t;try{if(e)if("string"==typeof e)if("document"===e)t=document.documentElement;else if(e.indexOf("__.__")>0){let n=e.split("__.__"),o=0,r=document.querySelector(n[o++]);if(r)for(;n[o];)r=r[n[o]],o++;r instanceof HTMLElement&&(t=r)}else t=document.querySelector(e);else t=e;else t=document.body}catch(e){console.error(e)}return t}const c=!!(n&&"undefined"!=typeof document&&window.document&&window.document.createElement);function a(e){var n;if(function(e){return e.startsWith(t)}(e)){const o=e.replace(t,""),r=null===(n=document.querySelector(o))||void 0===n?void 0:n.parentElement;return r?r.classList.contains("m-btn__content")?r.parentElement:r:null}return null}var u=Object.freeze({__proto__:null,registerInfiniteScrollJSInterop:function(t,n,o,r,i){if(!t||!n)return void console.warn("[MInfiniteScroll] Element or container is not provided. Make sure to provide the correct element and container.");let l;if(l="window"===n?window:"document"===n?document.documentElement:document.querySelector(n),l)return l.addEventListener("scroll",s),s(),{check:s,updateThreshold:e=>o=e,updateEnable:e=>r=e,dispose:()=>{i.dispose(),l.removeEventListener("scroll",s)}};function s(){return e(this,void 0,void 0,(function*(){if(!1===r)return;const e=t.getBoundingClientRect().top;(l===window?window.innerHeight:l.getBoundingClientRect().bottom)>=e-o&&(yield i.invokeMethodAsync("OnScrollInternal",!1))}))}console.warn(`[MInfiniteScroll] Element with selector '${n}' not found.`)}});const d=["touchcancel","touchend","touchmove","touchenter","touchleave","touchstart"];function f(e){return{detail:e.detail,screenX:e.screenX,screenY:e.screenY,clientX:e.clientX,clientY:e.clientY,offsetX:e.offsetX,offsetY:e.offsetY,pageX:e.pageX,pageY:e.pageY,button:e.button,buttons:e.buttons,ctrlKey:e.ctrlKey,shiftKey:e.shiftKey,altKey:e.altKey,metaKey:e.metaKey,type:e.type}}function p(e){return{detail:e.detail,touches:m(e.touches),targetTouches:m(e.targetTouches),changedTouches:m(e.changedTouches),ctrlKey:e.ctrlKey,shiftKey:e.shiftKey,altKey:e.altKey,metaKey:e.metaKey,type:e.type}}function m(e){const t=[];for(let n=0;n<e.length;n++){const o=e[n];t.push({identifier:o.identifier,clientX:o.clientX,clientY:o.clientY,screenX:o.screenX,screenY:o.screenY,pageX:o.pageX,pageY:o.pageY})}return t}function h(e,t){Blazor&&Blazor.registerCustomEventType(e,{browserEventName:t,createEventArgs:e=>g("mouse",e)})}function v(e,t){Blazor&&Blazor.registerCustomEventType(e,{browserEventName:t,createEventArgs:e=>{const t=(n=e,Object.assign(Object.assign({},f(n)),{dataTransfer:n.dataTransfer?{dropEffect:n.dataTransfer.dropEffect,effectAllowed:n.dataTransfer.effectAllowed,files:Array.from(n.dataTransfer.files).map((e=>e.name)),items:Array.from(n.dataTransfer.items).map((e=>({kind:e.kind,type:e.type}))),types:n.dataTransfer.types}:null}));var n;const o=e.dataTransfer.getData("data-value"),r=e.dataTransfer.getData("offsetX"),i=e.dataTransfer.getData("offsetY");return t.dataTransfer.data={value:o,offsetX:Number(r),offsetY:Number(i)},t}})}function g(e,t){let n={target:{}};return"mouse"===e?n=Object.assign(Object.assign({},n),f(t)):"touch"===e&&(n=Object.assign(Object.assign({},n),p(t))),n.target=function(e){const t=e,n={},o=t.getAttributeNames().find((e=>e.startsWith("_bl_")));return o?(n.elementReferenceId=o,n.selector=`[${o}]`):n.selector=l(t),n.class=t.getAttribute("class"),n}(t.target),n}let y=0;const w={};var b=Object.freeze({__proto__:null,registerSliderEvents:function(t,n){w[y]=c;const r=document.querySelector("[data-app]"),i=!o||{passive:!0,capture:!0},l=!!o&&{passive:!0};t.addEventListener("mousedown",c),t.addEventListener("touchstart",c);const s=t.querySelector(".m-slider__track-container");return y++;function c(t){return e(this,void 0,void 0,(function*(){const e="touches"in t;u(t),r.addEventListener(e?"touchmove":"mousemove",u,l),function(e,t,n,o=!1){const r=i=>{n(i),e.removeEventListener(t,r,o)};e.addEventListener(t,r,o)}(r,e?"touchend":"mouseup",a,i);var o=s.getBoundingClientRect();e?yield n.invokeMethodAsync("OnTouchStartInternal",{touchEventArgs:g("touch",t),trackRect:o}):yield n.invokeMethodAsync("OnMouseDownInternal",{mouseEventArgs:g("mouse",t),trackRect:o})}))}function a(t){return e(this,void 0,void 0,(function*(){t.stopPropagation(),r.removeEventListener("touchmove",u,l),r.removeEventListener("mousemove",u,l);const e="touches"in t,o={type:t.type,clientX:e?t.changedTouches[0].clientX:t.clientX,clientY:e?t.changedTouches[0].clientY:t.clientY};yield n.invokeMethodAsync("OnMouseUpInternal",o)}))}function u(t){return e(this,void 0,void 0,(function*(){const e="touches"in t,o={type:t.type,clientX:e?t.touches[0].clientX:t.clientX,clientY:e?t.touches[0].clientY:t.clientY};var r=s.getBoundingClientRect();yield n.invokeMethodAsync("OnMouseMoveInternal",{mouseEventArgs:o,trackRect:r})}))}},unregisterSliderEvents:function(e,t){if(e){const n=w[t];e.removeEventListener("mousedown",n),e.removeEventListener("touchstart",n),delete w[t]}}});let E=0;const T={};function L(e,t,n){e.style.height="0";const o=e.scrollHeight,r=parseInt(t,10)*parseFloat(n);e.style.height=Math.max(o,r)+"px"}var _=Object.freeze({__proto__:null,registerTextareaAutoGrowEvent:function(e){const t=e=>{const t=e.target;if(void 0===t.getAttribute("data-auto-grow"))return;const n=t.getAttribute("rows"),o=t.getAttribute("data-row-height");L(t,n,o)};return T[E]=t,e.addEventListener("input",t),E++},unregisterTextareaAutoGrowEvent:function(e,t){if(!e)return;const n=T[t];n&&e.removeEventListener("input",n)},calculateTextareaHeight:L}),C=function(e,t,n){var o=null,r=null,i=function(){o&&(clearTimeout(o),r=null,o=null)},l=function(){if(!t)return e.apply(this,arguments);var l=this,s=arguments,c=n&&!o;return i(),r=function(){e.apply(l,s)},o=setTimeout((function(){if(o=null,!c){var e=r;return r=null,e()}}),t),c?r():void 0};return l.cancel=i,l.flush=function(){var e=r;i(),e&&e()},l};var x=function(e,t,n){var o=null,r=null,i=n&&n.leading,l=n&&n.trailing;null==i&&(i=!0);null==l&&(l=!i);1==i&&(l=!1);var s=function(){o&&(clearTimeout(o),o=null)},c=function(){var n=i&&!o,s=this,c=arguments;if(r=function(){return e.apply(s,c)},o||(o=setTimeout((function(){if(o=null,l)return r()}),t)),n)return n=!1,r()};return c.cancel=s,c.flush=function(){var e=r;s(),e&&e()},c};function S(){var e,t;h("exmousedown","mousedown"),h("exmouseup","mouseup"),h("exclick","click"),h("exmouseleave","mouseleave"),h("exmouseenter","mouseenter"),h("exmousemove","mousemove"),e="extouchstart",t="touchstart",Blazor&&Blazor.registerCustomEventType(e,{browserEventName:t,createEventArgs:e=>g("touch",e)}),function(e,t){Blazor&&Blazor.registerCustomEventType(e,{browserEventName:t})}("transitionend","transitionend"),v("exdrop","drop"),Blazor&&Blazor.registerCustomEventType("auxclick",{browserEventName:"auxclick",createEventArgs:f})}function k(e,t){e.style.transform=t,e.style.webkitTransform=t}function A(e){return"TouchEvent"===e.constructor.name}function M(e){return"KeyboardEvent"===e.constructor.name}const O={show(e,t,n={}){if(!t._ripple||!t._ripple.enabled)return;const o=document.createElement("span"),r=document.createElement("span");o.appendChild(r),o.className="m-ripple__container",n.class&&(o.className+=` ${n.class}`);const{radius:i,scale:l,x:s,y:c,centerX:a,centerY:u}=((e,t,n={})=>{let o=0,r=0;if(!M(e)){const n=t.getBoundingClientRect(),i=A(e)?e.touches[e.touches.length-1]:e;o=i.clientX-n.left,r=i.clientY-n.top}let i=0,l=.3;t._ripple&&t._ripple.circle?(l=.15,i=t.clientWidth/2,i=n.center?i:i+Math.sqrt((o-i)**2+(r-i)**2)/4):i=Math.sqrt(t.clientWidth**2+t.clientHeight**2)/2;const s=(t.clientWidth-2*i)/2+"px",c=(t.clientHeight-2*i)/2+"px";return{radius:i,scale:l,x:n.center?s:o-i+"px",y:n.center?c:r-i+"px",centerX:s,centerY:c}})(e,t,n),d=2*i+"px";r.className="m-ripple__animation",r.style.width=d,r.style.height=d,t.appendChild(o);const f=window.getComputedStyle(t);f&&"static"===f.position&&(t.style.position="relative",t.dataset.previousPosition="static"),r.classList.add("m-ripple__animation--enter"),r.classList.add("m-ripple__animation--visible"),k(r,`translate(${s}, ${c}) scale3d(${l},${l},${l})`),r.dataset.activated=String(performance.now()),setTimeout((()=>{r.classList.remove("m-ripple__animation--enter"),r.classList.add("m-ripple__animation--in"),k(r,`translate(${a}, ${u}) scale3d(1,1,1)`)}),0)},hide(e){if(!e||!e._ripple||!e._ripple.enabled)return;const t=e.getElementsByClassName("m-ripple__animation");if(0===t.length)return;const n=t[t.length-1];if(n.dataset.isHiding)return;n.dataset.isHiding="true";const o=performance.now()-Number(n.dataset.activated),r=Math.max(250-o,0);setTimeout((()=>{n.classList.remove("m-ripple__animation--in"),n.classList.add("m-ripple__animation--out"),setTimeout((()=>{var t;1===e.getElementsByClassName("m-ripple__animation").length&&e.dataset.previousPosition&&(e.style.position=e.dataset.previousPosition,delete e.dataset.previousPosition),(null===(t=n.parentNode)||void 0===t?void 0:t.parentNode)===e&&e.removeChild(n.parentNode)}),300)}),r)}};function H(e){const t={},n=e.currentTarget;if(n&&n._ripple&&!n._ripple.touched&&!e.rippleStop){if(e.rippleStop=!0,A(e))n._ripple.touched=!0,n._ripple.isTouch=!0;else if(n._ripple.isTouch)return;if(t.center=n._ripple.centered||M(e),n._ripple.class&&(t.class=n._ripple.class),A(e)){if(n._ripple.showTimerCommit)return;n._ripple.showTimerCommit=()=>{O.show(e,n,t)},n._ripple.showTimer=window.setTimeout((()=>{n&&n._ripple&&n._ripple.showTimerCommit&&(n._ripple.showTimerCommit(),n._ripple.showTimerCommit=null)}),80)}else O.show(e,n,t)}}function N(e){const t=e.currentTarget;if(t&&t._ripple)if(window.clearTimeout(t._ripple.showTimer),"touchend"===e.type&&t._ripple.showTimerCommit){t._ripple.showTimerCommit(),t._ripple.showTimerCommit=null;t._ripple.showTimer=setTimeout((()=>N(e)))}else window.setTimeout((()=>{t._ripple&&(t._ripple.touched=!1)})),O.hide(t)}function B(e){const t=e.currentTarget;t&&t._ripple&&(t._ripple.showTimerCommit&&(t._ripple.showTimerCommit=null),window.clearTimeout(t._ripple.showTimer))}function I(e){const t=e.currentTarget;t.keyboardRipple||e.keyCode!==r.enter&&e.keyCode!==r.space||(t.keyboardRipple=!0,H(e))}function Y(e){e.currentTarget.keyboardRipple=!1,N(e)}function W(e){const t=e.currentTarget;!0===t.keyboardRipple&&(t.keyboardRipple=!1,N(e))}function P(e,t,n){let o=!1;t?o=!0:O.hide(e);const r=t||{};e._ripple=e._ripple||{},e._ripple.enabled=o,e._ripple=Object.assign(Object.assign({},e._ripple),{centered:r.center,class:r.class,circle:r.circle}),o&&!n?(e.addEventListener("touchstart",H,{passive:!0}),e.addEventListener("touchend",N,{passive:!0}),e.addEventListener("touchmove",B,{passive:!0}),e.addEventListener("touchcancel",N),e.addEventListener("mousedown",H),e.addEventListener("mouseup",N),e.addEventListener("mouseleave",N),e.addEventListener("keydown",I),e.addEventListener("keyup",Y),e.addEventListener("blur",W),e.addEventListener("dragstart",N,{passive:!0})):!o&&n&&X(e)}function X(e){e.removeEventListener("mousedown",H),e.removeEventListener("touchstart",H),e.removeEventListener("touchend",N),e.removeEventListener("touchmove",B),e.removeEventListener("touchcancel",N),e.removeEventListener("mouseup",N),e.removeEventListener("mouseleave",N),e.removeEventListener("keydown",I),e.removeEventListener("keyup",Y),e.removeEventListener("dragstart",N),e.removeEventListener("blur",W),e._ripple.enabled=!1}function D(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return 0;const t=+window.getComputedStyle(e).getPropertyValue("z-index");return t||D(e.parentNode)}function R(e){var t={};t.offsetTop=e.offsetTop||0,t.offsetLeft=e.offsetLeft||0,t.scrollHeight=e.scrollHeight||0,t.scrollWidth=e.scrollWidth||0,t.scrollLeft=e.scrollLeft||0,t.scrollTop=e.scrollTop||0,t.clientTop=e.clientTop||0,t.clientLeft=e.clientLeft||0,t.clientHeight=e.clientHeight||0,t.clientWidth=e.clientWidth||0;var n=function(e){var t=new Object;if(t.x=0,t.y=0,null!==e&&e.getBoundingClientRect){var n=document.documentElement,o=e.getBoundingClientRect(),r=n.scrollLeft,i=n.scrollTop;t.offsetWidth=o.width,t.offsetHeight=o.height,t.relativeTop=o.top,t.relativeBottom=o.bottom,t.relativeLeft=o.left,t.relativeRight=o.right,t.absoluteLeft=o.left+r,t.absoluteTop=o.top+i}return t}(e);return t.offsetWidth=Math.round(n.offsetWidth)||0,t.offsetHeight=Math.round(n.offsetHeight)||0,t.relativeTop=Math.round(n.relativeTop)||0,t.relativeBottom=Math.round(n.relativeBottom)||0,t.relativeLeft=Math.round(n.relativeLeft)||0,t.relativeRight=Math.round(n.relativeRight)||0,t.absoluteLeft=Math.round(n.absoluteLeft)||0,t.absoluteTop=Math.round(n.absoluteTop)||0,t}window.onload=function(){var e;S(),e="pastewithdata",Blazor&&Blazor.registerCustomEventType(e,{browserEventName:"paste",createEventArgs:e=>({type:e.type,pastedData:e.clipboardData.getData("text")})}),function(){const e=new MutationObserver(((e,n)=>{for(const n of e){if("childList"===n.type&&n.addedNodes.length>0)for(const e of Array.from(n.addedNodes))e instanceof HTMLElement&&e.nodeType===Node.ELEMENT_NODE&&e.hasAttribute("ripple")&&!e._ripple&&P(e,t(e),!1);if("attributes"===n.type){const e=n.target;e.hasAttribute("ripple")&&!e._ripple&&("ripple"===n.attributeName?P(e,t(e),!1):!e.hasAttribute("ripple")&&e._ripple&&(X(e),delete e._ripple))}if("attributes"===n.type&&"ripple"===n.attributeName){const e=n.target;e._ripple&&P(e,t(e),e._ripple.enabled)}if("childList"===n.type&&n.removedNodes.length>0)for(const e of Array.from(n.removedNodes))e instanceof HTMLElement&&e.nodeType===Node.ELEMENT_NODE&&e._ripple&&(X(e),delete e._ripple)}}));function t(e){const t=e.getAttribute("ripple");if("string"!=typeof t&&!t||"false"===t)return null;const n={};return t.split("&").forEach((e=>{"center"===e?n.center=!0:"circle"===e?n.circle=!0:n.class=e.trim()})),n}const n=Array.from(document.querySelectorAll("[ripple]"));for(const e of n)P(e,t(e),!1);e.observe(document,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["ripple"],attributeOldValue:!1})}()};var q=1,z={};function $(){return document.activeElement.getAttribute("id")||""}function j(e=[],t=[]){const n={};return e&&(e.forEach((e=>n[e]=window[e])),n.pageYOffset=U()),t&&t.forEach((e=>n[e]=document.documentElement[e])),n}function F(e){return"HTML"!==e.tagName&&"BODY"!==e.tagName&&1==e.nodeType}function K(e=[],t){const n=[D(s(t))],o=[...Array.from(document.getElementsByClassName("m-menu__content--active")),...Array.from(document.getElementsByClassName("m-dialog__content--active"))];for(let t=0;t<o.length;t++)e.includes(o[t])||n.push(D(o[t]));return Math.max(...n)}function V(e,t,n,o,r,i){if(!r){var l=document.querySelector(i);o.nodeType&&l.appendChild(o)}var s,c={activator:{},content:{},relativeYOffset:0,offsetParentLeft:0};if(e){var u=a(s=t)||document.querySelector(s);c.activator=G(u,n),c.activator.offsetLeft=u.offsetLeft,c.activator.offsetTop=n?0:u.offsetTop}return function(e,t){if(!t||!t.style||"none"!==t.style.display)return void e();t.style.display="inline-block",e(),t.style.display="none"}((()=>{if(o){if(o.offsetParent){const t=Z(o.offsetParent);c.relativeYOffset=U()+t.top,e?(c.activator.top-=c.relativeYOffset,c.activator.left-=window.pageXOffset+t.left):c.offsetParentLeft=t.left}c.content=G(o,n),c.content.offsetLeft=o.offsetLeft,c.content.offsetTop=o.offsetTop}}),o),c}function U(){let e=window.pageYOffset;const t=parseInt(document.documentElement.style.getPropertyValue("--m-body-scroll-y"));return t&&(e+=Math.abs(t)),e}function G(e,t){if(!e)return{};const n=Z(e);if(!t){const t=window.getComputedStyle(e);n.left=parseInt(t.marginLeft),n.top=parseInt(t.marginTop)}const o=window.innerHeight||document.documentElement.clientHeight;return Object.assign(Object.assign({},n),{maxHeight:o})}function Z(e){if(!e||!e.nodeType)return null;const t=e.getBoundingClientRect();return{top:Math.round(t.top),left:Math.round(t.left),bottom:Math.round(t.bottom),right:Math.round(t.right),width:Math.round(t.width),height:Math.round(t.height)}}function J(e,t,n,o){e.preventDefault();const r=e.key;if("ArrowLeft"===r||"Backspace"===r){if("Backspace"===r){const e={type:r,index:t,value:""};o&&o.invokeMethodAsync("Invoke",e)}Q(t-1,n)}else"ArrowRight"===r&&Q(t+1,n)}function Q(e,t){if(e<0)Q(0,t);else if(e>=t.length)Q(t.length-1,t);else if(document.activeElement!==t[e]){s(t[e]).focus()}}function ee(e,t,n){const o=s(n[t]);o&&document.activeElement===o&&o.select()}function te(e,t,n,o){const r=e.target.value;if(r&&""!==r&&(Q(t+1,n),o)){const e={type:"Input",index:t,value:r};o.invokeMethodAsync("Invoke",e)}}function ne(){var e,t,n="weird_get_top_level_domain=cookie",o=document.location.hostname.split(".");for(e=o.length-1;e>=0;e--)if(t=o.slice(e).join("."),document.cookie=n+";domain=."+t+";",document.cookie.indexOf(n)>-1)return document.cookie=n.split("=")[0]+"=;domain=."+t+";expires=Thu, 01 Jan 1970 00:00:01 GMT;",t}function oe(e){e.stopPropagation()}var re=Object.freeze({__proto__:null,getZIndex:D,getDomInfo:function(e,t="body"){var n={},o=s(e);if(o)if(o.style&&"none"===o.style.display){var r=o.cloneNode(!0);r.style.display="inline-block",r.style["z-index"]=-1e3,o.parentElement.appendChild(r),n=R(r),o.parentElement.removeChild(r)}else n=R(o);return n},getParentClientWidthOrWindowInnerWidth:function(e){return e.parentElement?e.parentElement.clientWidth:window.innerWidth},triggerEvent:function(e,t,n,o){var r=s(e),i=document.createEvent(t);return i.initEvent(n),o&&i.stopPropagation(),r.dispatchEvent(i)},setProperty:function(e,t,n){s(e)[t]=n},getBoundingClientRect:function(e,t="body"){var n,o;let r=s(e);var i={};if(r&&r.getBoundingClientRect)if(r.style&&"none"===r.style.display){var l=r.cloneNode(!0);l.style.display="inline-block",l.style["z-index"]=-1e3,null===(n=document.querySelector(t))||void 0===n||n.appendChild(l),i=l.getBoundingClientRect(),null===(o=document.querySelector(t))||void 0===o||o.removeChild(l)}else i=r.getBoundingClientRect();return i},addHtmlElementEventListener:function(e,t,n,o,r){let i;if(i="window"==e?window:"document"==e?document.documentElement:document.querySelector(e),!i)return 0;var s=q;const c={};var a=e=>{var t;if((null==r?void 0:r.stopPropagation)&&e.stopPropagation(),("boolean"!=typeof e.cancelable||e.cancelable)&&(null==r?void 0:r.preventDefault)&&e.preventDefault(),(null==r?void 0:r.relatedTarget)&&(null===(t=document.querySelector(r.relatedTarget))||void 0===t?void 0:t.contains(e.relatedTarget)))return;let o={};if(d.includes(e.type))o=p(e);else if(e instanceof MouseEvent)o=f(e);else for(var i in e)"string"!=typeof e[i]&&"number"!=typeof e[i]||(o[i]=e[i]);if(e.target&&e.target!==window&&e.target!==document){o.target={};const t=e.target,n=t.getAttributeNames().find((e=>e.startsWith("_bl_")));n?(o.target.elementReferenceId=n,o.target.selector=`[${n}]`):o.target.selector=l(t),o.target.class=t.getAttribute("class")}n.invokeMethodAsync("Invoke",o)};return(null==r?void 0:r.debounce)&&r.debounce>0?c.listener=C(a,r.debounce):(null==r?void 0:r.throttle)&&r.throttle>0?c.listener=x(a,r.throttle,{trailing:!0}):c.listener=a,c.options=o,c.handle=n,c.el=i,c.type=t,z[s]?z[s].push(c):z[s]=[c],i.addEventListener(t,c.listener,c.options),q++},removeHtmlElementEventListener:function(e){var t=z[e];t&&(t.forEach((e=>{e.handle.dispose(),e.el.removeEventListener(e.type,e.listener,e.options)})),delete z[e])},addMouseleaveEventListener:function(e){var t=document.querySelector(e);t&&t.addEventListener()},contains:function(e,t){const n=s(e);return!(!n||!n.contains)&&n.contains(s(t))},equalsOrContains:function(e,t){const n=s(e),o=s(t);return!!n&&n.contains&&!!o&&(n==o||n.contains(o))},copy:function(e){navigator.clipboard?navigator.clipboard.writeText(e).then((function(){console.log("Async: Copying to clipboard was successful!")}),(function(e){console.error("Async: Could not copy text: ",e)})):function(e){var t=document.createElement("textarea");t.value=e,t.style.top="0",t.style.left="0",t.style.position="fixed",document.body.appendChild(t),t.focus(),t.select();try{var n=document.execCommand("copy")?"successful":"unsuccessful";console.log("Fallback: Copying text command was "+n)}catch(e){console.error("Fallback: Oops, unable to copy",e)}document.body.removeChild(t)}(e)},focus:function(e,t=!1){let n=s(e);n instanceof HTMLElement?n.focus({preventScroll:t}):console.error("Unable to focus an invalid element")},select:function(e){let t=s(e);if(!(t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement))throw new Error("Unable to select an invalid element");t.select()},hasFocus:function(e){let t=s(e);return document.activeElement===t},blur:function(e){s(e).blur()},log:function(e){console.log(e)},scrollIntoView:function(e,t){let n=s(e);n instanceof HTMLElement&&(null===t||null==t?n.scrollIntoView():"boolean"==typeof t?n.scrollIntoView(t):n.scrollIntoView({block:null==t.block?void 0:t.block,inline:null==t.inline?void 0:t.inline,behavior:t.behavior}))},scrollIntoParentView:function(e,t=!1,n=!1,o=1,r="smooth"){const i=s(e);if(i instanceof HTMLElement){let e=i;for(;o>0;)if(e=e.parentElement,o--,!e)return;const l={behavior:r};if(t)if(n)l.left=i.offsetLeft;else{const t=i.offsetLeft-e.offsetLeft;t-e.scrollLeft<0?l.left=t:t+i.offsetWidth-e.scrollLeft>e.offsetWidth&&(l.left=t+i.offsetWidth-e.offsetWidth)}else if(n)l.top=i.offsetTop;else{const t=i.offsetTop-e.offsetTop;t-e.scrollTop<0?l.top=t:t+i.offsetHeight-e.scrollTop>e.offsetHeight&&(l.top=t+i.offsetHeight-e.offsetHeight)}(l.left||l.top)&&e.scrollTo(l)}},scrollTo:function(e,t){let n=s(e);if(n instanceof HTMLElement){const e={left:null===t.left?void 0:t.left,top:null===t.top?void 0:t.top,behavior:t.behavior};n.scrollTo(e)}else n===window&&window.scrollTo(t)},scrollToTarget:function(e,t=null,n=0,o="smooth"){const r=document.querySelector(e);if(r){let e;e=t?r.offsetTop:r.getBoundingClientRect().top+window.scrollY;(t?document.querySelector(t):document.documentElement).scrollTo({top:e-n,behavior:o})}},scrollToElement:function(e,t,n){const o=s(e);if(!o)return;const r=o.getBoundingClientRect().top+window.pageYOffset-t;window.scrollTo({top:r,behavior:n})},scrollToActiveElement:function(e,t=".active",n="center",o=!1){let r,i=s(e);if("string"==typeof t&&(r=e.querySelector(t)),!i||!r)return;const l="center"===n?r.offsetTop-i.offsetHeight/2+r.offsetHeight/2:r.offsetTop-n;i.scrollTo({top:l,behavior:o?"smooth":"auto"})},addClsToFirstChild:function(e,t){var n=s(e);n.firstElementChild&&n.firstElementChild.classList.add(t)},removeClsFromFirstChild:function(e,t){var n=s(e);n.firstElementChild&&n.firstElementChild.classList.remove(t)},getAbsoluteTop:function e(t){var n=t.offsetTop;return null!=t.offsetParent&&(n+=e(t.offsetParent)),n},getAbsoluteLeft:function e(t){var n=t.offsetLeft;return null!=t.offsetParent&&(n+=e(t.offsetParent)),n},addElementToBody:function(e){document.body.appendChild(e)},delElementFromBody:function(e){document.body.removeChild(e)},addElementTo:function(e,t){let n=s(t);n&&e&&n.appendChild(e)},delElementFrom:function(e,t){let n=s(t);n&&e&&n.removeChild(e)},getActiveElement:$,focusDialog:function e(t,n=0){let o=document.querySelector(t);o&&!o.hasAttribute("disabled")&&setTimeout((()=>{o.focus(),"#"+$()!==t&&n<10&&e(t,n+1)}),10)},getWindow:function(){return{innerWidth:window.innerWidth,innerHeight:window.innerHeight,pageXOffset:window.pageXOffset,pageYOffset:window.pageYOffset,isTop:0==window.scrollY,isBottom:window.scrollY+window.innerHeight==document.body.clientHeight}},getWindowAndDocumentProps:j,css:function(e,t,n=null){var o=s(e);if("string"==typeof t)o.style[t]=n;else for(let e in t)t.hasOwnProperty(e)&&(o.style[e]=t[e])},addCls:function(e,t){let n=s(e);"string"==typeof t?n.classList.add(t):n.classList.add(...t)},removeCls:function(e,t){let n=s(e);"string"==typeof t?n.classList.remove(t):n.classList.remove(...t)},elementScrollIntoView:function(e){let t=s(e);t&&t.scrollIntoView({behavior:"smooth",block:"nearest",inline:"start"})},getScroll:function(){return{x:window.pageXOffset,y:window.pageYOffset}},getScrollParent:function(e,t=void 0){null!=t||(t=c?window:void 0);let n=e;for(;n&&n!==t&&F(n);){const{overflowY:e}=window.getComputedStyle(n);if(/scroll|auto|overlay/i.test(e))return n;n=n.parentNode}return t},getScrollTop:function(e){const t="scrollTop"in e?e.scrollTop:e.pageYOffset;return Math.max(t,0)},getInnerText:function(e){return s(e).innerText},getMenuOrDialogMaxZIndex:K,getMaxZIndex:function(){return Array.from(document.all).reduce(((e,t)=>Math.max(e,+window.getComputedStyle(t).zIndex||0)),0)},getStyle:function(e,t){return(e=s(e)).currentStyle?e.currentStyle[t]:window.getComputedStyle?document.defaultView.getComputedStyle(e,null).getPropertyValue(t):void 0},getTextAreaInfo:function(e){var t={},n=s(e);return t.scrollHeight=n.scrollHeight||0,e.currentStyle?(t.lineHeight=parseFloat(e.currentStyle["line-height"]),t.paddingTop=parseFloat(e.currentStyle["padding-top"]),t.paddingBottom=parseFloat(e.currentStyle["padding-bottom"]),t.borderBottom=parseFloat(e.currentStyle["border-bottom"]),t.borderTop=parseFloat(e.currentStyle["border-top"])):window.getComputedStyle&&(t.lineHeight=parseFloat(document.defaultView.getComputedStyle(e,null).getPropertyValue("line-height")),t.paddingTop=parseFloat(document.defaultView.getComputedStyle(e,null).getPropertyValue("padding-top")),t.paddingBottom=parseFloat(document.defaultView.getComputedStyle(e,null).getPropertyValue("padding-bottom")),t.borderBottom=parseFloat(document.defaultView.getComputedStyle(e,null).getPropertyValue("border-bottom")),t.borderTop=parseFloat(document.defaultView.getComputedStyle(e,null).getPropertyValue("border-top"))),Object.is(NaN,t.borderTop)&&(t.borderTop=1),Object.is(NaN,t.borderBottom)&&(t.borderBottom=1),t},disposeObj:function(e){},upsertThemeStyle:function(e,t){const n=document.getElementById(e);n&&document.head.removeChild(n);const o=document.createElement("style");o.id=e,o.type="text/css",o.innerHTML=t,document.head.insertAdjacentElement("beforeend",o)},getImageDimensions:function(e){return new Promise((function(t,n){var o=new Image;o.src=e,o.onload=function(){t({width:o.width,height:o.height,hasError:!1})},o.onerror=function(){t({width:0,height:0,hasError:!0})}}))},preventDefaultForSpecificKeys:function(e,t){const n=s(e);n&&n.addEventListener("keydown",(e=>{t.includes(e.code)&&e.preventDefault()}))},getBoundingClientRects:function(e){for(var t=document.querySelectorAll(e),n=[],o=0;o<t.length;o++){var r=t[o],i={id:r.id,rect:r.getBoundingClientRect()};n.push(i)}return n},getSize:function(e,t){var n=s(e),o=n.style.display,r=n.style.overflow;n.style.display="",n.style.overflow="hidden";var i=n["offset"+t.charAt(0).toUpperCase()+t.slice(1)]||0;return n.style.display=o,n.style.overflow=r,i},getProp:function(e,t){if("window"===e)return window[t];var n=s(e);return n?n[t]:null},updateWindowTransition:function(e,t,n){var o=s(e),r=o.querySelector(".m-window__container");if(n){var i=s(n);r.style.height=i.clientHeight+"px"}else t?(r.classList.add("m-window__container--is-active"),r.style.height=o.clientHeight+"px"):(r.style.height="",r.classList.remove("m-window__container--is-active"))},getScrollHeightWithoutHeight:function(e){var t=s(e);if(!t)return 0;var n=t.style.height;t.style.height="0";var o=t.scrollHeight;return t.style.height=n,o},registerTextFieldOnMouseDown:function(e,t,n){if(!e||!t)return;const o=e=>{if(e.target!==s(t)&&(e.preventDefault(),e.stopPropagation()),n){const t={Detail:e.detail,ScreenX:e.screenX,ScreenY:e.screenY,ClientX:e.clientX,ClientY:e.clientY,OffsetX:e.offsetX,OffsetY:e.offsetY,PageX:e.pageX,PageY:e.pageY,Button:e.button,Buttons:e.buttons,CtrlKey:e.ctrlKey,ShiftKey:e.shiftKey,AltKey:e.altKey,MetaKey:e.metaKey,Type:e.type};n.invokeMethodAsync("Invoke",t)}};e.addEventListener("mousedown",o);const r={listener:o,handle:n},l=`registerTextFieldOnMouseDown_${i(e)}`;z[l]=[r]},unregisterTextFieldOnMouseDown:function(e){const t=`registerTextFieldOnMouseDown_${i(e)}`,n=z[t];n&&n.length&&n.forEach((t=>{t.handle.dispose(),e&&e.removeEventListener("mousedown",t.listener)}))},containsActiveElement:function(e){var t=s(e);return t&&t.contains?t.contains(document.activeElement):null},copyChild:function(e){"string"==typeof e&&(e=document.querySelector(e)),e&&(e.setAttribute("contenteditable","true"),e.focus(),document.execCommand("selectAll",!1,null),document.execCommand("copy"),document.execCommand("unselect"),e.blur(),e.removeAttribute("contenteditable"))},copyText:function(e){if(navigator.clipboard)navigator.clipboard.writeText(e).then((function(){console.log("Async: Copying to clipboard was successful!")}),(function(e){console.error("Async: Could not copy text: ",e)}));else{var t=document.createElement("textarea");t.value=e,t.readOnly=!0,t.style.top="0",t.style.left="0",t.style.position="fixed",document.body.appendChild(t),t.focus(),t.select();try{var n=document.execCommand("copy")?"successful":"unsuccessful";console.log("Fallback: Copying text command was "+n)}catch(e){console.error("Fallback: Oops, unable to copy",e)}document.body.removeChild(t)}},getMenuableDimensions:V,invokeMultipleMethod:function(e,t,n,o,r,i,l,s,c){var a={windowAndDocument:null,dimensions:null,zIndex:0};return a.windowAndDocument=j(e,t),a.dimensions=V(n,o,r,i,l,s),a.zIndex=K([i],c),a},registerOTPInputOnInputEvent:function(e,t){for(let n=0;n<e.length;n++){const o=o=>te(o,n,e,t),r=t=>ee(t,n,e),i=o=>J(o,n,e,t);e[n].addEventListener("input",o),e[n].addEventListener("focus",r),e[n].addEventListener("keyup",i),e[n]._optInput={inputListener:o,focusListener:r,keyupListener:i}}},unregisterOTPInputOnInputEvent:function(e){for(let t=0;t<e.length;t++){const n=e[t];n&&n._optInput&&(n.removeEventListener("input",n._optInput.inputListener),n.removeEventListener("focus",n._optInput.focusListener),n.removeEventListener("keyup",n._optInput.keyupListener))}},getListIndexWhereAttributeExists:function(e,t,n){const o=document.querySelectorAll(e);if(!o)return-1;let r=-1;for(let e=0;e<o.length;e++)if(o[e].getAttribute(t)===n){r=e;break}return r},scrollToTile:function(e,t,n,o){var r=document.querySelectorAll(t);if(!r)return;let i=r[n];if(!i)return;const l=document.querySelector(e);if(!l)return;const s=l.scrollTop,c=l.clientHeight;s>i.offsetTop-8?l.scrollTo({top:i.offsetTop-i.clientHeight,behavior:"smooth"}):s+c<i.offsetTop+i.clientHeight+8&&l.scrollTo({top:i.offsetTop-c+2*i.clientHeight,behavior:"smooth"})},getElementTranslateY:function(e){const t=window.getComputedStyle(e),n=t.transform||t.webkitTransform,o=n.slice(7,n.length-1).split(", ")[5];return Number(o)},get_top_domain:ne,setCookie:function(e,t){if(null!=t){var n=ne();n?isNaN(n[0])&&(n=`.${n}`):n="";var o=new Date;o.setTime(o.getTime()+2592e6),document.cookie=`${e}=${escape(null==t?void 0:t.toString())};path=/;expires=${o.toUTCString()};domain=${n}`}},getCookie:function(e){const t=new RegExp(`(^| )${e}=([^;]*)(;|$)`),n=document.cookie.match(t);return n?unescape(n[2]):null},registerDragEvent:function(e,t){if(e){const n=i(e),o=e=>{if(t){const n=e.target.getAttribute(t);e.dataTransfer.setData(t,n),e.dataTransfer.setData("offsetX",e.offsetX.toString()),e.dataTransfer.setData("offsetY",e.offsetY.toString())}};z[`${n}:dragstart`]=[{listener:o}],e.addEventListener("dragstart",o)}},unregisterDragEvent:function(e){const t=i(e);if(t){const n=`${t}:dragstart`;z[n]&&z[n].forEach((t=>{e.removeEventListener("dragstart",t.listener)}))}},resizableDataTable:function(e){const t=e.querySelector("table"),n=t.querySelector(".m-data-table-header").getElementsByTagName("tr")[0],o=n?Array.from(n.children):[];if(!o)return;const r=t.offsetHeight,i=[];for(var l=0;l<[...o].length;l++){const e=o[l],t=e.querySelector(".m-data-table-header__col-resize");if(!t){o.splice(l,1);continue}t.style.height=r+"px";let n=e.firstElementChild.offsetWidth;n=n+32+18+1+1,e.style.minWidth||(e.minWidth=n,e.style.minWidth=n+"px"),i.push(s(t))}return{un:()=>{i.forEach((e=>e()))}};function s(n){let r,i,l,s,a,u;const d=e=>e.stopPropagation(),f=e=>{i=e.target.parentElement,l=i.nextElementSibling,r=e.pageX,u=t.offsetWidth;var n=function(e){if("border-box"==c(e,"box-sizing"))return 0;var t=c(e,"padding-left"),n=c(e,"padding-right");return parseInt(t)+parseInt(n)}(i);s=i.offsetWidth-n,l&&(a=l.offsetWidth-n)},p=n=>{if(i){let o=n.pageX-r;e.classList.contains("m-data-table--rtl")&&(o=0-o);let c=s+o;i.style.width=c+"px";if(e.classList.contains("m-data-table--resizable-overflow"))return void(t.style.width=u+o+"px");if(e.classList.contains("m-data-table--resizable-independent")){let e=a-o;const t=s+a;o>0?l&&e<l.minWidth&&(e=l.minWidth,c=t-e):c<i.minWidth&&(c=i.minWidth,e=t-c),i.style.width=c+"px",l&&(l.style.width=e+"px")}}},m=e=>{if(i)for(let e=0;e<o.length;e++){const t=o[e];t.style.width=t.offsetWidth+"px"}i=void 0,l=void 0,r=void 0,a=void 0,s=void 0,u=void 0};return n.addEventListener("click",d),n.addEventListener("mousedown",f),document.addEventListener("mousemove",p),document.addEventListener("mouseup",m),()=>{n.removeEventListener("click",d),n.removeEventListener("mousedown",f),document.removeEventListener("mousemove",p),document.removeEventListener("mouseup",m)}}function c(e,t){return window.getComputedStyle(e,null).getPropertyValue(t)}},updateDataTableResizeHeight:function(e){const t=e.querySelector("table"),n=t.querySelector(".m-data-table-header").getElementsByTagName("tr")[0],o=n?n.children:[];if(!o)return;const r=t.offsetHeight;for(var i=0;i<o.length;i++){o[i].querySelector(".m-data-table-header__col-resize").style.height=r+"px"}},addStopPropagationEvent:function(e,t){s(e).addEventListener(t,oe)},removeStopPropagationEvent:function(e,t){s(e).removeEventListener(t,oe)},historyBack:function(){history.back()},historyGo:function(e){history.go(e)},historyReplace:function(e){history.replaceState(null,"",e)},registerTableScrollEvent:function(e){const t=()=>{const t=e.scrollWidth,n=e.clientWidth,o=e.scrollLeft;if(t==n)return e.classList.remove("scrolling"),e.classList.remove("scrolled-to-right"),void e.classList.remove("scrolled-to-left");const r=e.parentElement.classList.contains("m-data-table--rtl");Math.abs(t-((r?-o:o)+n))<1?(e.classList.remove("scrolling"),e.classList.remove("scrolled-to-left"),e.classList.add("scrolled-to-right")):0==o?(e.classList.remove("scrolling"),e.classList.remove("scrolled-to-right"),e.classList.add("scrolled-to-left")):(e.classList.remove("scrolled-to-right"),e.classList.remove("scrolled-to-left"),e.classList.add("scrolling"))};t(),e.addEventListener("scroll",t),e._m_table_scroll_event=t},unregisterTableScrollEvent:function(e){const t=e._m_table_scroll_event;t&&(e.removeEventListener("scroll",t),delete e._m_table_scroll_event)},updateTabSlider:function(e,t,n,o,r){if(!e)return void console.warn("[MTab] the element of slider wrapper is not found");if(!t)return void(e.style.cssText="");const i=o?t.scrollHeight:n,l=o?0:t.offsetLeft,s=o?0:t.offsetLeft+t.offsetWidth,c=t.offsetTop,a=o?n:t.clientWidth;e.style.width=`${a}px`,e.style.height=`${i}px`,r||(e.style.left=`${l}px`),r&&(e.style.right=`${s}px`),o&&(e.style.top=`${c}px`)},isScrollNearBottom:function(e,t=200){return!!e&&e.scrollHeight-(e.scrollTop+e.clientHeight)<t},matchesSelector:function(e,t){if(!(n&&"undefined"!=typeof CSS&&void 0!==CSS.supports&&CSS.supports(`selector(${t})`)))return!1;try{return!!e&&e.matches(t)}catch(e){return!1}},prepareSticky:function(e,t,n,o){let r;r="window"===e?{top:0,bottom:window.innerHeight}:s(e).getBoundingClientRect();const i=t.getBoundingClientRect(),l=function(e,t,n){if(a(n)&&t.top>e.top-n)return n+t.top+"px";return}(i,r,n),c=function(e,t,n){if(a(n)&&t.bottom<e.bottom+n){return n+(window.innerHeight-t.bottom)+"px"}return}(i,r,o);return{fixedTop:l,fixedBottom:c,width:i.width,height:i.height};function a(e){return null!=e}},toggleMourningMode:function(e){document.documentElement.classList.toggle("m-mourning-mode",e)}});window.MasaBlazor={interop:Object.assign(Object.assign(Object.assign(Object.assign({},re),b),_),u),xgplayerPlugins:[]}}();
//# sourceMappingURL=masa-blazor.js.map
