@using HX.Experiment.Shared.Model
<style>
    .dialog-button.confirm {
        width: 300px;
        background: linear-gradient(0deg, #008ef7, #1e9fff);
        box-shadow: 0px 8px 16px 0px rgb(48 138 239 / 83%);
        border-radius: 28px;
        color: white;
        font-size: 2rem;
    }

    .dialog-button.cancel {
        width: 300px;
        border-radius: 28px;
        box-shadow: 0px 8px 16px 0px rgb(177 213 253 / 83%);
        font-size: 2rem;
    }

    .customer-input .m-input__prepend-outer .m-label {
        width: 140px;
        font-size: 1.25rem;
    }

    .student-dialog .m-text-field__slot {
        font-size: 1.625rem;
    }

    .student-dialog .customer-text {
        font-size: 1.625rem;
        font-weight: 400;
        position: unset !important;
        color: black;
    }
</style>

<MForm Model="StudentModel" Context="StudentDialogContext">
    <MRow>
        <MCard>
            <MCardTitle Class="text-center">
                开始检测
            </MCardTitle>
            <MCardText Class="d-flex justify-center flex-column align-center">
                <MRow Style="width: 100%;">
                    <MCol Cols="6">
                        <MLabel class="font-weight-medium" Style="font-size:1.5rem; ">
                            姓名:
                            <span>@StudentModel.Name</span>
                        </MLabel>
                    </MCol>
                    <MCol Cols="6">
                        <MLabel class="font-weight-medium" Style="font-size:1.5rem; ">
                            身份证号:
                            <span>@StudentModel.CardId</span>
                        </MLabel>
                    </MCol>
                    <MCol Cols="12" Style="width: 100%;">
                        <MDivider></MDivider>
                    </MCol>
                </MRow>
                <MCard Class="text-center d-flex flex-column justify-center "
                    Style="width: 100px; height: 100px;background-color: #4caf50; border-radius: 10px;">
                    <MLabel Class="text-h5" Style="color: white" Color="white">@(DeviceModel?.Name ?? "设备")</MLabel>
                </MCard>
                <MLabel Style="color: #0288d1; margin-top: 10px;font-size: 1.5rem">
                    请确认已佩戴设备并且显示以上内容
                </MLabel>
            </MCardText>
            <MCardActions Class="text-center d-flex justify-center">
                <MButton OnClick="HandleSubmit" Color="#04B55C"
                    Style="width: 200px;color: white; font-size:1.5rem; border-radius: 5px;">
                    已佩戴,开始检测
                </MButton>
            </MCardActions>
        </MCard>
    </MRow>
    <MRow Justify="JustifyTypes.Center">
        <MCol Cols="4">
            <MButton Height="56" Class="dialog-button cancel" Color="#fff" OnClick="CancelAsync">取消</MButton>
        </MCol>
        <MCol Cols="4">
            <MButton Height="56" Class="dialog-button confirm" OnClick="SaveAsync">
                确认
            </MButton>
        </MCol>
    </MRow>
</MForm>
