---
trigger: manual
description: 
globs: 
---

# HX.Experiment Project Analysis Plan

## Notes
- This is a project analysis plan for the HX.Experiment project.
- It outlines the main components and functionality of the project.
- 项目架构是:
    - HX.Experiment.MAUI.Shared: 共享组件库。
    - HX.Experiment.MAUI.Web: 前端应用。
    - HX.Experiment.MAUI.Web.Client: 前端应用客户端。
    - HX.Experiment.MAUI.Web.Shared: 共享组件。
- 使用MASA的组件 尽量少写CSS样式


## Project Structure
这个项目的目标是教学产品系统设计,主要包括以下几个部分:

- HX.Experiment.MAUI.Shared：共享组件库，包含通用页面、Tabs、布局、模型和服务，供各端复用。
- HX.Experiment.MAUI：MAUI 跨平台主应用，支持 Android/iOS/Windows，集成 Shared 组件，负责页面导航与业务逻辑。
- HX.Experiment.MAUI.Web：基于 Blazor 的 Web 前端应用，集成 Shared 组件，负责 Web 端交互与展示。
- HX.Experiment.MAUI.Web.Client：Web 客户端应用，主要用于 WebAssembly/前端资源，复用 Shared 组件。

主要页面与模块（部分）：
- 实验管理（ExperimentManager）
- 班级管理（ClassManager）
- 实验监控（Monitor）
- 学生实验（StudentExperiment）
- 学生管理（StudentManager）
- 任务详情（TaskDetail）


所有页面均基于 MASA.Blazor 组件库，尽量减少自定义 CSS，UI 统一风格。

## interface Library

- MASA.Blazor :https://github.com/masastack/MASA.Blazor
- MASA Documentation :https://docs.masastack.com/blazor/getting-started/installation

## Task List
- [x] 项目架构搭建
- [x] 前端应用搭建
- [x] 前端应用客户端搭建
- [x] 共享组件搭建
- 页面UI与逻辑
- [ ] 前端应用客户端UI与逻辑

## Current Goal