function e(e,t,i,n){return new(i||(i=Promise))((function(o,s){function a(e){try{l(n.next(e))}catch(e){s(e)}}function r(e){try{l(n.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?o(e.value):(t=e.value,t instanceof i?t:new i((function(e){e(t)}))).then(a,r)}l((n=n.apply(e,t||[])).next())}))}"function"==typeof SuppressedError&&SuppressedError;class t{constructor(e,t){this.getOriginInstance=()=>this.instance,this.getCenter=()=>this.instance.getCenter(),this.setZoom=e=>this.instance.setZoom(e),this.getZoom=()=>this.instance.getZoom(),this.setMaxZoom=e=>this.instance.setMaxZoom(e),this.setMinZoom=e=>this.instance.setMinZoom(e),this.enableScrollWheelZoom=()=>this.instance.enableScrollWheelZoom(),this.disableScrollWheelZoom=()=>this.instance.disableScrollWheelZoom(),this.setMapType=e=>this.instance.setMapType(e),this.getMapType=()=>this.instance.getMapType(),this.setTrafficOn=()=>this.instance.setTrafficOn(),this.setTrafficOff=()=>this.instance.setTrafficOff(),this.setMapStyleV2=e=>this.instance.setMapStyleV2(e),this.panTo=e=>this.instance.panTo(e),this.addOverlay=e=>this.instance.addOverlay(e),this.removeOverlay=e=>this.instance.removeOverlay(e),this.clearOverlays=()=>this.instance.clearOverlays(),this.instance=new BMapGL.Map(e),t.enableScrollWheelZoom&&this.instance.enableScrollWheelZoom(),this.instance.setMaxZoom(t.maxZoom),this.instance.setMinZoom(t.minZoom),this.instance.centerAndZoom(t.center,t.zoom),this.instance.setMapType(t.mapTypeString),t.trafficOn&&this.instance.setTrafficOn(),t.dark&&this.instance.setMapStyleV2({styleId:t.darkThemeId})}setDotNetObjectReference(t,i){this.dotNetHelper=t,i.forEach((i=>{this.instance.addEventListener(i,(function(n){return e(this,void 0,void 0,(function*(){"dragstart"==i||"dragging"==i||"dragend"==i||"dblclick"==i?yield t.invokeMethodAsync("OnEvent",i,{latlng:n.point,pixel:n.pixel}):"click"==i||"rightclick"==i||"rightdblclick"==i||"mousemove"==i?yield t.invokeMethodAsync("OnEvent",i,{latlng:n.latlng,pixel:n.pixel}):yield t.invokeMethodAsync("OnEvent",i,null)}))}))}))}addCircle(e){var t=new BMapGL.Circle(e.center,e.radius,{strokeColor:e.strokeColor,strokeWeight:e.strokeWeight,strokeOpacity:e.strokeOpacity,strokeStyle:0==e.strokeStyle?"solid":"dashed",fillColor:e.fillColor,fillOpacity:e.fillOpacity});return this.instance.addOverlay(t),t}addMarker(e){var t=new BMapGL.Marker(e.point,{offset:e.offset,rotation:e.rotation,title:e.title});if(e.icon&&e.icon.url){const{url:i,size:n,options:o}=e.icon,s={};o&&(o.anchor&&(s.anchor=new BMapGL.Size(o.anchor.width,o.anchor.height)),o.imageOffset&&(s.imageOffset=new BMapGL.Size(o.imageOffset.width,o.imageOffset.height))),t.setIcon(new BMapGL.Icon(i,new BMapGL.Size(n.width,n.height),s))}return this.instance.addOverlay(t),t}addLabel(e){var t=new BMapGL.Label(e.content,{offset:e.offset,position:e.position});return this.instance.addOverlay(t),t}addPolyline(e){if(null==e.points)return null;var t=new BMapGL.Polyline(e.points,{strokeColor:e.strokeColor,strokeWeight:e.strokeWeight,strokeOpacity:e.strokeOpacity,strokeStyle:0==e.strokeStyle?"solid":"dashed",geodesic:e.geodesic,clip:e.clip});return this.instance.addOverlay(t),t}addPolygon(e){if(null==e.points)return null;var t=[];e.points.forEach((e=>{t.push(i(e))}));var n=new BMapGL.Polygon(t,{strokeColor:e.strokeColor,strokeWeight:e.strokeWeight,strokeOpacity:e.strokeOpacity,strokeStyle:0==e.strokeStyle?"solid":"dashed",fillColor:e.fillColor,fillOpacity:e.fillOpacity});return this.instance.addOverlay(n),n}contains(e){var t=this.instance.getOverlays();for(let i=0;i<t.length;i++)if(t[i]===e)return!0;return!1}destroyMap(){null!=this.instance&&delete this.instance}}"undefined"!=typeof BMapGL?(BMapGL.Polygon.prototype.setPathWithGeoPoint=(e,t)=>{if(null!=e){var n=[];e.forEach((e=>{n.push(i(e))})),t.setPath(n)}},console.info("BMapGL is loaded")):console.error("BMapGL is not defined");const i=e=>new BMapGL.Point(e.lng,e.lat),n=(e,i)=>e&&document.getElementById(e)?new t(e,i):null;export{n as init};
//# sourceMappingURL=baidumap.js.map
