using HX.Experiment.Shared.Model;
using HX.Experiment.Shared.Services;
using OfficeOpenXml;
using UFU.CoreFX.Models;
using UFU.CoreFX.Utils;

namespace HX.Experiment.Web.Service.Services;

/// <summary>
/// Excel导入学生服务实现
/// </summary>
public class ImportStudentService : IImportStudentService
{
    private readonly IServiceProvider _serviceProvider;
    
    public ImportStudentService(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        // 设置EPPlus许可证上下文
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
    }

    /// <summary>
    /// 从Excel文件导入学生信息
    /// </summary>
    public async Task<ImportResult> ImportStudentsFromExcelAsync(Stream fileStream, string fileName, string classId)
    {
        var result = new ImportResult();
        
        try
        {
            // 验证文件格式
            var validationResult = await ValidateExcelFormatAsync(fileStream, fileName);
            if (!validationResult.IsValid)
            {
                result.Success = false;
                result.ErrorMessages.AddRange(validationResult.ErrorMessages);
                return result;
            }

            // 重置流位置
            fileStream.Position = 0;
            
            using var package = new ExcelPackage(fileStream);
            var worksheet = package.Workbook.Worksheets[0];
            
            var students = new List<DataModel<StudentInfo>>();
            var failedStudents = new List<FailedStudentInfo>();
            
            // 从第2行开始读取数据（第1行是标题）
            for (int row = 2; row <= worksheet.Dimension.End.Row; row++)
            {
                try
                {
                    var name = worksheet.Cells[row, 1].Text?.Trim();
                    var cardId = worksheet.Cells[row, 2].Text?.Trim();
                    var userId = worksheet.Cells[row, 3].Text?.Trim();
                    
                    // 验证必填字段
                    if (string.IsNullOrEmpty(name))
                    {
                        failedStudents.Add(new FailedStudentInfo
                        {
                            RowNumber = row,
                            Name = name ?? "",
                            CardId = cardId ?? "",
                            ErrorReason = "姓名不能为空"
                        });
                        continue;
                    }
                    
                    if (string.IsNullOrEmpty(cardId))
                    {
                        failedStudents.Add(new FailedStudentInfo
                        {
                            RowNumber = row,
                            Name = name,
                            CardId = cardId ?? "",
                            ErrorReason = "学号/身份证号不能为空"
                        });
                        continue;
                    }
                    
                    // 检查是否已存在相同学号的学生
                    if (await IsStudentExistsAsync(cardId, classId))
                    {
                        failedStudents.Add(new FailedStudentInfo
                        {
                            RowNumber = row,
                            Name = name,
                            CardId = cardId,
                            ErrorReason = "该学号在当前班级中已存在"
                        });
                        continue;
                    }
                    
                    // 创建学生信息
                    var studentInfo = new StudentInfo
                    {
                        Name = name,
                        CardId = cardId,
                        ClassId = classId,
                        UserId = userId
                    };
                    
                    var dataModel = new DataModel<StudentInfo>
                    {
                        Data = studentInfo,
                        AddTime = DateTime.Now,
                        UpdateTime = DateTime.Now,
                        Version = Guid.NewGuid().ToString()
                    };
                    
                    students.Add(dataModel);
                }
                catch (Exception ex)
                {
                    failedStudents.Add(new FailedStudentInfo
                    {
                        RowNumber = row,
                        Name = worksheet.Cells[row, 1].Text?.Trim() ?? "",
                        CardId = worksheet.Cells[row, 2].Text?.Trim() ?? "",
                        ErrorReason = $"处理数据时发生错误: {ex.Message}"
                    });
                }
            }
            
            // 批量保存成功的学生信息
            if (students.Any())
            {
                var savedStudents = await SaveStudentsAsync(students);
                result.ImportedStudents.AddRange(savedStudents);
                result.SuccessCount = savedStudents.Count;
            }
            
            result.FailedStudents.AddRange(failedStudents);
            result.FailureCount = failedStudents.Count;
            result.Success = result.SuccessCount > 0;
            
            if (result.FailureCount > 0)
            {
                result.ErrorMessages.Add($"共有 {result.FailureCount} 条记录导入失败");
            }
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessages.Add($"导入过程中发生错误: {ex.Message}");
        }
        
        return result;
    }

    /// <summary>
    /// 验证Excel文件格式
    /// </summary>
    public async Task<ValidationResult> ValidateExcelFormatAsync(Stream fileStream, string fileName)
    {
        var result = new ValidationResult { IsValid = true };
        
        try
        {
            // 验证文件扩展名
            var extension = Path.GetExtension(fileName).ToLower();
            if (extension != ".xlsx" && extension != ".xls")
            {
                result.IsValid = false;
                result.ErrorMessages.Add("文件格式不正确，请上传Excel文件（.xlsx或.xls）");
                return result;
            }
            
            using var package = new ExcelPackage(fileStream);
            
            if (package.Workbook.Worksheets.Count == 0)
            {
                result.IsValid = false;
                result.ErrorMessages.Add("Excel文件中没有工作表");
                return result;
            }
            
            var worksheet = package.Workbook.Worksheets[0];
            
            if (worksheet.Dimension == null || worksheet.Dimension.End.Row < 2)
            {
                result.IsValid = false;
                result.ErrorMessages.Add("Excel文件中没有数据行");
                return result;
            }
            
            // 验证标题行
            var expectedHeaders = new[] { "姓名", "学号/身份证号", "用户ID" };
            for (int col = 1; col <= expectedHeaders.Length; col++)
            {
                var headerValue = worksheet.Cells[1, col].Text?.Trim();
                if (headerValue != expectedHeaders[col - 1])
                {
                    result.IsValid = false;
                    result.ErrorMessages.Add($"第{col}列标题应为'{expectedHeaders[col - 1]}'，实际为'{headerValue}'");
                }
            }
            
            // 预览前10行数据
            var previewCount = Math.Min(10, worksheet.Dimension.End.Row - 1);
            for (int row = 2; row <= previewCount + 1; row++)
            {
                var name = worksheet.Cells[row, 1].Text?.Trim();
                var cardId = worksheet.Cells[row, 2].Text?.Trim();
                var userId = worksheet.Cells[row, 3].Text?.Trim();
                
                result.PreviewStudents.Add(new StudentPreviewInfo
                {
                    RowNumber = row,
                    Name = name ?? "",
                    CardId = cardId ?? "",
                    UserId = userId ?? ""
                });
            }
        }
        catch (Exception ex)
        {
            result.IsValid = false;
            result.ErrorMessages.Add($"读取Excel文件时发生错误: {ex.Message}");
        }
        
        return result;
    }

    /// <summary>
    /// 获取Excel模板文件
    /// </summary>
    public async Task<Stream> GetExcelTemplateAsync()
    {
        var stream = new MemoryStream();
        
        using (var package = new ExcelPackage(stream))
        {
            var worksheet = package.Workbook.Worksheets.Add("学生信息模板");
            
            // 设置标题行
            worksheet.Cells[1, 1].Value = "姓名";
            worksheet.Cells[1, 2].Value = "学号/身份证号";
            worksheet.Cells[1, 3].Value = "用户ID";
            
            // 设置示例数据
            worksheet.Cells[2, 1].Value = "张三";
            worksheet.Cells[2, 2].Value = "2023001";
            worksheet.Cells[2, 3].Value = "user001";
            
            worksheet.Cells[3, 1].Value = "李四";
            worksheet.Cells[3, 2].Value = "2023002";
            worksheet.Cells[3, 3].Value = "user002";
            
            // 设置列宽
            worksheet.Column(1).Width = 15;
            worksheet.Column(2).Width = 20;
            worksheet.Column(3).Width = 15;
            
            // 设置标题行样式
            using (var range = worksheet.Cells[1, 1, 1, 3])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            }
            
            await package.SaveAsync();
        }
        
        stream.Position = 0;
        return stream;
    }

    /// <summary>
    /// 检查学生是否已存在
    /// </summary>
    private async Task<bool> IsStudentExistsAsync(string cardId, string classId)
    {
        // try
        // {
        //     using var scope = _serviceProvider.CreateScope();
        //     var context = scope.ServiceProvider.GetRequiredService<IDataContext>();

        //     var query = context.Query<StudentInfo>();
        //     var exists = await query.AnyAsync(x => x.Data.CardId == cardId && x.Data.ClassId == classId);
        //     return exists;
        // }
        // catch
        // {
        //     return false;
        // }
        return false;
    }

    /// <summary>
    /// 批量保存学生信息
    /// </summary>
    private async Task<List<DataModel<StudentInfo>>> SaveStudentsAsync(List<DataModel<StudentInfo>> students)
    {
        // var savedStudents = new List<DataModel<StudentInfo>>();
        
        // try
        // {
        //     using var scope = _serviceProvider.CreateScope();
        //     var context = scope.ServiceProvider.GetRequiredService<IDataContext>();
            
        //     foreach (var student in students)
        //     {
        //         context.Add(student);
        //         savedStudents.Add(student);
        //     }
            
        //     await context.SaveChangesAsync();
        // }
        // catch (Exception ex)
        // {
        //     // 记录错误日志
        //     LogTool.Logger.Error($"批量保存学生信息失败: {ex.Message}");
        //     throw;
        // }
        
        return new List<DataModel<StudentInfo>>();
    }
}
