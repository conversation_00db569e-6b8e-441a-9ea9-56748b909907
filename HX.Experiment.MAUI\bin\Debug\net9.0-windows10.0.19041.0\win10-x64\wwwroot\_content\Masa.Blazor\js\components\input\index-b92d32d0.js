import{_ as t}from"../../chunks/tslib.es6-68144fbe.js";import{p as e,a as n,b as i}from"../../chunks/EventType-63cda6c3.js";import{g as s}from"../../chunks/helper-6d386307.js";function o(t,i){let o={target:{}};return"mouse"===t?o=Object.assign(Object.assign({},o),e(i)):"touch"===t&&(o=Object.assign(Object.assign({},o),n(i))),o.target=s(i.target),o}function u(t,e,n){let i=!1;for(const s of t.composedPath()){if(s===n)break;if(a(s,e)){i=!0;break}}return i}function a(t,e){const n=["_blazorEvents_1","stopPropagationFlags",e];let i=t,s=0;for(;i[n[s]];)i=i[n[s]],s++;return s==n.length&&"boolean"==typeof i&&i}var h,r,c,l,p,d;class m{constructor(e,n,i,s){h.add(this),this.input=n,this.inputSlot=i,this.dotnetHelper=e,this.debounce=s,t(this,h,"m",r).call(this)}setValue(t){this.input.value=t}}function v(t,e,n,i){return new m(t,e,n,i)}h=new WeakSet,r=function(){this.input&&this.inputSlot&&(t(this,h,"m",p).call(this),t(this,h,"m",d).call(this),this.input&&(this.input instanceof HTMLInputElement||this.input instanceof HTMLTextAreaElement)&&t(this,h,"m",c).call(this))},c=function(){let e,n=!1;this.input.addEventListener("compositionstart",(t=>{n=!0})),this.input.addEventListener("compositionend",(t=>{n=!1;const e=i(t);e.value=this.input.value,-1!==this.input.maxLength&&e.value.length>this.input.maxLength&&(e.value=e.value.substring(0,this.input.maxLength)),this.dotnetHelper.invokeMethodAsync("OnInput",e)})),this.input.addEventListener("input",(t=>{if(!n){var s=i(t);clearTimeout(e),e=setTimeout((()=>{this.dotnetHelper.invokeMethodAsync("OnInput",s)}),this.debounce)}})),this.input.addEventListener("change",(e=>{var n=i(e);t(this,h,"m",l).call(this,e),this.dotnetHelper.invokeMethodAsync("OnChange",n)}))},l=function(t){if("number"===t.target.type){const e=t.target.value,n=t.target.valueAsNumber;e&&e!==n.toString()&&(this.input.value=isNaN(n)?"":n.toString())}},p=function(){this.inputSlot.addEventListener("click",(t=>{if(!u(t,"click",this.inputSlot)){var e=o("mouse",t);this.dotnetHelper.invokeMethodAsync("OnClick",e)}}))},d=function(){this.inputSlot.addEventListener("mouseup",(t=>{if(!u(t,"mouseup",this.inputSlot)){var e=o("mouse",t);this.dotnetHelper.invokeMethodAsync("OnMouseUp",e)}}))};export{v as init};
//# sourceMappingURL=index-b92d32d0.js.map
