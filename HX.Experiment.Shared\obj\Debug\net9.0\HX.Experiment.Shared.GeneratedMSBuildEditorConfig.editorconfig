is_global = true
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows,browser
build_property.RootNamespace = HX.Experiment.Shared
build_property.RootNamespace = HX.Experiment.Shared
build_property.ProjectDir = D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Shared\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = D:\Project\HuiXin\HX.HRV\HX.Experiment\HX.Experiment.Shared
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[D:/Project/HuiXin/HX.HRV/HX.Experiment/HX.Experiment.Shared/Pages/Eye/Calibration.razor]
build_metadata.AdditionalFiles.TargetPath = 
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRXllXENhbGlicmF0aW9uLnJhem9y
build_metadata.AdditionalFiles.CssScope = 
build_metadata.AdditionalFiles.CssScope = 

[D:/Project/HuiXin/HX.HRV/HX.Experiment/HX.Experiment.Shared/Pages/HXDevice/Detail.razor]
build_metadata.AdditionalFiles.TargetPath = 
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSFhEZXZpY2VcRGV0YWlsLnJhem9y
build_metadata.AdditionalFiles.CssScope = 
build_metadata.AdditionalFiles.CssScope = b-2hbl4rgscp

[D:/Project/HuiXin/HX.HRV/HX.Experiment/HX.Experiment.Shared/Pages/ClassManager.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcQ2xhc3NNYW5hZ2VyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Project/HuiXin/HX.HRV/HX.Experiment/HX.Experiment.Shared/Pages/DeviceSetup.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRGV2aWNlU2V0dXAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Project/HuiXin/HX.HRV/HX.Experiment/HX.Experiment.Shared/Pages/Experiment.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcRXhwZXJpbWVudC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Project/HuiXin/HX.HRV/HX.Experiment/HX.Experiment.Shared/Pages/HXDevice/StudentDeviceDialog.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSFhEZXZpY2VcU3R1ZGVudERldmljZURpYWxvZy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Project/HuiXin/HX.HRV/HX.Experiment/HX.Experiment.Shared/Pages/HXDevice/StudentTestDialog.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSFhEZXZpY2VcU3R1ZGVudFRlc3REaWFsb2cucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Project/HuiXin/HX.HRV/HX.Experiment/HX.Experiment.Shared/Pages/Login.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcTG9naW4ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Project/HuiXin/HX.HRV/HX.Experiment/HX.Experiment.Shared/Pages/Monitor.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcTW9uaXRvci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Project/HuiXin/HX.HRV/HX.Experiment/HX.Experiment.Shared/Pages/StudentExperiment.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcU3R1ZGVudEV4cGVyaW1lbnQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Project/HuiXin/HX.HRV/HX.Experiment/HX.Experiment.Shared/Pages/TaskDetail.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcVGFza0RldGFpbC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/Project/HuiXin/HX.HRV/HX.Experiment/HX.Experiment.Shared/Pages/TaskList.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcVGFza0xpc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Project/HuiXin/HX.HRV/HX.Experiment/HX.Experiment.Shared/Routes.razor]
build_metadata.AdditionalFiles.TargetPath = Um91dGVzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/Project/HuiXin/HX.HRV/HX.Experiment/HX.Experiment.Shared/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = X0ltcG9ydHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/Project/HuiXin/HX.HRV/HX.Experiment/HX.Experiment.Shared/Layout/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = TGF5b3V0XE1haW5MYXlvdXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = b-ix415v3vrn
