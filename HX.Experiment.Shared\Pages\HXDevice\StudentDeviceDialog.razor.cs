using HX.Experiment.Shared.Model;
using UFU.IoT.Models;
using UFU.CoreFX.Models;
using UFU.CoreFX.Shared.Services;
using Microsoft.AspNetCore.Components;
using Masa.Blazor;

namespace HX.Experiment.Shared.Pages.HXDevice;

public partial class StudentDeviceDialog : ComponentBase
{
    [Parameter] public StudentInfo StudentModel { get; set; } = new();
    [Parameter] public DeviceModel? DeviceModel { get; set; }
    [Parameter] public int CheckTimeValue { get; set; }
    [Parameter] public EventCallback CloseDialogEvent { get; set; }

    [Inject] public StateService StateService { get; set; } = null!;
    [Inject] public IPopupService PopupService { get; set; } = null!;

    private async Task HandleSubmit()
    {
        try
        {
            // 创建检测记录
            var testRecord = new
            {
                StudentId = StudentModel.CardId, // 使用身份证号作为学生ID
                StudentName = StudentModel.Name,
                DeviceId = DeviceModel?.Id,
                DeviceSN = DeviceModel?.DeviceSN,
                CheckTime = CheckTimeValue,
                StartTime = DateTime.Now,
                EndTime = DateTime.Now.AddMinutes(CheckTimeValue),
                Status = "检测中"
            };

            // 调用API创建检测记录
            var result = await StateService.PostAsJsonAsync<DataModel<object>>(
                "/api/v2/hx_experiment/TestRecord/Add", testRecord);

            if (result.Success)
            {
                await PopupService.EnqueueSnackbarAsync("检测已开始！", AlertTypes.Success);
                
                // 发送开始检测命令到设备
                await SendStartTestCommand();
                
                await CloseDialogEvent.InvokeAsync();
            }
            else
            {
                await PopupService.EnqueueSnackbarAsync(result.Message ?? "开始检测失败", AlertTypes.Error);
            }
        }
        catch (Exception ex)
        {
            await PopupService.EnqueueSnackbarAsync($"开始检测失败: {ex.Message}", AlertTypes.Error);
        }
        finally
        {
            PopupService.HideProgressCircular();
        }
    }

    private async Task SendStartTestCommand()
    {
        try
        {
            if (DeviceModel == null) return;

            // 发送设备查找命令，让设备显示绿色指示灯
            var findCommand = new
            {
                MsgId = 44444,
                Time = DateTimeOffset.Now.ToUnixTimeMilliseconds(),
                Device = new
                {
                    SN = DeviceModel.DeviceSN,
                    Type = DeviceModel.TypeId,
                },
                CMD = 1, // Write命令
                Data = new
                {
                    FindCode = (byte)16,
                    FindColor = 0x4CAF50 // 绿色
                }
            };

            // 这里应该通过WebSocket或其他方式发送命令到设备
            // 由于这是实验项目，可能需要根据实际的设备通信方式调整
            await StateService.PostAsJsonAsync<object>(
                "/api/v2/hx_experiment/Device/SendCommand", findCommand);

            // 延迟后发送开始检测命令
            await Task.Delay(1000);

            var startCommand = new
            {
                MsgId = 11111,
                Time = DateTimeOffset.Now.ToUnixTimeMilliseconds(),
                Device = new
                {
                    SN = DeviceModel.DeviceSN,
                    Type = DeviceModel.TypeId,
                },
                CMD = 1, // Write命令
                Data = new
                {
                    CollectMode = 1 // 连续采集模式
                }
            };

            await StateService.PostAsJsonAsync<object>(
                "/api/v2/hx_experiment/Device/SendCommand", startCommand);
        }
        catch (Exception ex)
        {
            // 设备命令发送失败不影响检测记录的创建
            Console.WriteLine($"发送设备命令失败: {ex.Message}");
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
    }
}
